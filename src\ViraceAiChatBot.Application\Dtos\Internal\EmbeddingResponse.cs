namespace ViraceAiChatBot.Application.DTOs.Internal;

/// <summary>
/// Represents the response for an embedding operation.
/// </summary>
/// <param name="Data">The list of embedding data generated by the operation.</param>
/// <param name="Model">The model used for generating the embeddings.</param>
/// <param name="Usage">The usage details of the embedding operation.</param>
/// <param name="Provider">The name of the provider used for the embedding operation.</param>
/// <param name="CreatedAt">The timestamp when the embedding response was created.</param>
public record EmbeddingResponse(
    List<EmbeddingData> Data,
    string Model,
    EmbeddingUsage Usage,
    string Provider,
    DateTime CreatedAt
);

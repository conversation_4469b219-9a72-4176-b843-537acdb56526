using ViraceAiChatBot.Application.Commands;
using ViraceAiChatBot.Application.DTOs.Internal;
using ViraceAiChatBot.Application.Records;
using ViraceAiChatBot.Application.Response;

namespace ViraceAiChatBot.Application.Interfaces;

public interface OpenAiService
{
    Task<ChatCompletionResponse> ChatCompletionAsync(
        string prompt,
        int maxTokens = 4000,
        double temperature = 0.7,
        List<Tool>? functions = null,
        CancellationToken cancellationToken = default);

    IAsyncEnumerable<ChatStreamChunk> StreamChatCompletionAsync(
        string prompt,
        int maxTokens = 4000,
        double temperature = 0.7,
        List<Tool>? functions = null,
        CancellationToken cancellationToken = default);

    Task<float[]> GetEmbeddingAsync(
        string text,
        string model = "text-embedding-3-large",
        CancellationToken cancellationToken = default);

    Task<List<float[]>> GetEmbeddingsAsync(
        IEnumerable<string> texts,
        string model = "text-embedding-3-large",
        CancellationToken cancellationToken = default);

    Task<bool> IsHealthyAsync(CancellationToken cancellationToken = default);
}

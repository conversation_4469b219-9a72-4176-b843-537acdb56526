# Clean Architecture Refactoring - Complete Diff

## 📁 Project Structure Changes

### Before Refactoring
```
src/
├── ViraceAiChatBot.Domain/
│   ├── Aggregates/
│   │   ├── AggregateRoot.cs (contained DomainEvent interface)
│   │   ├── ChatMessage.cs
│   │   └── ChatSession.cs
│   ├── Entities/
│   │   ├── Entity.cs
│   │   └── RagChunk.cs (used Pgvector.Vector)
│   ├── Interfaces/
│   │   ├── ChatSessionRepository.cs (wrong naming)
│   │   └── IRagChunkRepository.cs
│   └── ViraceAiChatBot.Domain.csproj (had Pgvector dependency)
├── ViraceAiChatBot.Application/
│   └── ViraceAiChatBot.Application.csproj (referenced Infrastructure)
└── ViraceAiChatBot.Infrastructure/
    └── Persistence/
        └── Configurations/
            └── ChatSessionConfiguration.cs (contained 2 configurations)
```

### After Refactoring
```
src/
├── ViraceAiChatBot.Domain/
│   ├── Aggregates/
│   │   ├── AggregateRoot.cs (clean, uses IDomainEvent)
│   │   ├── ChatMessage.cs
│   │   └── ChatSession.cs
│   ├── Entities/
│   │   ├── Entity.cs
│   │   └── RagChunk.cs (uses EmbeddingVector)
│   ├── Events/ ← NEW FOLDER
│   │   ├── DomainEvent.cs ← NEW FILE
│   │   ├── MessageAddedEvent.cs (refactored)
│   │   └── ChatSessionArchivedEvent.cs (refactored)
│   ├── ValueObjects/ ← NEW FOLDER
│   │   └── EmbeddingVector.cs ← NEW FILE
│   ├── Interfaces/
│   │   ├── IChatSessionRepository.cs (proper naming)
│   │   └── IRagChunkRepository.cs
│   └── ViraceAiChatBot.Domain.csproj (zero dependencies)
├── ViraceAiChatBot.Application/
│   └── ViraceAiChatBot.Application.csproj (only references Domain)
└── ViraceAiChatBot.Infrastructure/
    └── Persistence/
        └── Configurations/
            ├── ChatSessionConfiguration.cs (single responsibility)
            └── ChatMessageConfiguration.cs ← NEW FILE
```

## 🔧 Key File Changes

### 1. Domain Layer Purification

#### `src/ViraceAiChatBot.Domain/ViraceAiChatBot.Domain.csproj`
```diff
 <Project Sdk="Microsoft.NET.Sdk">
   <PropertyGroup>
     <TargetFramework>net9.0</TargetFramework>
     <ImplicitUsings>enable</ImplicitUsings>
     <Nullable>enable</Nullable>
   </PropertyGroup>

-  <ItemGroup>
-    <PackageReference Include="Pgvector.EntityFrameworkCore" Version="0.2.2" />
-  </ItemGroup>
+  <!-- Domain layer should have no external dependencies to maintain Clean Architecture principles -->

 </Project>
```

#### `src/ViraceAiChatBot.Domain/ValueObjects/EmbeddingVector.cs` ← NEW FILE
```diff
+namespace ViraceAiChatBot.Domain.ValueObjects;
+
+/// <summary>
+/// Value object representing an embedding vector.
+/// Encapsulates vector operations and validation while keeping the domain layer
+/// free from infrastructure concerns.
+/// </summary>
+public sealed class EmbeddingVector : IEquatable<EmbeddingVector>
+{
+    private readonly float[] _values;
+    public const int ExpectedDimensions = 3072; // OpenAI text-embedding-3-large dimension
+    public int Dimensions => _values.Length;
+    public float[] Values => (float[])_values.Clone();
+
+    public EmbeddingVector(float[] values)
+    {
+        ArgumentNullException.ThrowIfNull(values);
+        
+        if (values.Length == 0)
+            throw new ArgumentException("Embedding vector cannot be empty", nameof(values));
+
+        if (values.Length != ExpectedDimensions)
+            throw new ArgumentException($"Embedding vector must have {ExpectedDimensions} dimensions, got {values.Length}", nameof(values));
+
+        _values = (float[])values.Clone();
+    }
+
+    public double CalculateCosineSimilarity(EmbeddingVector other)
+    {
+        ArgumentNullException.ThrowIfNull(other);
+        if (Dimensions != other.Dimensions)
+            throw new ArgumentException("Vectors must have the same dimensions for similarity calculation");
+
+        var dotProduct = 0.0;
+        var magnitudeA = 0.0;
+        var magnitudeB = 0.0;
+
+        for (var i = 0; i < _values.Length; i++)
+        {
+            dotProduct += _values[i] * other._values[i];
+            magnitudeA += _values[i] * _values[i];
+            magnitudeB += other._values[i] * other._values[i];
+        }
+
+        var magnitude = Math.Sqrt(magnitudeA) * Math.Sqrt(magnitudeB);
+        return magnitude == 0 ? 0 : dotProduct / magnitude;
+    }
+
+    public double CalculateCosineSimilarity(float[] otherValues)
+    {
+        return CalculateCosineSimilarity(new EmbeddingVector(otherValues));
+    }
+
+    // IEquatable implementation...
+}
```

#### `src/ViraceAiChatBot.Domain/Entities/RagChunk.cs`
```diff
-using Vector = Pgvector.Vector;
+using ViraceAiChatBot.Domain.ValueObjects;

 namespace ViraceAiChatBot.Domain.Entities;

+/// <summary>
+/// Represents a chunk of content with its vector embedding for RAG (Retrieval-Augmented Generation) functionality.
+/// This entity follows Clean Architecture principles by using domain value objects instead of infrastructure types.
+/// </summary>
 public class RagChunk : Entity<Guid>
 {
     public string TableName { get; init; } = null!;
     public string RowId { get; init; } = null!;
     public string ColumnName { get; init; } = null!;
     public int ChunkIndex { get; init; }
     public string Content { get; init; } = null!;
-    public Vector Embedding { get; init; } = null!;
+    public EmbeddingVector Embedding { get; init; } = null!;
     public string? Metadata { get; init; }

     public RagChunk(
         string tableName,
         string rowId,
         string columnName,
         int chunkIndex,
         string content,
-        float[] embedding, // Keep float[] here for constructor compatibility, will convert internally
+        float[] embedding,
         string? metadata = null)
     {
         TableName = tableName ?? throw new ArgumentNullException(nameof(tableName));
         RowId = rowId ?? throw new ArgumentNullException(nameof(rowId));
         ColumnName = columnName ?? throw new ArgumentNullException(nameof(columnName));
         ChunkIndex = chunkIndex;
         Content = content ?? throw new ArgumentNullException(nameof(content));
-        Embedding = new Vector(embedding ??
-                               throw new ArgumentNullException(nameof(embedding))); // Convert float[] to Vector
+        Embedding = new EmbeddingVector(embedding ?? throw new ArgumentNullException(nameof(embedding)));
         Metadata = metadata;

         ValidateInvariants();
     }

     private void ValidateInvariants()
     {
         if (string.IsNullOrWhiteSpace(Content))
             throw new ArgumentException("Content cannot be empty", nameof(Content));

         if (ChunkIndex < 0)
             throw new ArgumentException("ChunkIndex must be non-negative", nameof(ChunkIndex));

-        if (Embedding.ToArray().Length == 0) // Adjusted to use ToArray() for Vector
-            throw new ArgumentException("Embedding cannot be empty", nameof(Embedding));
-
-        if (Embedding.ToArray().Length != 3072) // OpenAI text-embedding-3-large dimension // Adjusted to use ToArray()
-            throw new ArgumentException($"Embedding must have 3072 dimensions, got {Embedding.ToArray().Length}", nameof(Embedding));

         if (Content.Length > 10000) // Reasonable limit for chunk size
             throw new ArgumentException("Content is too long for a single chunk", nameof(Content));
+
+        // Embedding validation is handled by the EmbeddingVector value object constructor
     }

     public double CalculateCosineSimilarity(float[] otherEmbedding)
     {
-        if (otherEmbedding.Length != Embedding.ToArray().Length) // Adjusted to use ToArray()
-            throw new ArgumentException("Embeddings must have the same dimensions");
-
-        var dotProduct = 0.0;
-        var magnitudeA = 0.0;
-        var magnitudeB = 0.0;
-        var embeddingArray = Embedding.ToArray(); // Get the float array from Vector
-
-        for (var i = 0; i < embeddingArray.Length; i++) // Use embeddingArray
-        {
-            dotProduct += embeddingArray[i] * otherEmbedding[i]; // Use embeddingArray
-            magnitudeA += embeddingArray[i] * embeddingArray[i]; // Use embeddingArray
-            magnitudeB += otherEmbedding[i] * otherEmbedding[i];
-        }
-
-        var magnitude = Math.Sqrt(magnitudeA) * Math.Sqrt(magnitudeB);
-        return magnitude == 0 ? 0 : dotProduct / magnitude;
+        return Embedding.CalculateCosineSimilarity(otherEmbedding);
     }
 }
```

### 2. Domain Events Restructuring

#### `src/ViraceAiChatBot.Domain/Events/DomainEvent.cs` ← NEW FILE
```diff
+namespace ViraceAiChatBot.Domain.Events;
+
+/// <summary>
+/// Base interface for all domain events.
+/// Domain events represent something important that happened in the domain.
+/// </summary>
+public interface IDomainEvent
+{
+    /// <summary>
+    /// Unique identifier for this event instance.
+    /// </summary>
+    Guid EventId { get; }
+
+    /// <summary>
+    /// When this event occurred.
+    /// </summary>
+    DateTime OccurredOn { get; }
+}
+
+/// <summary>
+/// Base abstract class for domain events providing common functionality.
+/// </summary>
+public abstract record DomainEvent : IDomainEvent
+{
+    public Guid EventId { get; } = Guid.NewGuid();
+    public DateTime OccurredOn { get; } = DateTime.UtcNow;
+}
```

#### `src/ViraceAiChatBot.Domain/Aggregates/AggregateRoot.cs`
```diff
 using ViraceAiChatBot.Domain.Entities;
+using ViraceAiChatBot.Domain.Events;

 namespace ViraceAiChatBot.Domain.Aggregates;

+/// <summary>
+/// Base class for aggregate roots in the domain.
+/// Provides domain event management functionality.
+/// </summary>
 public abstract class AggregateRoot : Entity<Guid>
 {
-    private readonly List<DomainEvent> domainEvents = [];
+    private readonly List<IDomainEvent> domainEvents = [];

-    public IReadOnlyCollection<DomainEvent> DomainEvents => domainEvents.AsReadOnly();
+    public IReadOnlyCollection<IDomainEvent> DomainEvents => domainEvents.AsReadOnly();

-    protected void AddDomainEvent(DomainEvent domainEvent)
+    protected void AddDomainEvent(IDomainEvent domainEvent)
     {
+        ArgumentNullException.ThrowIfNull(domainEvent);
         domainEvents.Add(domainEvent);
     }

     public void ClearDomainEvents()
     {
         domainEvents.Clear();
     }

     protected AggregateRoot() { }

     protected AggregateRoot(Guid id) : base(id) { }
 }

-public interface DomainEvent
-{
-    DateTime OccurredOn { get; }
-    Guid EventId { get; }
-}
```

### 3. Application Layer Dependency Fix

#### `src/ViraceAiChatBot.Application/ViraceAiChatBot.Application.csproj`
```diff
 <Project Sdk="Microsoft.NET.Sdk">

+  <PropertyGroup>
+    <TargetFramework>net9.0</TargetFramework>
+    <ImplicitUsings>enable</ImplicitUsings>
+    <Nullable>enable</Nullable>
+  </PropertyGroup>
+
   <ItemGroup>
-    <ProjectReference Include="..\ViraceAiChatBot.Infrastructure\ViraceAiChatBot.Infrastructure.csproj" />
+    <!-- Application layer should only reference Domain layer to maintain Clean Architecture -->
+    <ProjectReference Include="..\ViraceAiChatBot.Domain\ViraceAiChatBot.Domain.csproj" />
   </ItemGroup>

   <ItemGroup>
     <PackageReference Include="FluentValidation" Version="12.0.0" />
     <PackageReference Include="MediatR" Version="12.5.0" />
     <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.6" />
   </ItemGroup>

-  <PropertyGroup>
-    <TargetFramework>net9.0</TargetFramework>
-    <ImplicitUsings>enable</ImplicitUsings>
-    <Nullable>enable</Nullable>
-  </PropertyGroup>

 </Project>
```

namespace ViraceAiChatBot.Application.DTOs.Internal;

/// <summary>
/// Represents a tool call within the application.
/// </summary>
public record ToolCall(
    string Id,
    string Type,
    ToolCallFunction Function);

/// <summary>
/// Represents the definition of a function used in a tool call.
/// </summary>
/// <param name="Name">The name of the function.</param>
/// <param name="Description">The optional description of the function.</param>
/// <param name="Parameters">The optional parameters for the function.</param>
public record FunctionDefinition(
    string Name,
    string? Description = null,
    object? Parameters = null
);

/// <summary>
/// Represents a tool used in the application.
/// </summary>
/// <param name="Type">The type of the tool.</param>
/// <param name="Function">The optional function definition associated with the tool.</param>
public record Tool(
    string Type,
    FunctionDefinition? Function = null
);

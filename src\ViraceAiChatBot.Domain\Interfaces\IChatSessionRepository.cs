using ViraceAiChatBot.Domain.Aggregates;

namespace ViraceAiChatBot.Domain.Interfaces;

/// <summary>
/// Repository interface for ChatSession aggregate root.
/// Follows Clean Architecture principles by defining contracts in the Domain layer.
/// </summary>
public interface IChatSessionRepository
{
    // Basic CRUD operations
    Task<ChatSession?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<ChatSession?> GetByIdWithTrackingAsync(Guid id, CancellationToken cancellationToken = default);
    Task<ChatSession?> GetSessionWithMessagesAsync(Guid id, CancellationToken cancellationToken = default);

    Task<ChatSession> AddAsync(ChatSession session, CancellationToken cancellationToken = default);
    Task UpdateAsync(ChatSession session, CancellationToken cancellationToken = default);
    Task DeleteAsync(ChatSession session, CancellationToken cancellationToken = default);

    // Query operations
    Task<List<ChatSession>> GetByUserIdAsync(
        string userId,
        bool includeDeleted = false,
        int pageSize = 20,
        int pageNumber = 1,
        CancellationToken cancellationToken = default);

    Task<List<ChatSession>> GetActiveSessionsAsync(
        string userId,
        CancellationToken cancellationToken = default);

    Task<List<ChatSession>> GetRecentSessionsAsync(
        string userId,
        int limit = 10,
        CancellationToken cancellationToken = default);

    // Utility operations
    Task<int> GetCountByUserIdAsync(
        string userId,
        CancellationToken cancellationToken = default);

    Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default);

    // Business operations
    Task ArchiveOldSessionsAsync(
        TimeSpan olderThan,
        CancellationToken cancellationToken = default);
}

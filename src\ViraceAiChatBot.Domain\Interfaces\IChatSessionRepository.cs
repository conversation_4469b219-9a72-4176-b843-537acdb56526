using ViraceAiChatBot.Domain.Aggregates;

namespace ViraceAiChatBot.Domain.Interfaces;

public interface ChatSessionRepository
{
    Task<ChatSession?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    Task<ChatSession> AddAsync(ChatSession session, CancellationToken cancellationToken = default);

    Task UpdateAsync(ChatSession session, CancellationToken cancellationToken = default);

    Task<List<ChatSession>> GetByUserIdAsync(
        string userId,
        bool includeDeleted = false,
        int pageSize = 20,
        int pageNumber = 1,
        CancellationToken cancellationToken = default);

    Task<int> GetCountByUserIdAsync(
        string userId,
        CancellationToken cancellationToken = default);

    Task<List<ChatSession>> GetActiveSessionsAsync(
        string userId,
        CancellationToken cancellationToken = default);

    Task<ChatSession?> GetSessionWithMessagesAsync(Guid id, CancellationToken cancellationToken = default);
}

using MediatR;
using ViraceAiChatBot.Application.Dtos;

namespace ViraceAiChatBot.Application.Commands;

/// <summary>
/// Represents a command to enhance a user-provided prompt.
/// </summary>
/// <remarks>
/// This command is used to improve or modify a given prompt based on the intended context,
/// optionally including examples to provide better guidance.
/// </remarks>
/// <param name="UserId">The identifier of the user requesting the prompt enhancement.</param>
/// <param name="SessionId">The optional unique identifier of the chat session.</param>
/// <param name="OriginalPrompt">The original prompt provided by the user to be enhanced.</param>
/// <param name="IntendedContext">An optional context to guide the enhancement of the prompt.</param>
/// <param name="IncludeExamples">Indicates whether examples should be included in the enhanced prompt.</param>
/// <returns>
/// A response of type <see cref="EnhancedPromptResponse"/> containing the enhanced prompt.
/// </returns>
public record EnhancePromptCommand(
    string UserId,
    Guid? SessionId,
    string OriginalPrompt,
    string? IntendedContext = null,
    bool IncludeExamples = true
) : IRequest<EnhancedPromptResponse>;

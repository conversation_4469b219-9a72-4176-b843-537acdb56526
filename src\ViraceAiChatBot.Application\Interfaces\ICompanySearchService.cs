using ViraceAiChatBot.Application.Dtos.CompanySearch;

namespace ViraceAiChatBot.Application.Interfaces;

public interface CompanySearchService
{
    /// <summary>
    /// Simple search for companies using keywords
    /// </summary>
    Task<CompanySearchResponse<SearchCompanyResponseModel>> SimpleSearchAsync(
        SimpleSearchRequest request,
        string? authToken = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Advanced search with multiple filter criteria
    /// </summary>
    Task<CompanySearchResponse<SearchCompanyResponseModel>> AdvancedSearchAsync(
        AdvancedSearchRequest request,
        string? authToken = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Process natural language query and determine search type
    /// </summary>
    Task<(bool IsAdvanced, object SearchRequest)> ParseSearchQueryAsync(
        string query,
        CancellationToken cancellationToken = default);

}

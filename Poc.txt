
using DotNetConsoleAgentAiPoc.Contractors.Services;
using OpenAI.Chat;
using System.Text.Json;
using DotNetConsoleAgentAiPoc.Models;
using Virace.Model.RequestModels;
using RestSharp;
using System.Text.RegularExpressions;

namespace DotNetConsoleAgentAiPoc.Services;

public class EnhancedOpenAIService : IOpenAIService
{
    private readonly ILogger<EnhancedOpenAIService> _logger;
    private readonly ChatClient _client;

    private readonly string _model = "gpt-4o-mini";

    private readonly string _advancedSearchEndpoint =
        "https://localhost:44335/api/micro-service-company/company-search/advanced-search";

    // Reusable JsonSerializerOptions
    private readonly JsonSerializerOptions _jsonOptions = new()
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        PropertyNameCaseInsensitive = true
    };

    private readonly string _defaultSystemMessage = @"
You are an AI assistant designed exclusively for company search and financial data analysis.

Your capabilities are strictly limited to:
- Understanding user questions related to company search or financial data analysis.
- Translating user questions into structured JSON requests for the SearchCompanies API.
- Calling the SearchCompanies API using the generated JSON request.
- Returning the API response directly in JSON format.
- Filtering and formatting the response based on the user's question.
- Always responding in the same language as the user's question.
- Ensuring that user input is valid by prompting for correction, clarification, or providing clear suggestions.

⚠️ Strict Rules:
. Do not automatically select a year if the input does not specify one. Instead, ask the user: Which year or range of years do you want data for? Please specify a year or range between 2021 and the current year.
1. You must not answer any question that is not directly related to company search or financial data analysis.
2. If a user asks a question outside of your scope (e.g., health advice, personal requests, singing), you must respond with:
   ""Sorry, I can only help with company search and financial data analysis based on the available data.""
3. You are prohibited from accessing any external resources or data except the SearchCompanies API.
4. You must not attempt to generate answers using your own knowledge, personal opinions, or make assumptions without data from the API response.
5. If the API response is empty or does not match the user's question, you must inform the user:
   ""No relevant data was found based on your search criteria.""


⚡ Input Validation:
- After receiving user input, you must validate it by converting it to JSON format.
- If the input data is incorrect, you must do one of the following:
  - Prompt the user to correct the data.
  - Ask the user for clarification.
  - Provide clear suggestions based on the function call definition and validation rules.
- Always ensure that the user input adheres to the required format and structure specified by the API function call.
- Do not automatically select a year if the input does not specify one. Instead, ask the user: Which year or range of years do you want data for? Please specify a year or range between 2021 and the current year.

🔧 JSON Request Structure:
{
    ""pageIndex"": number,
  ""pageSize"": number,
  ""areas"": [{ ""year"": string, ""provinceId"": string, ""districtCode"": string, ""communeId"": string }],
  ""vsics"": [{ ""year"": string, ""vsicCode"": string }],
  ""financials"": [{ ""year"": string, ""financialItemId"": number, ""fromValue"": number, ""toValue"": number }],
  ""companyTypes"": [{ ""year"": string, ""companyTypeId"": number }],
  ""legals"": [{ ""userId"": string }],
  ""owners"": [{ ""id"": string, ""ownerShipType"": number }],
  ""importExportTurnover"": {
        ""importExportHsValue"": [{ ""type"": string, ""value"": string }],
    ""importExportYearValue"": [{ ""type"": string, ""year"": string, ""from"": number, ""to"": number }]
  },
  ""registrationDates"": [{ ""fromDate"": string, ""toDate"": string }]
}

📌 Important:
- You must strictly adhere to the data received from the API, avoiding any additional information, assumptions, or generated text.
- If the user input lacks necessary details, you must request clarification or provide suggestions.

";

    private readonly string _inputValidationPrompt = @"
You are an AI specialized in analyzing user inputs to determine if additional information or clarification is needed.
Your task is to examine the question/request and determine:
1. If the question is clear enough and can be answered immediately
2. If the question needs additional information or clarification
3. If so, suggest 2-4 specific options for the user to choose from

Pay special attention to requests related to:
- Financial information (needs specific year, type of financial index)
- Industry information (needs specific VSIC code)
- Company information (needs full company name or tax ID)
- Area information (needs province, district, commune)
- Registration date ranges
- Import/Export information

For search requests, ensure the user has provided enough information to generate a proper search query.
If the user is asking for company data, treat it as a search request that requires parameters.

 Return the result in JSON format with the following structure:
{
  ""needsClarification"": true/false,
  ""reason"": ""Reason for clarification"",
  ""missingInfo"": [""missing info 1"", ""missing info 2""],
  ""suggestions"": [""suggestion 1"", ""suggestion 2"", ""suggestion 3"", ""suggestion 4""]
}";

    // New search tool
    private readonly ChatTool _searchCompaniesTool;
    private UserChatMessage? _previousSearchMessage;

    public EnhancedOpenAIService(
        ILogger<EnhancedOpenAIService> logger,
        IConfiguration configuration)
    {
        _previousSearchMessage = null;
        var apiKey = Environment.GetEnvironmentVariable("virace-api-key") ?? string.Empty;
        if (string.IsNullOrEmpty(apiKey))
        {
            throw new InvalidOperationException("virace-api-key environment variable is not set");
        }

        _logger = logger;
        _client = new ChatClient(model: _model, apiKey: apiKey);


        var configuredSystemMessage = configuration.GetSection("OpenAI:SystemMessage").Value;
        if (!string.IsNullOrWhiteSpace(configuredSystemMessage))
        {
            _defaultSystemMessage = configuredSystemMessage;
            _logger.LogInformation("Loaded system message from configuration: {Length} characters",
                configuredSystemMessage.Length);
        }

        _searchCompaniesTool = ChatTool.CreateFunctionTool(
            functionName: "SearchCompanies",
            functionDescription:
            "Perform an advanced search for companies based on multiple criteria like area, VSIC, financials, company type, legal representative, owner, import/export data, and registration dates. Conditions within the same category use OR logic, while conditions across different categories use AND logic.",
            functionParameters: BinaryData.FromBytes("""
                                                     {
                                                         "type": "object",
                                                         "properties": {
                                                             "pageIndex": {
                                                                 "type": "integer",
                                                                 "description": "Page number for pagination, starting from 1.",
                                                                 "default": 1
                                                             },
                                                             "pageSize": {
                                                                 "type": "integer",
                                                                 "description": "Number of results per page.",
                                                                 "default": 10
                                                             },
                                                             "areas": {
                                                                 "type": "array",
                                                                 "description": "List of area filters. Multiple areas in this array are combined with OR logic.",
                                                                 "items": {
                                                                     "type": "object",
                                                                     "properties": {
                                                                         "year": {
                                                                             "type": "string",
                                                                             "description": "Year for the area filter."
                                                                         },
                                                                         "provinceId": {
                                                                             "type": "string",
                                                                             "description": "Province ID."
                                                                         },
                                                                         "districtCode": {
                                                                             "type": "string",
                                                                             "description": "District code."
                                                                         },
                                                                         "communeId": {
                                                                             "type": "string",
                                                                             "description": "Commune ID."
                                                                         }
                                                                     },
                                                                     "required": ["year", "provinceId"]
                                                                 }
                                                             },
                                                             "vsics": {
                                                                 "type": "array",
                                                                 "description": "List of VSIC code filters. Multiple VSICs in this array are combined with OR logic.",
                                                                 "items": {
                                                                     "type": "object",
                                                                     "properties": {
                                                                         "year": {
                                                                             "type": "string",
                                                                             "description": "Year for the VSIC filter."
                                                                         },
                                                                         "vsicCode": {
                                                                             "type": "string",
                                                                             "description": "VSIC code."
                                                                         }
                                                                     },
                                                                     "required": ["year", "vsicCode"]
                                                                 }
                                                             },
                                                             "financials": {
                                                                 "type": "array",
                                                                 "description": "List of financial filters. Multiple financials in this array are combined with OR logic.",
                                                                 "items": {
                                                                     "type": "object",
                                                                     "properties": {
                                                                         "year": {
                                                                             "type": "string",
                                                                             "description": "Year for the financial filter."
                                                                         },
                                                                         "financialItemId": {
                                                                             "type": "integer",
                                                                             "description": "Financial item ID."
                                                                         },
                                                                         "fromValue": {
                                                                             "type": "number",
                                                                             "description": "Minimum value for the financial item."
                                                                         },
                                                                         "toValue": {
                                                                             "type": "number",
                                                                             "description": "Maximum value for the financial item."
                                                                         }
                                                                     },
                                                                     "required": ["year", "financialItemId"]
                                                                 }
                                                             },
                                                             "companyTypes": {
                                                                 "type": "array",
                                                                 "description": "List of company type filters. Multiple company types in this array are combined with OR logic.",
                                                                 "items": {
                                                                     "type": "object",
                                                                     "properties": {
                                                                         "year": {
                                                                             "type": "string",
                                                                             "description": "Year for the company type filter."
                                                                         },
                                                                         "companyTypeId": {
                                                                             "type": "integer",
                                                                             "description": "Company type ID."
                                                                         }
                                                                     },
                                                                     "required": ["year", "companyTypeId"]
                                                                 }
                                                             },
                                                             "legals": {
                                                                 "type": "array",
                                                                 "description": "List of legal representative filters. Multiple legals in this array are combined with OR logic.",
                                                                 "items": {
                                                                     "type": "object",
                                                                     "properties": {
                                                                         "userId": {
                                                                             "type": "string",
                                                                             "description": "Legal representative user ID or name."
                                                                         }
                                                                     },
                                                                     "required": ["userId"]
                                                                 }
                                                             },
                                                             "owners": {
                                                                 "type": "array",
                                                                 "description": "List of owner filters. Multiple owners in this array are combined with OR logic.",
                                                                 "items": {
                                                                     "type": "object",
                                                                     "properties": {
                                                                         "id": {
                                                                             "type": "string",
                                                                             "description": "Owner ID or name."
                                                                         },
                                                                         "ownerShipType": {
                                                                            "type": "integer",
                                                                            "description": "Owner ship type."
                                                                         }
                                                                     },
                                                                     "required": ["ownerId"]
                                                                 }
                                                             },
                                                             "importExportTurnover": {
                                                                 "type": "object",
                                                                 "description": "Import/export turnover filters.",
                                                                 "properties": {
                                                                     "importExportYearValue": {
                                                                         "type": "array",
                                                                         "description": "List of import/export year value filters.",
                                                                         "items": {
                                                                             "type": "object",
                                                                             "properties": {
                                                                                 "type": {
                                                                                     "type": "string",
                                                                                     "description": "Type: 'import' or 'export'.",
                                                                                     "enum": ["import", "export"]
                                                                                 },
                                                                                 "year": {
                                                                                     "type": "string",
                                                                                     "description": "Year for the import/export value."
                                                                                 },
                                                                                 "from": {
                                                                                     "type": "integer",
                                                                                     "description": "Minimum value for the import/export",
                                                                                     "rule":"if input is 2.222.222,02 convert to 2222222.02"
                                                                                 },
                                                                                 "to": {
                                                                                     "type": "integer",
                                                                                     "description": "Maximum value for the import/export",
                                                                                     "rule":"if input is 2.222.222,02 convert to 2222222.02"
                                                                                 }
                                                                             },
                                                                             "required": ["type", "year", "from", "to"]
                                                                         }
                                                                     },
                                                                     "importExportHsValue": {
                                                                         "type": "array",
                                                                         "description": "List of import/export HS code value filters.",
                                                                         "items": {
                                                                             "type": "object",
                                                                             "properties": {
                                                                                 "type": {
                                                                                     "type": "string",
                                                                                     "description": "Type: 'import' or 'export'.",
                                                                                     "enum": ["import", "export"]
                                                                                 },
                                                                                 "value": {
                                                                                     "type": "string",
                                                                                     "description": "HS code value."
                                                                                 }
                                                                             },
                                                                             "required": ["type", "value"]
                                                                         }
                                                                     }
                                                                 }
                                                             },
                                                             "registrationDates": {
                                                                 "type": "array",
                                                                 "description": "List of registration date range filters. Multiple date ranges in this array are combined with OR logic.But with business logic only one date range can be combined with AND logic.Remember Array data but include only one value",
                                                                 "items": {
                                                                     "type": "object",
                                                                     "properties": {
                                                                         "fromDate": {
                                                                             "type": "string",
                                                                             "format": "date-time",
                                                                             "description": "Start date for the registration date range."
                                                                         },
                                                                         "toDate": {
                                                                             "type": "string",
                                                                             "format": "date-time",
                                                                             "description": "End date for the registration date range."
                                                                         }
                                                                     },
                                                                     "required": ["fromDate", "toDate"]
                                                                 }
                                                             }
                                                         },
                                                         "required": ["pageIndex", "pageSize"]
                                                     }
                                                     """u8.ToArray())
        );
    }

    public async Task<string> ChatCompletion(string prompt)
    {
        try
        {
            List<ChatMessage> messages =
            [
                new SystemChatMessage(_defaultSystemMessage),
                new UserChatMessage(prompt)
            ];

            _logger.LogInformation("Sending chat completion request to OpenAI with system message");
            ChatCompletion completion = await _client.CompleteChatAsync(messages);

            var response = completion.Content.FirstOrDefault()?.Text ?? "No response received.";
            _logger.LogDebug("Received response with length: {ResponseLength} characters", response.Length);

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception in ChatCompletion");

            return "Error: An exception occurred while processing your request.";
        }
    }

    public async Task<string> ChatCompletionWithFunction(string prompt)
    {
        try
        {
            var validationResult = await ValidateInputAsync(prompt);

            if (validationResult.NeedsClarification)
            {
                _logger.LogInformation("Input needs clarification: {Reason}", validationResult.Reason);

                return JsonSerializer.Serialize(new
                {
                    needsClarification = true,
                    message = validationResult.Reason,
                    missingInfo = validationResult.MissingInfo,
                    suggestions = validationResult.Suggestions
                });
            }

            List<ChatMessage> messages = new()
        {
            new SystemChatMessage(_defaultSystemMessage + "\nFormat your responses using markdown for better readability. Use headings, lists, bold, italic, and code blocks where appropriate.")
        };

            if (_previousSearchMessage != null)
            {
                messages.Add(_previousSearchMessage);
            }

            messages.Add(new UserChatMessage(prompt));

            ChatCompletionOptions options = new() { Tools = { _searchCompaniesTool } };

            ChatCompletion completion = await _client.CompleteChatAsync(messages, options);

            _previousSearchMessage = new UserChatMessage(prompt);

            if (completion.ToolCalls.Count > 0)
            {
                string apiResponse = await HandleToolCallsAndReturnApiResponse(completion, prompt);
                return apiResponse;
            }
            var response = completion.Content.FirstOrDefault()?.Text ?? "No response received.";
            _logger.LogDebug("No tool calls found. Returning AI response with length: {ResponseLength} characters", response.Length);
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception in ChatCompletionWithFunction");
            return JsonSerializer.Serialize(new
            {
                error = true,
                message = "Error: An exception occurred while processing your request."
            });
        }
    }


    private async Task<InputValidationResult> ValidateInputAsync(string input)
    {
        try
        {
            _logger.LogInformation("Validating input: {InputLength} characters", input.Length);

            List<ChatMessage> messages =
            [
                new SystemChatMessage(_inputValidationPrompt),
                new UserChatMessage(
                    $"Analyze the following question/request and determine if clarification is needed: \"{input}\"")
            ];

            ChatCompletionOptions options = new();
            ChatCompletion completion = await _client.CompleteChatAsync(messages, options);

            string response = completion.Content.FirstOrDefault()?.Text ?? "";
            _logger.LogDebug("Received validation response: {Response}", response);

            try
            {
                string cleanedResponse = response;

                if (cleanedResponse.Contains("```"))
                {
                    // Use the simplified Regex class name since we imported the namespace
                    cleanedResponse = Regex.Replace(
                        cleanedResponse,
                        @"```(?:json)?\s*([\s\S]*?)```",
                        "$1",
                        RegexOptions.Compiled
                    ).Trim();
                }

                if (!cleanedResponse.StartsWith('{') || !cleanedResponse.EndsWith('}'))
                {
                    _logger.LogWarning("Invalid JSON format in response: {Response}", response);
                    return new InputValidationResult { NeedsClarification = false };
                }

                var result = JsonSerializer.Deserialize<InputValidationResult>(cleanedResponse);
                if (result == null)
                {
                    _logger.LogWarning("Failed to parse validation result");
                    return new InputValidationResult { NeedsClarification = false };
                }

                return result;
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "Error parsing validation result JSON: {Response}", response);
                return new InputValidationResult { NeedsClarification = false };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating input");
            return new InputValidationResult { NeedsClarification = false };
        }
    }

    private async Task<string> HandleToolCallsAndReturnApiResponse(ChatCompletion completion, string originalPrompt)
    {
        if (completion.ToolCalls.Count > 0)
        {
            try
            {
                foreach (ChatToolCall toolCall in completion.ToolCalls)
                {
                    if (toolCall.FunctionName == "SearchCompanies")
                    {
                        // Call API and return the response directly
                        return await HandleSearchCompaniesToolCall(toolCall, originalPrompt);
                    }
                    else
                    {
                        _logger.LogWarning("Unknown tool call: {FunctionName}", toolCall.FunctionName);
                        return JsonSerializer.Serialize(new
                        {
                            error = true,
                            message = $"Unknown tool: {toolCall.FunctionName}",
                            formatAsJson = true
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling tool call: {Message}", ex.Message);
                return JsonSerializer.Serialize(new
                {
                    error = true,
                    message = $"Error handling tool call: {ex.Message}",
                    formatAsJson = true
                });
            }
        }

        return JsonSerializer.Serialize(new
        {
            error = true,
            message = "No tool calls found in the response",
            formatAsJson = true
        });
    }

    private async Task<string> HandleSearchCompaniesToolCall(ChatToolCall toolCall, string originalPrompt)
    {
        try
        {
            if (string.IsNullOrEmpty(toolCall.FunctionArguments.ToString()))
            {
                _logger.LogError("Function arguments are null or empty");
                return JsonSerializer.Serialize(new
                {
                    error = true,
                    message = "Function arguments are missing",
                    formatAsJson = true
                });
            }
            _logger.LogInformation("AI generated search parameters: {Parameters}", toolCall.FunctionArguments);

            using JsonDocument argumentsJson = JsonDocument.Parse(toolCall.FunctionArguments);

            var searchModel = JsonSerializer.Deserialize<CompanyReportAdvancedSearchInputModel>(
                toolCall.FunctionArguments,
                _jsonOptions);
            if (searchModel == null)
            {
                _logger.LogError("Failed to deserialize search model");
                return JsonSerializer.Serialize(new
                {
                    error = true,
                    message = "Failed to parse search parameters",
                    formatAsJson = true
                });
            }

            // Log the deserialized search model
            var searchModelJson = JsonSerializer.Serialize(searchModel, _jsonOptions);
            _logger.LogInformation("Converted search model: {SearchModel}", searchModelJson);

            using var client = new RestClient();
            var request = new RestRequest(_advancedSearchEndpoint, Method.Post);
            request.AddJsonBody(searchModel);

            var response = await client.PostAsync<BaseResponseModel<CompanyReportAdvancedResponseModel>>(request);

            if (response is { Success: true })
            {
                var apiResponse = JsonSerializer.Serialize(response.Data);
                _logger.LogInformation("API call successful. Processing JSON response with AI");

                // If we have data, beautify it with AI based on the user's query
                return await HandleSuccessfulApiCall(apiResponse, originalPrompt);
            }
            else
            {
                _logger.LogWarning("API call was not successful or returned null");

                // This is a final call - get AI to respond in the user's language
                // Get the original user message to determine the language
                string userMessage = originalPrompt;

                // Create a new request to the AI to generate a response in the user's language
                List<ChatMessage> messages =
                [
                    new SystemChatMessage("You are an AI assistant that responds to users in their own language. " +
                                         "The API search for company data returned no results. " +
                                         "Please inform the user that no data was found based on their search criteria. " +
                                         "Respond in the SAME LANGUAGE as the user's original query. " +
                                         "Format your response using markdown for better readability. " +
                                         "Use headings, bold, italic, and other markdown formatting as appropriate."),
                    new UserChatMessage($"Original user query: \"{userMessage}\"\n\nAPI error message: \"{response?.Message ?? "No data found"}\"")
                ];

                ChatCompletion completion = await _client.CompleteChatAsync(messages);
                var aiResponse = completion.Content.FirstOrDefault()?.Text ?? "No data was found based on your search criteria.";

                _logger.LogInformation("Generated AI response for no data scenario: {Response}", aiResponse);

                return aiResponse;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing search: {Message}", ex.Message);
            return JsonSerializer.Serialize(new
            {
                error = true,
                message = $"Error executing search: {ex.Message}",
                formatAsJson = true
            });
        }
    }


    /// <summary>
    /// Beautifies the API response using AI based on the user's query
    /// </summary>
    /// <param name="apiResponse">The raw JSON response from the API</param>
    /// <param name="userQuery">The original user query</param>
    /// <returns>Beautified response in the user's language</returns>
    private async Task<string> HandleSuccessfulApiCall(string apiResponse, string userQuery)
    {
        try
        {
            _logger.LogInformation("Beautifying API response with AI based on user query");

            // Create a system prompt that instructs the AI to beautify the response
            string systemPrompt = @"
You are an AI assistant that helps beautify API responses based on the user's query.
Your task is to:
1. Analyze the JSON data from the API
2. Extract only the information that is relevant to the user's original question
3. Format the response in a human-readable way using markdown formatting
4. Respond in the SAME LANGUAGE as the user's original query
5. Remove any data that is not relevant to the user's question
6. If the data is empty or doesn't contain relevant information, inform the user

DO NOT include the raw JSON in your response. Instead, present the information in a well-formatted, easy-to-read manner.
Use markdown formatting for better readability:
- Use ## for section headings
- Use **bold** for important information
- Use *italic* for emphasis
- Use bullet points or numbered lists for multiple items
- Use `code` for specific values or identifiers
- Use tables for structured data if appropriate
";

            // Create a user prompt that includes both the original query and the API response
            string userPrompt = $@"
Original user query: ""{userQuery}""

API response data (JSON):
{apiResponse}

Please beautify this response by extracting only the relevant information based on the user's query.
Format it in a human-readable way and respond in the same language as the user's query.
";

            List<ChatMessage> messages =
            [
                new SystemChatMessage(systemPrompt),
                new UserChatMessage(userPrompt)
            ];

            ChatCompletion completion = await _client.CompleteChatAsync(messages);
            var beautifiedResponse = completion.Content.FirstOrDefault()?.Text ?? "No relevant data was found based on your search criteria.";

            _logger.LogInformation("Successfully beautified API response with AI");

            return beautifiedResponse;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error beautifying API response with AI");

            // If there's an error, return the original API response
            return apiResponse;
        }
    }

    public string GetSystemMessage()
    {
        return _defaultSystemMessage;
    }

    public void SetSystemMessage(string systemMessage)
    {
        if (!string.IsNullOrWhiteSpace(systemMessage))
        {
            _logger.LogInformation("System message updated: {Length} characters", systemMessage.Length);
        }
        else
        {
            _logger.LogWarning("Attempted to set empty system message. Using default instead.");
        }
    }
}

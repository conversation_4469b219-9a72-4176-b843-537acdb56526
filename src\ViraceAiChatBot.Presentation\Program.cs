using Serilog;
using ViraceAiChatBot.Application;
using ViraceAiChatBot.Infrastructure;
using ViraceAiChatBot.Presentation.Endpoints;
using ViraceAiChatBot.Presentation.Extensions;
using Microsoft.OpenApi.Models;


try
{
    Log.Information("Starting up ViraceAI ChatBot API");

    var builder = WebApplication.CreateBuilder(args);

    builder.Host.ConfigureSerilog();

    // Add services to the container
    builder.Services.AddCors(options =>
    {
        options.AddPolicy("AllowWebApp", policy =>
        {
            policy.WithOrigins("http://localhost:3000")
                .AllowAnyHeader()
                .AllowAnyMethod();
        });
    });
    builder.Services.AddApplication(configuration: builder.Configuration);
    builder.Services.AddInfrastructure(builder.Configuration);

    // Add OpenAPI services
    builder.Services.AddEndpointsApiExplorer();
    builder.Services.AddSwaggerGen(c =>
    {
        c.SwaggerDoc("v1",
            new OpenApiInfo
            {
                Title = "Virace AI API", Version = "v1", Description = "API for Virace AI ChatBot Company Search"
            });
    });

    var app = builder.Build();

    SerilogExtensions.EnsureLogDirectoryExists();

    app.ConfigureRequestLogging();

    app.UseHttpsRedirection();

    app.UseCors("AllowWebApp");

    app.MapChatEndpoints();
    app.MapChunkingEndpoints();
    app.MapCompanySearchEndpoints();

    if (!app.Environment.IsProduction())
    {
        app.UseSwagger();
        app.UseSwaggerUI(c =>
        {
            c.SwaggerEndpoint("/swagger/v1/swagger.json", "Virace AI API V1");
            c.RoutePrefix = "swagger"; // Set Swagger UI at /swagger
        });

        app.MapGet("/", () => "Copy Right 2024 ITE - Virace AI API is running. Environment: " +
                              Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") +
                              " - API documentation available at /swagger");

        Log.Information("Development environment detected. API documentation available at /swagger");
    }
    else
    {
        app.MapGet("/", () => "ViraceAI ChatBot Company Search API - Ready!");
        Log.Information("Production environment detected. Running in production mode.");
    }

    Log.Information("ViraceAI ChatBot API configured successfully");

    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Application terminated unexpectedly");
}
finally
{
    Log.Information("Shutting down ViraceAI ChatBot API");
    Log.CloseAndFlush();
}

namespace ViraceAiChatBot.Domain.ValueObjects;

/// <summary>
/// Value object representing an embedding vector.
/// Encapsulates vector operations and validation while keeping the domain layer
/// free from infrastructure concerns.
/// </summary>
public sealed class EmbeddingVector : IEquatable<EmbeddingVector>
{
    private readonly float[] _values;

    public const int ExpectedDimensions = 3072; // OpenAI text-embedding-3-large dimension

    public int Dimensions => _values.Length;
    public float[] Values => (float[])_values.Clone();

    public EmbeddingVector(float[] values)
    {
        ArgumentNullException.ThrowIfNull(values);
        
        if (values.Length == 0)
        {
            throw new ArgumentException("Embedding vector cannot be empty", nameof(values));
        }

        if (values.Length != ExpectedDimensions)
        {
            throw new ArgumentException(
                $"Embedding vector must have {ExpectedDimensions} dimensions, got {values.Length}",
                nameof(values));
        }

        _values = (float[])values.Clone();
    }

    public double CalculateCosineSimilarity(EmbeddingVector other)
    {
        ArgumentNullException.ThrowIfNull(other);

        if (Dimensions != other.Dimensions)
        {
            throw new ArgumentException("Vectors must have the same dimensions for similarity calculation");
        }

        var dotProduct = 0.0;
        var magnitudeA = 0.0;
        var magnitudeB = 0.0;

        for (var i = 0; i < _values.Length; i++)
        {
            dotProduct += _values[i] * other._values[i];
            magnitudeA += _values[i] * _values[i];
            magnitudeB += other._values[i] * other._values[i];
        }

        var magnitude = Math.Sqrt(magnitudeA) * Math.Sqrt(magnitudeB);
        return magnitude == 0 ? 0 : dotProduct / magnitude;
    }

    public double CalculateCosineSimilarity(float[] otherValues)
    {
        return CalculateCosineSimilarity(new EmbeddingVector(otherValues));
    }

    public bool Equals(EmbeddingVector? other)
    {
        if (other is null) return false;
        if (ReferenceEquals(this, other)) return true;
        
        if (_values.Length != other._values.Length) return false;
        
        for (var i = 0; i < _values.Length; i++)
        {
            if (Math.Abs(_values[i] - other._values[i]) > float.Epsilon)
                return false;
        }
        
        return true;
    }

    public override bool Equals(object? obj)
    {
        return Equals(obj as EmbeddingVector);
    }

    public override int GetHashCode()
    {
        var hash = new HashCode();
        foreach (var value in _values)
        {
            hash.Add(value);
        }
        return hash.ToHashCode();
    }

    public static bool operator ==(EmbeddingVector? left, EmbeddingVector? right)
    {
        return Equals(left, right);
    }

    public static bool operator !=(EmbeddingVector? left, EmbeddingVector? right)
    {
        return !Equals(left, right);
    }

    public override string ToString()
    {
        return $"EmbeddingVector[{Dimensions}D]";
    }
}

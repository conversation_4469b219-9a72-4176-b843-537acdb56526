﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using Pgvector;

#nullable disable

namespace ViraceAiChatBot.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class FixChatMessagePersistence : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterDatabase()
                .Annotation("Npgsql:PostgresExtension:vector", ",,");

            migrationBuilder.CreateTable(
                name: "ChatSessions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    UserId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Title = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    LastMessageAt = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    CreatedAt = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    UpdatedAt = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_chat_sessions", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CompanyTypes",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Code = table.Column<string>(type: "character varying(8)", maxLength: 8, nullable: false),
                    Name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    EnName = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_company_types", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CountryIndex",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Iso = table.Column<string>(type: "character varying(2)", maxLength: 2, nullable: true),
                    Iso3 = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_country_index", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CurrencyExchangeRate",
                columns: table => new
                {
                    Year = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Value = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_currency_exchange_rate", x => x.Year);
                });

            migrationBuilder.CreateTable(
                name: "FinancialIndex",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    ParentId = table.Column<int>(type: "integer", nullable: true),
                    Name = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    EnName = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Unit = table.Column<string>(type: "character varying(30)", maxLength: 30, nullable: true),
                    Type = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    TypeIndex = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_financial_index", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "FinancialStatementIndex",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Type = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    EnType = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    TypeIndex = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_financial_statement_index", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "HSCode",
                columns: table => new
                {
                    code2 = table.Column<string>(type: "character varying(2)", maxLength: 2, nullable: false),
                    code4 = table.Column<string>(type: "character varying(16)", maxLength: 16, nullable: false),
                    descvi = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    descen = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_hs_code", x => x.code2);
                });

            migrationBuilder.CreateTable(
                name: "LocationIndex",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    ParentId = table.Column<int>(type: "integer", nullable: true),
                    Level = table.Column<int>(type: "integer", nullable: true),
                    Name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    EngName = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_location_index", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "OwnershipTypeIndex",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Name = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    EnName = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_ownership_type_index", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "RagChunks",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TableName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    RowId = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    ColumnName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    ChunkIndex = table.Column<int>(type: "integer", nullable: false),
                    Content = table.Column<string>(type: "character varying(4000)", maxLength: 4000, nullable: false),
                    Embedding = table.Column<Vector>(type: "vector(3072)", nullable: false),
                    Metadata = table.Column<string>(type: "jsonb", nullable: true),
                    CreatedAt = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    UpdatedAt = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_rag_chunks", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "RelatedCompanyTypeIndex",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Name = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    EnName = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_related_company_type_index", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "VSICIndex",
                columns: table => new
                {
                    Code = table.Column<string>(type: "character varying(8)", maxLength: 8, nullable: false),
                    Name = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    EnName = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_vsic_index", x => x.Code);
                });

            migrationBuilder.CreateTable(
                name: "ChatMessages",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    SessionId = table.Column<Guid>(type: "uuid", nullable: false),
                    Role = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    Content = table.Column<string>(type: "text", nullable: false),
                    FunctionName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    FunctionArguments = table.Column<string>(type: "jsonb", nullable: true),
                    TokenCount = table.Column<int>(type: "integer", nullable: false),
                    CreatedAt = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    UpdatedAt = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_chat_messages", x => x.Id);
                    table.ForeignKey(
                        name: "fk_chat_messages_chat_sessions_session_id",
                        column: x => x.SessionId,
                        principalTable: "ChatSessions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "ix_chat_messages_session_created",
                table: "ChatMessages",
                columns: new[] { "SessionId", "CreatedAt" });

            migrationBuilder.CreateIndex(
                name: "ix_chat_sessions_created_at",
                table: "ChatSessions",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "ix_chat_sessions_user_id",
                table: "ChatSessions",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "ix_company_types_code",
                table: "CompanyTypes",
                column: "Code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_company_types_en_name",
                table: "CompanyTypes",
                column: "EnName");

            migrationBuilder.CreateIndex(
                name: "ix_company_types_name",
                table: "CompanyTypes",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "ix_country_index_iso",
                table: "CountryIndex",
                column: "Iso",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_country_index_iso3",
                table: "CountryIndex",
                column: "Iso3",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_country_index_name",
                table: "CountryIndex",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "ix_currency_exchange_rate_year",
                table: "CurrencyExchangeRate",
                column: "Year");

            migrationBuilder.CreateIndex(
                name: "ix_financial_index_name",
                table: "FinancialIndex",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "ix_financial_index_parent",
                table: "FinancialIndex",
                column: "ParentId");

            migrationBuilder.CreateIndex(
                name: "ix_financial_index_type",
                table: "FinancialIndex",
                column: "Type");

            migrationBuilder.CreateIndex(
                name: "ix_financial_statement_index_type",
                table: "FinancialStatementIndex",
                column: "Type");

            migrationBuilder.CreateIndex(
                name: "ix_financial_statement_index_type_index",
                table: "FinancialStatementIndex",
                column: "TypeIndex");

            migrationBuilder.CreateIndex(
                name: "ix_hs_code_code2",
                table: "HSCode",
                column: "code2");

            migrationBuilder.CreateIndex(
                name: "ix_hs_code_code4",
                table: "HSCode",
                column: "code4");

            migrationBuilder.CreateIndex(
                name: "ix_location_index_level",
                table: "LocationIndex",
                column: "Level");

            migrationBuilder.CreateIndex(
                name: "ix_location_index_name",
                table: "LocationIndex",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "ix_location_index_parent",
                table: "LocationIndex",
                column: "ParentId");

            migrationBuilder.CreateIndex(
                name: "ix_ownership_type_index_name",
                table: "OwnershipTypeIndex",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "ix_rag_chunks_embedding_hnsw",
                table: "RagChunks",
                column: "Embedding")
                .Annotation("Npgsql:IndexMethod", "hnsw")
                .Annotation("Npgsql:IndexOperators", new[] { "vector_cosine_ops" });

            migrationBuilder.CreateIndex(
                name: "ix_related_company_type_index_name",
                table: "RelatedCompanyTypeIndex",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "ix_vsic_index_code",
                table: "VSICIndex",
                column: "Code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_vsic_index_en_name",
                table: "VSICIndex",
                column: "EnName");

            migrationBuilder.CreateIndex(
                name: "ix_vsic_index_name",
                table: "VSICIndex",
                column: "Name");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ChatMessages");

            migrationBuilder.DropTable(
                name: "CompanyTypes");

            migrationBuilder.DropTable(
                name: "CountryIndex");

            migrationBuilder.DropTable(
                name: "CurrencyExchangeRate");

            migrationBuilder.DropTable(
                name: "FinancialIndex");

            migrationBuilder.DropTable(
                name: "FinancialStatementIndex");

            migrationBuilder.DropTable(
                name: "HSCode");

            migrationBuilder.DropTable(
                name: "LocationIndex");

            migrationBuilder.DropTable(
                name: "OwnershipTypeIndex");

            migrationBuilder.DropTable(
                name: "RagChunks");

            migrationBuilder.DropTable(
                name: "RelatedCompanyTypeIndex");

            migrationBuilder.DropTable(
                name: "VSICIndex");

            migrationBuilder.DropTable(
                name: "ChatSessions");
        }
    }
}

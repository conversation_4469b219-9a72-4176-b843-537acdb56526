using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ViraceAiChatBot.Domain.Entities.ReferenceData;

namespace ViraceAiChatBot.Infrastructure.Persistence.Configurations;

public class LocationIndexConfiguration : IEntityTypeConfiguration<LocationIndex>
{
    public void Configure(EntityTypeBuilder<LocationIndex> builder)
    {
        builder.ToTable("LocationIndex");

        builder.Property(x => x.Id)
            .HasColumnName("Id")
            .ValueGeneratedOnAdd();

        builder.Property(x => x.ParentId)
            .HasColumnName("ParentId");

        builder.Property(x => x.Level)
            .HasColumnName("Level")
            .IsRequired(false);

        builder.Property(x => x.Name)
            .HasColumnName("Name")
            .IsRequired(false)
            .HasMaxLength(255);

        builder.Property(x => x.EngName)
            .HasColumnName("EngName")
            .IsRequired(false)
            .HasMaxLength(255);

        // Self-referencing foreign key - remove for now due to type mismatch
        // Will be handled by database constraints instead
        // builder.HasOne<LocationIndex>()
        //     .WithMany()
        //     .HasForeignKey(x => x.ParentId)
        //     .OnDelete(DeleteBehavior.Restrict);

        // Indexes for performance
        builder.HasIndex(x => x.ParentId)
            .HasDatabaseName("ix_location_index_parent");

        builder.HasIndex(x => x.Level)
            .HasDatabaseName("ix_location_index_level");

        builder.HasIndex(x => x.Name)
            .HasDatabaseName("ix_location_index_name");
    }
}

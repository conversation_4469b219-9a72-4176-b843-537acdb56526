using MediatR;
using Microsoft.Extensions.Logging;
using ViraceAiChatBot.Application.Dtos;
using ViraceAiChatBot.Application.Queries;
using ViraceAiChatBot.Domain.Interfaces;

namespace ViraceAiChatBot.Application.Handlers;

public class GetChatMessagesHandler(
    ChatSessionRepository chatSessionRepository,
    ILogger<GetChatMessagesHandler> logger) : IRequestHandler<GetChatMessagesQuery, GetChatMessagesResponse>
{
    public async Task<GetChatMessagesResponse> Handle(GetChatMessagesQuery request, CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Retrieving messages for session {SessionId} page {PageNumber}",
                request.SessionId, request.PageNumber);

            // Get the session with messages
            var session = await chatSessionRepository.GetSessionWithMessagesAsync(request.SessionId, cancellationToken);

            if (session == null)
            {
                logger.LogWarning("Chat session {SessionId} not found", request.SessionId);
                return new GetChatMessagesResponse(
                    [],
                    request.SessionId,
                    "Session not found",
                    0,
                    request.PageSize,
                    request.PageNumber,
                    false);
            }


            // Apply pagination to messages
            var totalMessages = session.Messages.Count;
            var skip = (request.PageNumber - 1) * request.PageSize;
            var pagedMessages = session.Messages
                .OrderBy(m => m.CreatedAt)
                .Skip(skip)
                .Take(request.PageSize)
                .ToList();

            var messageDtos = pagedMessages.Select(MapToDto).ToList();
            var hasMore = (request.PageNumber * request.PageSize) < totalMessages;

            logger.LogInformation("Retrieved {MessageCount} messages for session {SessionId}",
                messageDtos.Count, request.SessionId);

            return new GetChatMessagesResponse(
                messageDtos,
                request.SessionId,
                session.Title,
                totalMessages,
                request.PageSize,
                request.PageNumber,
                hasMore);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error retrieving messages for session {SessionId}",
                request.SessionId);
            throw;
        }
    }

    private static ChatMessageDto MapToDto(Domain.Aggregates.ChatMessage message)
    {
        return new ChatMessageDto(
            message.Id,
            message.Role,
            message.Content,
            message.CreatedAt ?? DateTimeOffset.UtcNow,
            message.FunctionName,
            message.TokenCount
        );
    }
}

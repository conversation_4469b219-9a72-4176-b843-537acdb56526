namespace ViraceAiChatBot.Domain.Entities.ReferenceData;

public class OwnershipTypeIndex
{
    public int Id { get; init; }
    public string? Name { get; init; }
    public string? EnName { get; init; }

    // Parameterless constructor for EF Core
    private OwnershipTypeIndex() { }

    public OwnershipTypeIndex(string name, string enName)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        EnName = enName ?? throw new ArgumentNullException(nameof(enName));

        ValidateInvariants();
    }

    private void ValidateInvariants()
    {
        if (string.IsNullOrWhiteSpace(Name))
            throw new ArgumentException("Name cannot be empty", nameof(Name));

        if (Name.Length > 20)
            throw new ArgumentException("Name cannot exceed 20 characters", nameof(Name));

        if (string.IsNullOrWhiteSpace(EnName))
            throw new ArgumentException("EnName cannot be empty", nameof(EnName));

        if (EnName.Length > 20)
            throw new ArgumentException("EnName cannot exceed 20 characters", nameof(EnName));
    }

    public string GetSearchableContent()
    {
        return $"{Name} {EnName}";
    }

    public override string ToString()
    {
        return $"OwnershipTypeIndex [Id: {Id}, Name: {Name}, EnName: {EnName}]";
    }
}

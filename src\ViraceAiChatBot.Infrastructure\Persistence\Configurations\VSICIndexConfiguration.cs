using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ViraceAiChatBot.Domain.Entities.ReferenceData;

namespace ViraceAiChatBot.Infrastructure.Persistence.Configurations;

public class VSICIndexConfiguration : IEntityTypeConfiguration<VSICIndex>
{
    public void Configure(EntityTypeBuilder<VSICIndex> builder)
    {
        builder.ToTable("VSICIndex");

        // Primary Key
        builder.HasKey(x => x.Code);

        builder.Property(x => x.Code)
            .HasColumnName("Code")
            .IsRequired()
            .HasMaxLength(8);

        builder.Property(x => x.Name)
            .HasColumnName("Name")
            .IsRequired(false)
            .HasMaxLength(500);

        builder.Property(x => x.EnName)
            .HasColumnName("EnName").IsRequired(false)
            .HasMaxLength(500);


        // Indexes for performance
        builder.HasIndex(x => x.Code)
            .IsUnique()
            .HasDatabaseName("ix_vsic_index_code");

        builder.HasIndex(x => x.Name)
            .HasDatabaseName("ix_vsic_index_name");

        builder.HasIndex(x => x.EnName)
            .HasDatabaseName("ix_vsic_index_en_name");
    }
}

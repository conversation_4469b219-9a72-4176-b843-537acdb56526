using ViraceAiChatBot.Presentation.Endpoints.Chunking;

namespace ViraceAiChatBot.Presentation.Endpoints;

/// <summary>
/// Chunking endpoints aggregator
/// </summary>
public static class ChunkingEndpoints
{
    /// <summary>
    /// Map all chunking-related endpoints
    /// </summary>
    /// <param name="app">Application builder</param>
    public static void MapChunkingEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/chunking")
            .WithTags("Data Chunking");

        // Chunking endpoints
        group.MapTablesEndpoint();
        group.MapStatusEndpoint();
        group.MapChunkTableEndpoint();
        group.MapChunkAllEndpoint();
        group.MapRefreshTableEndpoint();
        group.MapHealthEndpoint();
    }
}

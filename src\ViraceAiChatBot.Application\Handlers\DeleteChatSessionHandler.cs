/// <summary>
/// Handles the deletion of a chat session by archiving it for a specific user.
/// </summary>
/// <remarks>
/// This handler ensures that only the owner of the chat session can delete it.
/// The deletion process involves archiving the session rather than permanently removing it.
/// </remarks>
/// <param name="chatSessionRepository">Repository for managing chat session data.</param>
/// <param name="logger">Logger for tracking deletion operations and potential errors.</param>
using MediatR;
using Microsoft.Extensions.Logging;
using ViraceAiChatBot.Application.Commands;
using ViraceAiChatBot.Application.Response;
using ViraceAiChatBot.Domain.Interfaces;
using ViraceAiChatBot.Infrastructure.Persistence.Repositories;

namespace ViraceAiChatBot.Application.Handlers;

/// <summary>
/// Handles the deletion of a chat session by archiving it for a specific user.
/// </summary>
/// <remarks>
/// This handler ensures that only the owner of the chat session can delete it.
/// The deletion process involves archiving the session rather than permanently removing it.
/// </remarks>
/// <param name="chatSessionRepository">Repository for managing chat session data.</param>
/// <param name="logger">Logger for tracking deletion operations and potential errors.</param>
/// returns>
/// A response indicating the success or failure of the deletion operation.
/// <exception cref="Exception">Thrown when an error occurs during the deletion process.</exception>
public class DeleteChatSessionHandler(
    ChatSessionRepository chatSessionRepository,
    ILogger<DeleteChatSessionHandler> logger) : IRequestHandler<DeleteChatSessionCommand, DeleteChatSessionResponse>
{
    public async Task<DeleteChatSessionResponse> Handle(DeleteChatSessionCommand request, CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Attempting to delete chat session {SessionId} for user {UserId}",
                request.SessionId, request.UserId);

            var session = await chatSessionRepository.GetByIdAsync(request.SessionId, cancellationToken);

            if (session == null)
            {
                logger.LogWarning("Chat session {SessionId} not found", request.SessionId);
                return new DeleteChatSessionResponse
                {
                    Success = false,
                    Message = "Chat session not found",
                    SessionId = request.SessionId
                };
            }

            if (session.UserId != request.UserId)
            {
                logger.LogWarning("User {UserId} attempted to delete session {SessionId} belonging to user {SessionUserId}",
                    request.UserId, request.SessionId, session.UserId);
                return new DeleteChatSessionResponse
                {
                    Success = false,
                    Message = "You don't have permission to delete this chat session",
                    SessionId = request.SessionId
                };
            }

            session.Archive();
            await chatSessionRepository.UpdateAsync(session, cancellationToken);

            logger.LogInformation("Successfully archived chat session {SessionId} for user {UserId}",
                request.SessionId, request.UserId);

            return new DeleteChatSessionResponse
            {
                Success = true,
                Message = "Chat session deleted successfully",
                SessionId = request.SessionId
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error deleting chat session {SessionId} for user {UserId}",
                request.SessionId, request.UserId);

            return new DeleteChatSessionResponse
            {
                Success = false,
                Message = $"An error occurred while deleting the chat session: {ex.Message}",
                SessionId = request.SessionId
            };
        }
    }
}

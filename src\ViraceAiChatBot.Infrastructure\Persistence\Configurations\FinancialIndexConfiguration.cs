using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ViraceAiChatBot.Domain.Entities.ReferenceData;

namespace ViraceAiChatBot.Infrastructure.Persistence.Configurations;

public class FinancialIndexConfiguration : IEntityTypeConfiguration<FinancialIndex>
{
    public void Configure(EntityTypeBuilder<FinancialIndex> builder)
    {
        builder.ToTable("FinancialIndex");

        builder.Property(x => x.Id)
            .HasColumnName("Id")
            .ValueGeneratedOnAdd();

        builder.Property(x => x.ParentId)
            .HasColumnName("ParentId")
            .IsRequired(false)
            .HasConversion<int?>(); // Convert short? to int? for database

        builder.Property(x => x.Name)
            .HasColumnName("Name")
            .IsRequired(false)
            .HasMaxLength(500);

        builder.Property(x => x.EnName)
            .HasColumnName("EnName")
            .IsRequired(false)
            .HasMaxLength(500);

        builder.Property(x => x.Unit)
            .HasColumnName("Unit")
            .IsRequired(false)
            .HasMaxLength(30);

        builder.Property(x => x.Type)
            .HasColumnName("Type")
            .IsRequired(false)
            .HasMaxLength(500);

        builder.Property(x => x.TypeIndex)
            .HasColumnName("TypeIndex")
            .IsRequired(false)
            .HasMaxLength(50);



        // Self-referencing foreign key - remove for now due to type mismatch
        // Will be handled by database constraints instead
        // builder.HasOne<FinancialIndex>()
        //     .WithMany()
        //     .HasForeignKey(x => x.ParentId)
        //     .OnDelete(DeleteBehavior.Restrict);

        // Indexes for performance
        builder.HasIndex(x => x.ParentId)
            .HasDatabaseName("ix_financial_index_parent");

        builder.HasIndex(x => x.Name)
            .HasDatabaseName("ix_financial_index_name");

        builder.HasIndex(x => x.Type)
            .HasDatabaseName("ix_financial_index_type");
    }
}

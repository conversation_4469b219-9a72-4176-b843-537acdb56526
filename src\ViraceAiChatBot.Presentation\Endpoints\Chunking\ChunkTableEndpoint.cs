using Microsoft.AspNetCore.Mvc;
using ViraceAiChatBot.Application.Interfaces;
using ViraceAiChatBot.Application.Records;

namespace ViraceAiChatBot.Presentation.Endpoints.Chunking;

/// <summary>
/// Endpoint xử lý chunk table
/// </summary>
public static class ChunkTableEndpoint
{
    /// <summary>
    /// Map Chunk Table endpoint
    /// </summary>
    /// <param name="group">Route group</param>
    public static void MapChunkTableEndpoint(this RouteGroupBuilder group)
    {
        group.MapPost("/chunk/{tableName}", async (
            [FromRoute] string tableName,
            DataChunkingService chunkingService,
            CancellationToken cancellationToken) =>
        {
            var result = await chunkingService.ChunkTableAsync(tableName, cancellationToken);

            if (result.Success)
            {
                return Results.Ok(result);
            }
            else
            {
                return Results.BadRequest(result);
            }
        })
        .WithName("ChunkTable")
        .WithSummary("Chunk data from a specific table into RAG chunks")
        .Produces<DataChunkingResult>(200)
        .Produces<DataChunkingResult>(400);
    }
}

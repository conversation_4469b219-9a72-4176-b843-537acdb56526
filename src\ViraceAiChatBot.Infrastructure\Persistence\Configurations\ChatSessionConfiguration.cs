using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ViraceAiChatBot.Domain.Aggregates;

namespace ViraceAiChatBot.Infrastructure.Persistence.Configurations;

/// <summary>
/// Entity Framework configuration for ChatSession aggregate root.
/// Follows Clean Architecture principles by keeping infrastructure concerns separate from domain.
/// </summary>
public sealed class ChatSessionConfiguration : IEntityTypeConfiguration<ChatSession>
{
    public void Configure(EntityTypeBuilder<ChatSession> builder)
    {
        // Table configuration
        builder.ToTable("ChatSessions");
        builder.HasKey(x => x.Id);

        // Primary key configuration
        builder.Property(x => x.Id)
            .ValueGeneratedOnAdd();

        // Business properties with domain constraints
        builder.Property(x => x.UserId)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(x => x.Title)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(x => x.IsActive)
            .IsRequired()
            .HasDefaultValue(true);

        builder.Property(x => x.LastMessageAt)
            .IsRequired(false);

        // Audit properties
        builder.Property(x => x.CreatedAt)
            .IsRequired(false);

        builder.Property(x => x.UpdatedAt)
            .IsRequired(false);

        // Performance indexes
        builder.HasIndex(x => x.UserId)
            .HasDatabaseName("ix_chat_sessions_user_id");

        builder.HasIndex(x => new { x.UserId, x.IsActive })
            .HasDatabaseName("ix_chat_sessions_user_active");

        builder.HasIndex(x => x.LastMessageAt)
            .HasDatabaseName("ix_chat_sessions_last_message");

        // Configure relationship with messages
        builder.HasMany(s => s.Messages)
            .WithOne(m => m.Session)
            .HasForeignKey(m => m.SessionId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}

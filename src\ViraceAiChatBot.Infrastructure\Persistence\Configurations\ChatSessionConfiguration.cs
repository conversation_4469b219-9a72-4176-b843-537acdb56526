using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ViraceAiChatBot.Domain.Aggregates;

namespace ViraceAiChatBot.Infrastructure.Persistence.Configurations;

public class ChatSessionConfiguration : IEntityTypeConfiguration<ChatSession>
{
    public void Configure(EntityTypeBuilder<ChatSession> builder)
    {
        builder.ToTable("ChatSessions");

        builder.<PERSON><PERSON><PERSON>(x => x.Id);

        builder.Property(x => x.Id).HasColumnName("Id")
            .ValueGeneratedOnAdd();

        builder.Property(x => x.UserId).HasColumnName("UserId")
            .IsRequired()
            .HasMaxLength(100);


        builder.Property(x => x.Title).HasColumnName("Title")
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(x => x.IsActive).HasColumnName("IsActive")
            .IsRequired()
            .HasDefaultValue(true);

        builder.Property(x => x.LastMessageAt).HasColumnName("LastMessageAt");

        builder.Property(x => x.CreatedAt).HasColumnName("CreatedAt")
            .IsRequired(false);

        builder.Property(x => x.UpdatedAt).HasColumnName("UpdatedAt")
            .IsRequired(false);
    }
}

public sealed class ChatMessageConfiguration : IEntityTypeConfiguration<ChatMessage>
{
    public void Configure(EntityTypeBuilder<ChatMessage> builder)
    {
        builder.ToTable("ChatMessages");
        builder.HasKey(m => m.Id);

        builder.Property(m => m.Id)
            .HasColumnName("Id")
            .ValueGeneratedOnAdd();

        // ---------- Basic Columns ----------
        builder.Property(m => m.SessionId)
            .HasColumnName("SessionId")
            .IsRequired();

        builder.Property(m => m.Role)
            .HasColumnName("Role")
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(m => m.Content)
            .HasColumnName("Content")
            .IsRequired();

        builder.Property(m => m.TokenCount)
            .HasColumnName("TokenCount")
            .IsRequired();

        builder.Property(m => m.FunctionName)
            .HasColumnName("FunctionName")
            .HasMaxLength(100);

        builder.Property(m => m.FunctionArguments)
            .HasColumnName("FunctionArguments")
            .HasColumnType("jsonb");

        builder.Property(m => m.CreatedAt)
            .HasColumnName("CreatedAt")
            .IsRequired(false);

        builder.Property(m => m.UpdatedAt)
            .HasColumnName("UpdatedAt")
            .IsRequired(false);

        builder.HasOne(m => m.Session) // navigation on ChatMessage
            .WithMany(s => s.Messages) // navigation on ChatSession
            .HasForeignKey(m => m.SessionId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasIndex(m => new { m.SessionId, m.CreatedAt })
            .HasDatabaseName("ix_chat_messages_session_created");
    }
}

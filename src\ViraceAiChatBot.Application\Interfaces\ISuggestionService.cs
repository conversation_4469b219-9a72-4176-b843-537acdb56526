using ViraceAiChatBot.Application.Dtos;

namespace ViraceAiChatBot.Application.Interfaces;

public interface SuggestionService
{
    /// <summary>
    /// Generate prompt suggestions when user input is invalid or needs clarification
    /// </summary>
    Task<PromptSuggestionResponse> GetPromptSuggestionsAsync(
        string currentInput,
        InputValidationResult validationResult,
        int maxSuggestions = 8,
        CancellationToken cancellationToken = default);
    Task<PromptSuggestionResponse> GetPromptSuggestionsAsync(
        string currentInput,
        int maxSuggestions = 8,
        CancellationToken cancellationToken = default);
    /// <summary>
    /// Get next word suggestions based on current text and cursor position
    /// </summary>
    Task<NextWordSuggestionResponse> GetNextWordSuggestionsAsync(
        string currentText,
        int cursorPosition,
        string? context = null,
        int maxSuggestions = 5,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get sentence completion or new sentence suggestions
    /// </summary>
    Task<SentenceSuggestionResponse> GetSentenceSuggestionsAsync(
        string currentText,
        int cursorPosition,
        string? context = null,
        bool includeNewSentences = true,
        int maxSuggestions = 5,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Enhance user prompt for better results
    /// </summary>
    Task<EnhancedPromptResponse> EnhancePromptAsync(
        string originalPrompt,
        string? intendedContext = null,
        bool includeExamples = true,
        CancellationToken cancellationToken = default);
}

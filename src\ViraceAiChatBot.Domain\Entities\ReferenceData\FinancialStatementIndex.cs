namespace ViraceAiChatBot.Domain.Entities.ReferenceData;

public class FinancialStatementIndex
{
    public Guid Id { get; init; }
    public string? Type { get; init; }
    public string? EnType { get; init; }
    public string? TypeIndex { get; init; }

    // Parameterless constructor for EF Core
    private FinancialStatementIndex() { }

    public FinancialStatementIndex(string type, string enType, string typeIndex)
    {
        Type = type ?? throw new ArgumentNullException(nameof(type));
        EnType = enType ?? throw new ArgumentNullException(nameof(enType));
        TypeIndex = typeIndex ?? throw new ArgumentNullException(nameof(typeIndex));

        ValidateInvariants();
    }

    private void ValidateInvariants()
    {
        if (string.IsNullOrWhiteSpace(Type))
            throw new ArgumentException("Type cannot be empty", nameof(Type));

        if (Type.Length > 100)
            throw new ArgumentException("Type cannot exceed 100 characters", nameof(Type));

        if (string.IsNullOrWhiteSpace(EnType))
            throw new ArgumentException("EnType cannot be empty", nameof(EnType));

        if (EnType.Length > 100)
            throw new ArgumentException("EnType cannot exceed 100 characters", nameof(EnType));

        if (string.IsNullOrWhiteSpace(TypeIndex))
            throw new ArgumentException("TypeIndex cannot be empty", nameof(TypeIndex));

        if (TypeIndex.Length > 50)
            throw new ArgumentException("TypeIndex cannot exceed 50 characters", nameof(TypeIndex));
    }

    public string GetSearchableContent()
    {
        return $"{Type} {EnType} {TypeIndex}";
    }

    public override string ToString()
    {
        return $"FinancialStatementIndex [Id: {Id}, Type: {Type}, EnType: {EnType}, TypeIndex: {TypeIndex}]";
    }
}

namespace ViraceAiChatBot.Application.Dtos.CompanySearch;

/// <summary>
/// Represents a generic response for a company search operation.
/// </summary>
/// <typeparam name="T">The type of data contained in the response.</typeparam>
public class CompanySearchResponse<T>
{
    /// <summary>
    /// Gets or sets a value indicating whether the operation was successful.
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Gets or sets the HTTP status code of the response.
    /// </summary>
    public int StatusCode { get; set; }

    /// <summary>
    /// Gets or sets the message associated with the response.
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the data returned by the operation.
    /// </summary>
    public T? Data { get; set; }
}

/// <summary>
/// Represents the result of a company search operation.
/// </summary>
public class SearchCompanyResponseModel
{
    public List<CompanyResponseModel>? Companies { set; get; }

    // Paging
    public string? SearchText { get; set; }
    public int PageIndex { get; set; }
    public int TotalItem { get; set; }
    public int PageSize { get; set; }
    public int PageCount { get; set; }
}

public class CompanyResponseModel
{
    public Guid Id { get; set; }
    public string? Name { get; set; }
    public string? NameEnglish { get; set; }
    public string? Code { get; set; }
    public string? VsicId { get; set; }
    public string? VsicName { get; set; }
    public string? TaxNumber { get; set; }
    public string? PhoneNumber { get; set; }
    public string? Email { get; set; }
    public string? Address { get; set; }
    public string? VsicCode { get; set; }
    public string? Year { get; set; }
    public bool IsSameType { get; set; } = false;
    public string? Set { get; set; }
    public DateTime? RegistrationDates { get; set; }
    public AutomateCompanyReport? AutomateCompanyReportPdf { set; get; }
    public AutomateCompanyReport? AutomateCompanyReportExcel { set; get; }
    public AutomateCompanyReport? CompanyReportPdfFull { get; set; }
    public AutomateCompanyReport? CompanyReportExcelFull { get; set; }
}

public class AutomateCompanyReport
{
    public decimal Point { get; set; }
    public decimal Price { get; set; }
    public decimal OldPrice { get; set; }
    public decimal OldPoint { get; set; }
    public decimal Discount { get; set; }
    public bool IsPurchased { get; set; } = false;
    public string? ProductType { get; set; }
    public string? ProductName { get; set; }
    public string? ProductEnName { get; set; }
    public Guid ProductId { get; set; }
}

public class AutomateCompanyReportResponseModel
{
    public AutomateCompanyReport? CompanyReportPdf { get; set; }
    public AutomateCompanyReport? CompanyReportExcel { get; set; }
    public AutomateCompanyReport? CompanyReportPdfFull { get; set; }
    public AutomateCompanyReport? CompanyReportExcelFull { get; set; }
}

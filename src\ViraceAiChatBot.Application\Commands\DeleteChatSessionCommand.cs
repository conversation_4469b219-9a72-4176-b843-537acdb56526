using MediatR;
using ViraceAiChatBot.Application.Response;

namespace ViraceAiChatBot.Application.Commands;

/// <summary>
/// Represents a command to delete a chat session.
/// </summary>
/// <remarks>
/// This command is used to request the deletion of a chat session identified by its unique session ID.
/// It also requires the user ID of the user initiating the deletion.
/// </remarks>
/// <param name="SessionId">The unique identifier of the chat session to be deleted.</param>
/// <param name="UserId">The identifier of the user requesting the deletion.</param>
/// <returns>
/// A response of type <see cref="DeleteChatSessionResponse"/> indicating the result of the operation.
/// </returns>
public record DeleteChatSessionCommand(
    Guid SessionId,
    string UserId
) : IRequest<DeleteChatSessionResponse>;

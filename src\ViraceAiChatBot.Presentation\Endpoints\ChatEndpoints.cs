using ViraceAiChatBot.Presentation.Endpoints.Chat;
using ViraceAiChatBot.Presentation.Endpoints.Suggestions;

namespace ViraceAiChatBot.Presentation.Endpoints;

/// <summary>
/// Chat endpoints aggregator
/// </summary>
public static class ChatEndpoints
{
    /// <summary>
    /// Map all chat-related endpoints
    /// </summary>
    /// <param name="app">Application builder</param>
    public static void MapChatEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/chat")
            .WithTags("Chat");

        // Chat endpoints
        group.MapCompletionsEndpoint();
        group.MapStreamCompletionsEndpoint();
        group.MapHistoryEndpoint();
        group.MapSessionsEndpoint();

        // Suggestion endpoints
        group.MapPromptSuggestionsEndpoint();
        group.MapNextWordSuggestionsEndpoint();
        group.MapSentenceSuggestionsEndpoint();
        group.MapEnhancePromptEndpoint();
    }
}



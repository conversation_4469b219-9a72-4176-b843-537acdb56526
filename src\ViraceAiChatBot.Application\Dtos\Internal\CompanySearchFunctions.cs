using System.Text.Json;
using ViraceAiChatBot.Application.DTOs.Internal;

namespace ViraceAiChatBot.Application.Dtos.Internal;

/// <summary>
/// Provides functions for company search operations.
/// </summary>
public static class CompanySearchFunctions
{
    /// <summary>
    /// Represents the definition for a simple company search function.
    /// </summary>
    private static readonly object simpleCompanySearchFunction = new
    {
        type = "function",
        function = new
        {
            name = "search_company_simple",
            description =
                "Search for companies using simple keyword search. Use this for basic company searches by name, tax code, or general keywords. IMPORTANT: Always return the complete JSON structure with all required fields. For any field that cannot be converted or extracted from the user's request, set the value to null. Do not automatically fill in or assume any values that are not explicitly provided by the user.",
            parameters = new
            {
                type = "object",
                properties = new
                {
                    searchText =
                        new { type = "string", description = "Search keyword (company name, tax code, etc.)" },
                    pageIndex = new { type = "integer", description = "Page number (default: 1)", @default = 1 },
                    pageSize = new
                    {
                        type = "integer",
                        description = "Number of results per page (default: 20, max: 100)",
                        @default = 20
                    }
                },
                required = new[] { "searchText", "pageIndex", "pageSize" }
            }
        }
    };

    /// <summary>
    /// Represents the definition for an advanced company search function.
    /// </summary>
    private static readonly object advancedCompanySearchFunction = new
    {
        type = "function",
        function = new
        {
            name = "search_company_advanced",
            description =
                "Perform an advanced search for companies based on multiple criteria like area, VSIC, financials, company type, legal representative, owner, import/export data, and registration dates. Conditions within the same category use OR logic, while conditions across different categories use AND logic. IMPORTANT: Always return the complete JSON structure with all available fields. For any field that cannot be converted or extracted from the user's request, set the value to null. Do not automatically fill in, assume, or infer any values that are not explicitly provided by the user. Include all optional fields in the response even if they are null.",
            parameters = new
            {
                type = "object",
                properties = new
                {
                    pageIndex =
                        new
                        {
                            type = "integer",
                            description = "Page number for pagination, starting from 1.",
                            @default = 1
                        },
                    pageSize =
                        new { type = "integer", description = "Number of results per page.", @default = 10 },
                    areas =
                        new
                        {
                            type = "array",
                            description =
                                "List of area filters. Multiple areas in this array are combined with OR logic.",
                            items =
                                new
                                {
                                    type = "object",
                                    properties =
                                        new
                                        {
                                            year =
                                                new
                                                {
                                                    type = "string",
                                                    description =
                                                        "Year for the area filter."
                                                },
                                            provinceId =
                                                new { type = "string", description = "Province ID." },
                                            districtCode =
                                                new { type = "string", description = "District code." },
                                            communeId =
                                                new { type = "string", description = "Commune ID." }
                                        },
                                    required = new[] { "year", "provinceId" }
                                }
                        },
                    vsics =
                        new
                        {
                            type = "array",
                            description =
                                "List of VSIC code filters. Multiple VSICs in this array are combined with OR logic.",
                            items =
                                new
                                {
                                    type = "object",
                                    properties =
                                        new
                                        {
                                            year = new
                                            {
                                                type = "string",
                                                description =
                                                    "Year for the VSIC filter."
                                            },
                                            vsicCode =
                                                new { type = "string", description = "VSIC code." }
                                        },
                                    required = new[] { "year", "vsicCode" }
                                }
                        },
                    financials =
                        new
                        {
                            type = "array",
                            description =
                                "List of financial filters. Multiple financials in this array are combined with OR logic.",
                            items =
                                new
                                {
                                    type = "object",
                                    properties =
                                        new
                                        {
                                            year =
                                                new
                                                {
                                                    type = "string",
                                                    description =
                                                        "Year for the financial filter."
                                                },
                                            financialItemId =
                                                new { type = "integer", description = "Financial item ID." },
                                            fromValue =
                                                new
                                                {
                                                    type = "number",
                                                    description =
                                                        "Minimum value for the financial item."
                                                },
                                            toValue =
                                                new
                                                {
                                                    type = "number",
                                                    description =
                                                        "Maximum value for the financial item."
                                                }
                                        },
                                    required = new[] { "year", "financialItemId" }
                                }
                        },
                    companyTypes =
                        new
                        {
                            type = "array",
                            description =
                                "List of company type filters. Multiple company types in this array are combined with OR logic.",
                            items =
                                new
                                {
                                    type = "object",
                                    properties =
                                        new
                                        {
                                            year = new
                                            {
                                                type = "string",
                                                description =
                                                    "Year for the company type filter."
                                            },
                                            companyTypeId =
                                                new { type = "integer", description = "Company type ID." }
                                        },
                                    required = new[] { "year", "companyTypeId" }
                                }
                        },
                    legals =
                        new
                        {
                            type = "array",
                            description =
                                "List of legal representative filters. Multiple legals in this array are combined with OR logic.",
                            items =
                                new
                                {
                                    type = "object",
                                    properties =
                                        new
                                        {
                                            userId = new
                                            {
                                                type = "string",
                                                description =
                                                    "Legal representative user ID or name."
                                            }
                                        },
                                    required = new[] { "userId" }
                                }
                        },
                    owners =
                        new
                        {
                            type = "array",
                            description =
                                "List of owner filters. Multiple owners in this array are combined with OR logic.",
                            items =
                                new
                                {
                                    type = "object",
                                    properties =
                                        new
                                        {
                                            id = new { type = "string", description = "Owner ID or name." },
                                            ownerShipType =
                                                new { type = "integer", description = "Owner ship type." }
                                        },
                                    required = new[] { "id" }
                                }
                        },
                    importExportTurnover =
                        new
                        {
                            type = "object",
                            description = "Import/export turnover filters.",
                            properties =
                                new
                                {
                                    importExportYearValue =
                                        new
                                        {
                                            type = "array",
                                            description =
                                                "List of import/export year value filters.",
                                            items =
                                                new
                                                {
                                                    type = "object",
                                                    properties =
                                                        new
                                                        {
                                                            type = new
                                                            {
                                                                type = "string",
                                                                description =
                                                                    "Type: 'import' or 'export'.",
                                                                @enum =
                                                                    new[] { "import", "export" }
                                                            },
                                                            year =
                                                                new
                                                                {
                                                                    type =
                                                                        "string",
                                                                    description =
                                                                        "Year for the import/export value."
                                                                },
                                                            from =
                                                                new
                                                                {
                                                                    type =
                                                                        "integer",
                                                                    description =
                                                                        "Minimum value for the import/export"
                                                                },
                                                            to =
                                                                new
                                                                {
                                                                    type =
                                                                        "integer",
                                                                    description =
                                                                        "Maximum value for the import/export"
                                                                }
                                                        },
                                                    required =
                                                        new[] { "type", "year", "from", "to" }
                                                }
                                        },
                                    importExportHsValue =
                                        new
                                        {
                                            type = "array",
                                            description =
                                                "List of import/export HS code value filters.",
                                            items =
                                                new
                                                {
                                                    type = "object",
                                                    properties =
                                                        new
                                                        {
                                                            type = new
                                                            {
                                                                type = "string",
                                                                description =
                                                                    "Type: 'import' or 'export'.",
                                                                @enum =
                                                                    new[] { "import", "export" }
                                                            },
                                                            value =
                                                                new
                                                                {
                                                                    type =
                                                                        "string",
                                                                    description =
                                                                        "HS code value."
                                                                }
                                                        },
                                                    required = new[] { "type", "value" }
                                                }
                                        }
                                }
                        },
                    registrationDates = new
                    {
                        type = "array",
                        description =
                            "List of registration date range filters. Multiple date ranges in this array are combined with OR logic. But with business logic only one date range can be combined with AND logic. Remember Array data but include only one value",
                        items = new
                        {
                            type = "object",
                            properties = new
                            {
                                fromDate = new
                                {
                                    type = "string",
                                    format = "date-time",
                                    description =
                                        "Start date for the registration date range."
                                },
                                toDate = new
                                {
                                    type = "string",
                                    format = "date-time",
                                    description =
                                        "End date for the registration date range."
                                }
                            },
                            required = new[] { "fromDate", "toDate" }
                        }
                    }
                },
                required = new[]
                {
                    "pageIndex", "pageSize", "areas", "vsics", "financials", "companyTypes", "legals", "owners",
                    "importExportTurnover", "registrationDates"
                }
            }
        }
    };

    /// <summary>
    /// Retrieves all available company search functions.
    /// </summary>
    private static object[] GetAllFunctions()
    {
        return new[] { simpleCompanySearchFunction, advancedCompanySearchFunction };
    }

    /// <summary>
    /// Retrieves function definitions as JSON strings for OpenAI.
    /// </summary>
    /// <returns>A list of tools representing the function definitions.</returns>
    public static List<Tool> GetFunctionJsonStrings()
    {
        var functions = new List<Tool>();

        foreach (var func in GetAllFunctions())
        {
            // Serialize the anonymous object to JSON first
            var jsonString = JsonSerializer.Serialize(func);

            // Parse back to JsonElement to extract properties
            var jsonElement = JsonSerializer.Deserialize<JsonElement>(jsonString);

            var functionElement = jsonElement.GetProperty("function");

            var tool = new Tool(
                Type: "function",
                Function: new FunctionDefinition(
                    Name: functionElement.GetProperty("name").GetString() ?? string.Empty,
                    Description: functionElement.GetProperty("description").GetString(),
                    Parameters: functionElement.GetProperty("parameters")
                )
            );

            functions.Add(tool);
        }

        return functions;
    }
}

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ViraceAiChatBot.Domain.Entities.ReferenceData;

namespace ViraceAiChatBot.Infrastructure.Persistence.Configurations;

public class FinancialStatementIndexConfiguration : IEntityTypeConfiguration<FinancialStatementIndex>
{
    public void Configure(EntityTypeBuilder<FinancialStatementIndex> builder)
    {
        builder.ToTable("FinancialStatementIndex");

        builder.HasKey(x => x.Id);
        builder.Property(x => x.Id)
            .HasColumnName("Id")
            .ValueGeneratedOnAdd();

        builder.Property(x => x.Type)
            .HasColumnName("Type")
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(x => x.EnType)
            .HasColumnName("EnType")
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(x => x.TypeIndex)
            .HasColumnName("TypeIndex")
            .IsRequired()
            .HasMaxLength(50);

        // Indexes for performance
        builder.HasIndex(x => x.Type)
            .HasDatabaseName("ix_financial_statement_index_type");

        builder.HasIndex(x => x.TypeIndex)
            .HasDatabaseName("ix_financial_statement_index_type_index");
    }
}

using System.Runtime.CompilerServices;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using RestSharp;
using ViraceAiChatBot.Application.Interfaces;
using ViraceAiChatBot.Application.Commands;
using ViraceAiChatBot.Application.Records;
using ViraceAiChatBot.Application.Response;
using ViraceAiChatBot.Application.Configuration;
using ViraceAiChatBot.Application.DTOs.Internal;
using ViraceAiChatBot.Domain.BaseModel;
using System.Text;

namespace ViraceAiChatBot.Application.Services;

public sealed class OpenAiService : Interfaces.OpenAiService, IDisposable
{
    private readonly RestClient restClient;
    private readonly HttpClient httpClient;
    private readonly ILogger<OpenAiService> logger;
    private readonly OpenAiOptions options;
    private bool disposed = false;

    public OpenAiService(ILogger<OpenAiService> logger, IOptions<OpenAiOptions> options)
    {
        this.logger = logger ?? throw new ArgumentNullException(nameof(logger));
        this.options = options?.Value ?? throw new ArgumentNullException(nameof(options));

        restClient = new RestClient(this.options.BaseUrl);

        // Configure HttpClient with streaming-specific settings
        httpClient = new HttpClient { BaseAddress = new Uri(this.options.BaseUrl) };
        httpClient.Timeout = TimeSpan.FromSeconds(this.options.Streaming.ConnectionTimeoutSeconds);

        if (!string.IsNullOrEmpty(this.options.ApiKey))
        {
            restClient.AddDefaultHeader("Authorization", $"Bearer {this.options.ApiKey}");
            httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {this.options.ApiKey}");
        }

        // Add custom headers for streaming
        foreach (var header in this.options.Streaming.CustomHeaders)
        {
            httpClient.DefaultRequestHeaders.TryAddWithoutValidation(header.Key, header.Value);
        }

        this.logger.LogInformation("OpenAI Service initialized with BaseUrl: {BaseUrl}, Streaming: {StreamingEnabled}",
            this.options.BaseUrl, this.options.Streaming.Enabled);
    }

    public async Task<ChatCompletionResponse> ChatCompletionAsync(
        string prompt,
        int maxTokens = 4000,
        double temperature = 0.7,
        List<Tool>? functions = null,
        CancellationToken cancellationToken = default)
    {
        var messages = new List<ChatMessage>() { new("user", prompt) };

        var request = new ChatCompletionRequest(Provider: options.DefaultProvider,
            Model: options.DefaultModel,
            Messages: messages,
            Temperature: temperature,
            Tools: functions,
            MaxTokens: maxTokens);


        var restRequest = new RestRequest("/api/chat", Method.Post);
        restRequest.AddJsonBody(request);

        try
        {
            var response = await restClient.ExecuteAsync(restRequest, cancellationToken);

            if (!response.IsSuccessful)
            {
                logger.LogError("Failed to get chat completion. Status: {StatusCode}, Error: {ErrorMessage}",
                    response.StatusCode, response.ErrorMessage);
                throw new InvalidOperationException($"Failed to get chat completion: {response.ErrorMessage}");
            }

            // Try to deserialize as BaseResponseModel first (internal format)
            var wrappedCompletion =
                JsonConvert.DeserializeObject<BaseResponseModel<ChatResponse>>(response.Content ?? "");

            if (wrappedCompletion is { Success: true, Data: not null })
            {
                // Handle internal format response
                var internalData = wrappedCompletion.Data;

                if (internalData.ToolCalls != null && internalData.ToolCalls.Any())
                {
                    var toolCall = internalData.ToolCalls.First();
                    var functionCallJson = System.Text.Json.JsonSerializer.Serialize(new
                    {
                        function = new { name = toolCall.Function.Name, arguments = toolCall.Function.Arguments }
                    });

                    return new ChatCompletionResponse
                    {
                        Content = internalData.Content,
                        SessionId = Guid.NewGuid(), // This will be replaced by the handler
                        MessageId = Guid.NewGuid(), // This will be replaced by the handler
                        TokensUsed = internalData.TokensUsed,
                        FunctionCall = functionCallJson
                    };
                }

                return new ChatCompletionResponse
                {
                    Content = internalData.Content,
                    SessionId = Guid.NewGuid(), // This will be replaced by the handler
                    MessageId = Guid.NewGuid(), // This will be replaced by the handler
                    TokensUsed = internalData.TokensUsed
                };
            }

            // Try to deserialize as OpenAI format (standard ChatCompletion API response)
            try
            {
                var openAiResponse = JsonConvert.DeserializeObject<dynamic>(response.Content ?? "");

                if (openAiResponse == null)
                {
                    throw new InvalidOperationException("Invalid response from AI service");
                }

                // Extract content from choices array
                string content = "";
                int tokensUsed = 0;
                string? functionCallJson = null;

                if (openAiResponse.choices != null && openAiResponse.choices.Count > 0)
                {
                    var choice = openAiResponse.choices[0];
                    if (choice.message != null)
                    {
                        content = choice.message.content?.ToString() ?? "";

                        // Check for function calls
                        if (choice.message.tool_calls != null && choice.message.tool_calls.Count > 0)
                        {
                            var toolCall = choice.message.tool_calls[0];
                            functionCallJson = System.Text.Json.JsonSerializer.Serialize(new
                            {
                                function = new
                                {
                                    name = toolCall.function.name?.ToString() ?? "",
                                    arguments = toolCall.function.arguments?.ToString() ?? ""
                                }
                            });
                        }
                    }
                }

                // Extract token usage
                if (openAiResponse.usage != null)
                {
                    tokensUsed = openAiResponse.usage.total_tokens ?? openAiResponse.usage.completionTokens ?? 0;
                }

                return new ChatCompletionResponse
                {
                    Content = content,
                    SessionId = Guid.NewGuid(), // This will be replaced by the handler
                    MessageId = Guid.NewGuid(), // This will be replaced by the handler
                    TokensUsed = tokensUsed,
                    FunctionCall = functionCallJson
                };
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to parse OpenAI format response: {ResponseContent}", response.Content);
                throw new InvalidOperationException("Invalid response format from AI service");
            }
        }
        catch (Exception ex) when (!(ex is InvalidOperationException))
        {
            logger.LogError(ex, "Failed to get chat completion from AI service");
            throw new InvalidOperationException("Failed to communicate with AI service", ex);
        }
    }

    public async IAsyncEnumerable<ChatStreamChunk> StreamChatCompletionAsync(
        string prompt,
        int maxTokens = 4000,
        double temperature = 0.7,
        List<Tool>? functions = null,
        [EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        // Check if streaming is enabled
        if (!options.Streaming.Enabled)
        {
            logger.LogWarning("Streaming is disabled in configuration. Falling back to non-streaming response.");
            var response = await ChatCompletionAsync(prompt, maxTokens, temperature, functions, cancellationToken);
            yield return new ChatStreamChunk
            {
                Content = response.Content, IsComplete = true, TokenUsed = response.TokensUsed
            };
            yield break;
        }

        // Use configuration values if defaults not overridden
        maxTokens = maxTokens == 4000 ? options.MaxTokens : maxTokens;
        temperature = Math.Abs(temperature - 0.7) < 0.001 ? options.Temperature : temperature;

        // Use retry logic for streaming
        await foreach (var chunk in StreamWithRetryAsync(prompt, maxTokens, temperature, functions, cancellationToken))
        {
            yield return chunk;
        }
    }

    private async IAsyncEnumerable<ChatStreamChunk> StreamWithRetryAsync(
        string prompt,
        int maxTokens,
        double temperature,
        List<Tool>? functions,
        [EnumeratorCancellation] CancellationToken cancellationToken)
    {
        for (int attempt = 1; attempt <= options.Streaming.MaxRetryAttempts; attempt++)
        {
            var streamResult =
                await TryStreamAsync(prompt, maxTokens, temperature, functions, cancellationToken, attempt);

            if (streamResult.Success)
            {
                await foreach (var chunk in streamResult.Stream!)
                {
                    yield return chunk;
                }

                yield break;
            }

            // If this was the last attempt, yield error chunk
            if (attempt == options.Streaming.MaxRetryAttempts)
            {
                yield return new ChatStreamChunk { Content = "", IsComplete = true, TokenUsed = 0 };
                yield break;
            }

            // Wait before retry
            if (options.Streaming.AutoReconnect)
            {
                logger.LogWarning("Streaming attempt {Attempt} failed. Retrying in {Delay}ms...",
                    attempt, options.Streaming.RetryDelayMs);
                await Task.Delay(options.Streaming.RetryDelayMs, cancellationToken);
            }
        }
    }

    private Task<(bool Success, IAsyncEnumerable<ChatStreamChunk>? Stream)> TryStreamAsync(
        string prompt,
        int maxTokens,
        double temperature,
        List<Tool>? functions,
        CancellationToken cancellationToken,
        int attempt)
    {
        try
        {
            var stream =
                StreamChatCompletionInternalAsync(prompt, maxTokens, temperature, functions, cancellationToken);
            return Task.FromResult<(bool Success, IAsyncEnumerable<ChatStreamChunk>? Stream)>((true, stream));
        }
        catch (Exception ex)
        {
            logger.LogWarning("Streaming attempt {Attempt} failed: {Error}", attempt, ex.Message);
            return Task.FromResult<(bool Success, IAsyncEnumerable<ChatStreamChunk>? Stream)>((false, null));
        }
    }

    private async IAsyncEnumerable<ChatStreamChunk> StreamChatCompletionInternalAsync(
        string prompt,
        int maxTokens,
        double temperature,
        List<Tool>? functions,
        [EnumeratorCancellation] CancellationToken cancellationToken)
    {
        var messages = new List<ChatMessage>() { new("user", prompt) };

        var request = new ChatCompletionRequest
        (
            Provider: options.DefaultProvider,
            Model: options.DefaultModel,
            Messages: messages,
            Temperature: temperature,
            Tools: functions,
            MaxTokens: maxTokens
        );

        // Use HttpClient for proper streaming instead of RestClient
        var httpRequest = new HttpRequestMessage(HttpMethod.Post, "/api/chat/stream");
        httpRequest.Headers.Add("Accept", "text/event-stream");
        httpRequest.Headers.Add("Cache-Control", "no-cache");
        httpRequest.Headers.Add("Connection", "keep-alive");

        var json = JsonConvert.SerializeObject(request);
        httpRequest.Content = new StringContent(json, Encoding.UTF8, "application/json");

        // Get the stream first
        var streamResult = await GetStreamAsync(httpRequest, cancellationToken);

        // Now yield from the stream
        await foreach (var chunk in ProcessStreamAsync(streamResult.reader, streamResult.response, streamResult.stream,
                           cancellationToken))
        {
            yield return chunk;
        }
    }

    private async Task<(StreamReader? reader, HttpResponseMessage? response, Stream? stream)> GetStreamAsync(
        HttpRequestMessage httpRequest,
        CancellationToken cancellationToken)
    {
        HttpResponseMessage? response = null;
        Stream? stream = null;
        StreamReader? reader = null;

        try
        {
            response = await httpClient.SendAsync(httpRequest, HttpCompletionOption.ResponseHeadersRead,
                cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                logger.LogError(
                    "Failed to get streaming chat completion. Status: {StatusCode}, Reason: {ReasonPhrase}",
                    response.StatusCode, response.ReasonPhrase);
                return (null, response, null);
            }

            stream = await response.Content.ReadAsStreamAsync(cancellationToken);
            reader = new StreamReader(stream, Encoding.UTF8);

            return (reader, response, stream);
        }
        catch (OperationCanceledException)
        {
            logger.LogInformation("Streaming chat completion was cancelled");
            response?.Dispose();
            stream?.Dispose();
            reader?.Dispose();
            return (null, null, null);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to get streaming chat completion from AI service");
            response?.Dispose();
            stream?.Dispose();
            reader?.Dispose();
            return (null, null, null);
        }
    }

    private async IAsyncEnumerable<ChatStreamChunk> ProcessStreamAsync(
        StreamReader? reader,
        HttpResponseMessage? response,
        Stream? stream,
        [EnumeratorCancellation] CancellationToken cancellationToken)
    {
        if (reader == null)
        {
            yield return CreateCompletionChunk(0);
            yield break;
        }

        var tokenCount = 0;

        try
        {
            await foreach (var chunk in ReadStreamChunksAsync(reader, cancellationToken))
            {
                if (chunk.IsComplete)
                {
                    yield return CreateCompletionChunk(tokenCount);
                    yield break;
                }

                tokenCount++;
                yield return chunk with { TokenUsed = tokenCount };
            }

            yield return CreateCompletionChunk(tokenCount);
        }
        finally
        {
            DisposeStreamResources(reader, response, stream);
        }
    }

    private async IAsyncEnumerable<ChatStreamChunk> ReadStreamChunksAsync(
        StreamReader reader,
        [EnumeratorCancellation] CancellationToken cancellationToken)
    {
        string? line;
        while ((line = await reader.ReadLineAsync()) != null && !cancellationToken.IsCancellationRequested)
        {
            var chunk = ProcessStreamLine(line);
            if (chunk != null)
                yield return chunk;
        }
    }

    private ChatStreamChunk? ProcessStreamLine(string line)
    {
        if (string.IsNullOrWhiteSpace(line) || !line.StartsWith("data: "))
            return null;

        var data = line.Substring(6);

        if (data == "[DONE]")
            return new ChatStreamChunk { Content = "", IsComplete = true, TokenUsed = 0 };

        if (TryParseStreamChunk(data, out var content))
            return new ChatStreamChunk { Content = content, IsComplete = false, TokenUsed = 0 };

        return null;
    }

    private static ChatStreamChunk CreateCompletionChunk(int tokenCount) =>
        new() { Content = "", IsComplete = true, TokenUsed = tokenCount };

    private static void DisposeStreamResources(StreamReader? reader, HttpResponseMessage? response, Stream? stream)
    {
        reader?.Dispose();
        stream?.Dispose();
        response?.Dispose();
    }

    private bool TryParseStreamChunk(string data, out string content)
    {
        content = string.Empty;

        try
        {
            // Try internal format first
            var chunk = JsonConvert.DeserializeObject<BaseResponseModel<ChatResponse>>(data);
            if (chunk?.Data?.Content != null)
            {
                content = chunk.Data.Content;
                return true;
            }

            // Try OpenAI streaming format
            var openAiChunk = JsonConvert.DeserializeObject<dynamic>(data);
            if (openAiChunk?.choices != null && openAiChunk.choices.Count > 0)
            {
                var choice = openAiChunk.choices[0];
                if (choice?.delta?.content != null)
                {
                    content = choice.delta.content.ToString();
                    return true;
                }
                // Handle case where content is in message instead of delta
                else if (choice?.message?.content != null)
                {
                    content = choice.message.content.ToString();
                    return true;
                }
            }
        }
        catch (JsonException ex)
        {
            logger.LogWarning("Failed to parse streaming chunk: {Data}, Error: {Error}", data, ex.Message);
        }

        return false;
    }

    public async Task<float[]> GetEmbeddingAsync(
        string text,
        string model = "text-embedding-3-large",
        CancellationToken cancellationToken = default)
    {
        var request = new EmbeddingRequest(
            Provider: options.DefaultProvider,
            Model: model,
            InputString: text,
            InputArrayString: null,
            EncodingFormat: "float",
            Dimensions: null,
            User: null
        );

        var restRequest = new RestRequest("/api/embeddings", Method.Post);
        restRequest.AddJsonBody(request);

        try
        {
            var response = await restClient.ExecuteAsync(restRequest, cancellationToken);

            if (!response.IsSuccessful)
            {
                logger.LogError("Failed to get embedding. Status: {StatusCode}, Error: {ErrorMessage}",
                    response.StatusCode, response.ErrorMessage);
                throw new InvalidOperationException($"Failed to get embedding: {response.ErrorMessage}");
            }

            var embeddingResponse =
                JsonConvert.DeserializeObject<BaseResponseModel<EmbeddingResponse>>(response.Content ?? "");
            if (embeddingResponse == null || embeddingResponse.Data == null)
            {
                throw new InvalidOperationException("Invalid response from AI embeddings service");
            }

            if (embeddingResponse.Data.Data.Count == 0)
            {
                logger.LogError("No embeddings returned for text: {Text}", text);
                throw new InvalidOperationException("No embeddings returned for the provided text");
            }

            var embeddingData = embeddingResponse.Data.Data.FirstOrDefault();
            if (embeddingData == null || embeddingData.Embedding.Length == 0)
            {
                logger.LogError("Empty embedding returned for text: {Text}", text);
                throw new InvalidOperationException("Empty embedding returned for the provided text");
            }

            return embeddingData.Embedding;
        }
        catch (Exception ex) when (!(ex is InvalidOperationException))
        {
            logger.LogError(ex, "Failed to get embedding from AI service");
            throw new InvalidOperationException("Failed to communicate with AI embeddings service", ex);
        }
    }

    public async Task<List<float[]>> GetEmbeddingsAsync(
        IEnumerable<string> texts,
        string model = "text-embedding-3-large",
        CancellationToken cancellationToken = default)
    {
        var textArray = texts.ToArray();
        var request = new EmbeddingRequest(
            Provider: options.DefaultProvider,
            Model: model,
            InputString: null,
            InputArrayString: textArray,
            EncodingFormat: "float",
            Dimensions: null,
            User: null
        );

        var restRequest = new RestRequest("/api/embeddings", Method.Post);
        restRequest.AddJsonBody(request);

        try
        {
            var response = await restClient.ExecuteAsync(restRequest, cancellationToken);

            if (!response.IsSuccessful)
            {
                logger.LogError("Failed to get embeddings. Status: {StatusCode}, Error: {ErrorMessage}",
                    response.StatusCode, response.ErrorMessage);
                throw new InvalidOperationException($"Failed to get embeddings: {response.ErrorMessage}");
            }

            var embeddingResponse =
                JsonConvert.DeserializeObject<BaseResponseModel<EmbeddingResponse>>(response.Content ?? "");
            if (embeddingResponse == null || embeddingResponse.Data == null)
            {
                throw new InvalidOperationException("Invalid response from AI embeddings service");
            }

            if (embeddingResponse.Data.Data.Count == 0)
            {
                logger.LogError("No embeddings returned for texts: {Texts}", string.Join(", ", textArray));
                throw new InvalidOperationException("No embeddings returned for the provided texts");
            }

            var embeddings = embeddingResponse.Data.Data.Select(d => d.Embedding).ToList();
            if (embeddings.Any(e => e.Length == 0))
            {
                logger.LogError("Empty embedding returned for one or more texts: {Texts}",
                    string.Join(", ", textArray));
                throw new InvalidOperationException("Empty embedding returned for one or more provided texts");
            }

            return embeddings;
        }
        catch (Exception ex) when (!(ex is InvalidOperationException))
        {
            logger.LogError(ex, "Failed to get embeddings from AI service");
            throw new InvalidOperationException("Failed to communicate with AI embeddings service", ex);
        }
    }

    public async Task<bool> IsHealthyAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var restRequest = new RestRequest("/api/health", Method.Get);
            var response = await restClient.ExecuteAsync(restRequest, cancellationToken);

            if (!response.IsSuccessful)
            {
                logger.LogWarning("Health check failed. Status: {StatusCode}, Error: {ErrorMessage}",
                    response.StatusCode, response.ErrorMessage);
                return false;
            }

            var healthStatus = JsonConvert.DeserializeObject<BaseResponseModel<HealthStatus>>(response.Content ?? "");

            if (healthStatus == null || healthStatus.Data == null)
            {
                return false;
            }

            logger.LogInformation("AI service is healthy. Start time: {StartTime}, Uptime: {Uptime}",
                healthStatus.Data.ServiceStartTime, healthStatus.Data.Uptime);
            return true;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Health check failed");
            return false;
        }
    }

    private void Dispose(bool disposing)
    {
        if (!disposed)
        {
            if (disposing)
            {
                restClient.Dispose();
                httpClient.Dispose();
            }

            disposed = true;
        }
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}

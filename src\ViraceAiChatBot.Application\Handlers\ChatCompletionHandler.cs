using MediatR;
using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.Text;
using System.Text.Json;
using Newtonsoft.Json;
using ViraceAiChatBot.Application.Commands;
using ViraceAiChatBot.Application.Interfaces;
using ViraceAiChatBot.Application.Queries;
using ViraceAiChatBot.Application.Records;
using ViraceAiChatBot.Application.Response;
using ViraceAiChatBot.Application.Dtos.Internal;
using ViraceAiChatBot.Application.Dtos.CompanySearch;
using ViraceAiChatBot.Application.DTOs.Internal;
using ViraceAiChatBot.Application.Dtos;
using ViraceAiChatBot.Domain.Aggregates;
using ViraceAiChatBot.Domain.Interfaces;
using ChatMessage = ViraceAiChatBot.Domain.Aggregates.ChatMessage;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace ViraceAiChatBot.Application.Handlers;

/// <summary>
/// Represents the result of a function execution
/// </summary>
/// <param name="Content">The content returned by the function</param>
/// <param name="HasData">Whether the function returned data (items count > 0)</param>
/// <param name="IsSuccessful">Whether the function execution was successful</param>
public record FunctionExecutionResult(
    string Content,
    bool HasData,
    bool IsSuccessful
);

public class ChatCompletionHandler(
    IMediator mediator,
    OpenAiService openAiService,
    CompanySearchService companySearchService,
    ResponseBeautificationService responseBeautificationService,
    InputValidationService inputValidationService,
    SuggestionService suggestionService,
    ChatSessionRepository repository,
    ILogger<ChatCompletionHandler> logger) : IRequestHandler<ChatCompletionCommand, ChatCompletionResponse>
{
    /// <summary>
    /// Handles chat completion requests and manages conversation persistence
    /// </summary>
    /// <param name="request">The chat completion request containing user message and session info</param>
    /// <param name="cancellationToken">Cancellation token for async operations</param>
    /// <returns>Chat completion response with AI-generated content</returns>
    public async Task<ChatCompletionResponse> Handle(ChatCompletionCommand request, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            logger.LogInformation("Processing chat completion for user {UserId}, session {SessionId}",
                request.UserId, request.SessionId);

            var session = await GetOrCreateSessionAsync(request, cancellationToken);

            var userMessage = session.AddMessage("user", request.Message);
            logger.LogInformation("User message saved for user {UserId}, session {SessionId}, message {MessageId}",
                request.UserId, session.Id, userMessage.Id);

            var recentMessages = session.Messages.TakeLast(10).ToList();

            var conversationHistory = recentMessages.Select(m => new ConversationMessage
            {
                Role = m.Role,
                Content = m.Content,
                Timestamp = m.CreatedAt?.DateTime ?? DateTime.UtcNow
            }).ToList();

            var validation = await inputValidationService.ValidateInputWithFullContextAsync(
                request.Message,
                conversationHistory,
                cancellationToken);

            switch (validation.Status)
            {
                case InputValidationStatus.Valid:
                case InputValidationStatus.ConfirmationResponse:
                    logger.LogInformation("Input is {Status} for user {UserId}", validation.Status, request.UserId);
                    if (request.SessionId.HasValue && session.Id != request.SessionId.Value)
                    {
                        logger.LogWarning("Session ID mismatch for user {UserId}: expected {Expected}, got {Got}",
                            request.UserId, session.Id, request.SessionId.Value);

                        var sessionMismatchResponse = "Phiên làm việc không hợp lệ. Vui lòng bắt đầu một phiên mới.";
                        var sessionMismatchMessage = session.AddMessage("assistant", sessionMismatchResponse);
                        await repository.UpdateAsync(session, cancellationToken);

                        return new ChatCompletionResponse
                        (
                            sessionMismatchResponse,
                            session.Id,
                            sessionMismatchMessage.Id,
                            0,
                            0,
                            null,
                            stopwatch.Elapsed,
                            new List<RetrievedChunk>(),
                            Metadata: new Dictionary<string, object>
                            {
                                ["type"] = "error", ["validation_status"] = validation.Status.ToString()
                            },
                            ResponseType: ChatResponseType.Text
                        );
                    }

                    break;

                case InputValidationStatus.NeedsClarification:
                    logger.LogInformation("Input needs clarification for user {UserId}: {Reason}",
                        request.UserId, validation.Reason);

                    var suggestions = await suggestionService.GetPromptSuggestionsAsync(
                        request.Message,
                        validation,
                        8,
                        cancellationToken);

                    var suggestionContent = FormatSuggestionsAsContent(suggestions, validation.Reason);
                    var clarificationMessage = session.AddMessage("assistant", suggestionContent);
                    await repository.UpdateAsync(session, cancellationToken);

                    return new ChatCompletionResponse
                    (
                        suggestionContent,
                        session.Id,
                        clarificationMessage.Id,
                        0,
                        0,
                        null,
                        stopwatch.Elapsed,
                        new List<RetrievedChunk>(),
                        Metadata: new Dictionary<string, object>
                        {
                            ["type"] = "suggestions",
                            ["validation_reason"] = validation.Reason ?? "",
                            ["suggestions_count"] = suggestions.Suggestions.Count
                        },
                        ResponseType: DetermineResponseType(suggestionContent)
                    );

                case InputValidationStatus.Invalid:
                case InputValidationStatus.ContainsProhibitedContent:
                case InputValidationStatus.ContainsSensitiveInformation:
                    if (IsCompletelyOffTopic(request.Message))
                    {
                        logger.LogWarning("Invalid input for user {UserId}: {Reason}", request.UserId, validation.Reason);

                        var invalidResponse = validation.ResponseMessage ??
                            "Xin lỗi, tôi chỉ có thể hỗ trợ tìm kiếm và phân tích dữ liệu doanh nghiệp. Vui lòng đặt câu hỏi liên quan đến công ty, tài chính, hoặc thông tin kinh doanh.";
                        var invalidMessage = session.AddMessage("assistant", invalidResponse);
                        await repository.UpdateAsync(session, cancellationToken);

                        return new ChatCompletionResponse
                        (
                            invalidResponse,
                            session.Id,
                            invalidMessage.Id,
                            0,
                            0,
                            null,
                            stopwatch.Elapsed,
                            new List<RetrievedChunk>(),
                            Metadata: new Dictionary<string, object>
                            {
                                ["type"] = "error", ["validation_status"] = validation.Status.ToString()
                            },
                            ResponseType: ChatResponseType.Text
                        );
                    }
                    // Otherwise, treat as valid and let AI process
                    logger.LogInformation("Relaxed validation - treating as valid for user {UserId}", request.UserId);
                    break;

                case InputValidationStatus.TooLong:
                    logger.LogWarning("Input too long for user {UserId}", request.UserId);

                    var tooLongResponse = validation.ResponseMessage ??
                        "Câu hỏi của bạn quá dài. Vui lòng rút gọn lại để tôi có thể hỗ trợ bạn tốt hơn.";
                    var tooLongMessage = session.AddMessage("assistant", tooLongResponse);
                    await repository.UpdateAsync(session, cancellationToken);

                    return new ChatCompletionResponse
                    (
                        tooLongResponse,
                        session.Id,
                        tooLongMessage.Id,
                        0,
                        0,
                        null,
                        stopwatch.Elapsed,
                        new List<RetrievedChunk>(),
                        Metadata: new Dictionary<string, object>
                        {
                            ["type"] = "error", ["validation_status"] = validation.Status.ToString()
                        },
                        ResponseType: ChatResponseType.Text
                    );

                case InputValidationStatus.TooShort:
                    // More relaxed - allow short queries like "FPT"
                    if (request.Message.Trim().Length < 2)
                    {
                        logger.LogWarning("Input too short for user {UserId}", request.UserId);

                        var tooShortResponse = validation.ResponseMessage ??
                            "Vui lòng cung cấp thêm thông tin để tôi có thể hỗ trợ bạn tốt hơn.";
                        var tooShortMessage = session.AddMessage("assistant", tooShortResponse);
                        await repository.UpdateAsync(session, cancellationToken);

                        return new ChatCompletionResponse
                        (
                            tooShortResponse,
                            session.Id,
                            tooShortMessage.Id,
                            0,
                            0,
                            null,
                            stopwatch.Elapsed,
                            new List<RetrievedChunk>(),
                            Metadata: new Dictionary<string, object>
                            {
                                ["type"] = "error", ["validation_status"] = validation.Status.ToString()
                            },
                            ResponseType: ChatResponseType.Text
                        );
                    }
                    // Otherwise, treat as valid
                    logger.LogInformation("Relaxed validation - treating short input as valid for user {UserId}", request.UserId);
                    break;

                case InputValidationStatus.Greeting:
                    logger.LogInformation("Greeting received from user {UserId}", request.UserId);

                    var greetingResponse = validation.ResponseMessage ??
                        "Xin chào! Tôi là trợ lý AI chuyên về tìm kiếm và phân tích dữ liệu doanh nghiệp. Tôi có thể giúp bạn tìm hiểu thông tin về các công ty, phân tích tài chính, và nghiên cứu ngành tại Việt Nam. Bạn cần hỗ trợ gì hôm nay?";
                    var greetingMessage = session.AddMessage("assistant", greetingResponse);
                    await repository.UpdateAsync(session, cancellationToken);

                    return new ChatCompletionResponse
                    (
                        greetingResponse,
                        session.Id,
                        greetingMessage.Id,
                        0,
                        0,
                        null,
                        stopwatch.Elapsed,
                        new List<RetrievedChunk>(),
                        Metadata: new Dictionary<string, object>
                        {
                            ["type"] = "greeting", ["validation_status"] = validation.Status.ToString()
                        },
                        ResponseType: ChatResponseType.Text
                    );

                case InputValidationStatus.Farewell:
                    logger.LogInformation("Farewell received from user {UserId}", request.UserId);

                    var farewellResponse = validation.ResponseMessage ??
                        "Cảm ơn bạn đã sử dụng dịch vụ. Chúc bạn một ngày tốt lành! Hẹn gặp lại.";
                    var farewellMessage = session.AddMessage("assistant", farewellResponse);
                    await repository.UpdateAsync(session, cancellationToken);

                    return new ChatCompletionResponse
                    (
                        farewellResponse,
                        session.Id,
                        farewellMessage.Id,
                        0,
                        0,
                        null,
                        stopwatch.Elapsed,
                        new List<RetrievedChunk>(),
                        Metadata: new Dictionary<string, object>
                        {
                            ["type"] = "farewell", ["validation_status"] = validation.Status.ToString()
                        },
                        ResponseType: ChatResponseType.Text
                    );

                case InputValidationStatus.MetaQuestion:
                    logger.LogInformation("Meta question received from user {UserId}", request.UserId);

                    var metaResponse = validation.ResponseMessage ??
                        "Tôi là trợ lý AI được thiết kế đặc biệt để hỗ trợ tìm kiếm và phân tích dữ liệu doanh nghiệp tại Việt Nam. Tôi có thể giúp bạn tìm hiểu thông tin về các công ty, phân tích báo cáo tài chính, nghiên cứu ngành, và dữ liệu kinh tế vĩ mô. Bạn có câu hỏi gì về doanh nghiệp mà tôi có thể hỗ trợ?";
                    var metaMessage = session.AddMessage("assistant", metaResponse);
                    await repository.UpdateAsync(session, cancellationToken);

                    return new ChatCompletionResponse
                    (
                        metaResponse,
                        session.Id,
                        metaMessage.Id,
                        0,
                        0,
                        null,
                        stopwatch.Elapsed,
                        new List<RetrievedChunk>(),
                        Metadata: new Dictionary<string, object>
                        {
                            ["type"] = "meta_question", ["validation_status"] = validation.Status.ToString()
                        },
                        ResponseType: ChatResponseType.Text
                    );

                // All other validation statuses - treat as valid with relaxed approach
                default:
                    logger.LogInformation("Validation status {Status} for user {UserId} - proceeding with relaxed validation",
                        validation.Status, request.UserId);
                    break;
            }

            // Only proceed with AI processing for Valid, ConfirmationResponse, and relaxed validation cases
            var contextChunks = new List<RetrievedChunk>();
            if (request.UseRag)
            {
                contextChunks = await RetrieveContextAsync(request, cancellationToken);
            }

            var prompt = BuildEnhancedPrompt(request.Message, contextChunks, session);

            var completion = await openAiService.ChatCompletionAsync(
                prompt,
                request.MaxTokens,
                request.Temperature,
                functions: GetAvailableFunctions(),
                cancellationToken);

            ChatMessage assistantMessage;

            if (!string.IsNullOrEmpty(completion.FunctionCall))
            {
                var result = await HandleFunctionCallAsync(completion, prompt, request, cancellationToken);
                completion = result.Item1;
                assistantMessage = session.AddMessage("assistant", "Function call executed successfully"
                    , functionName: result.Item2,
                    functionArguments: result.Item3);
            }
            else
            {
                assistantMessage = session.AddMessage("assistant", completion.Content,
                    completion.FunctionCall);
            }

            await repository.UpdateAsync(session, cancellationToken);
            logger.LogInformation("Session updated with new messages. Session ID: {SessionId}, User ID: {UserId}",
                session.Id, request.UserId);

            stopwatch.Stop();

            logger.LogInformation("Chat completion processed successfully in {ElapsedMs}ms for user {UserId}",
                stopwatch.ElapsedMilliseconds, request.UserId);

            // Determine response type based on content and function call
            var responseType = DetermineResponseType(completion.Content, completion.FunctionCall);

            return new ChatCompletionResponse(
                completion.Content,
                session.Id,
                assistantMessage.Id,
                completion.TokensUsed,
                contextChunks.Count,
                completion.FunctionCall,
                stopwatch.Elapsed,
                contextChunks,
                ResponseType: responseType
            );
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing chat completion for user {UserId}", request.UserId);

            var errorResponse = await responseBeautificationService.FormatErrorResponseAsync(
                ex.Message,
                request.Message,
                cancellationToken);

            try
            {
                var session = await GetOrCreateSessionAsync(request, cancellationToken);
                session.AddMessage("user", request.Message);
                var errorMessage = session.AddMessage("assistant", errorResponse);
                await repository.UpdateAsync(session, cancellationToken);

                return new ChatCompletionResponse
                (
                    errorResponse,
                    session.Id,
                    errorMessage.Id,
                    0,
                    0,
                    null,
                    stopwatch.Elapsed,
                    [],
                    ResponseType: ChatResponseType.Text
                );
            }
            catch (Exception saveEx)
            {
                logger.LogError(saveEx, "Failed to save error conversation for user {UserId}", request.UserId);

                return new ChatCompletionResponse
                (
                    errorResponse,
                    request.SessionId ?? Guid.NewGuid(),
                    Guid.NewGuid(),
                    0,
                    0,
                    null,
                    stopwatch.Elapsed,
                    [],
                    ResponseType: ChatResponseType.Text
                );
            }
        }
    }

    /// <summary>
    /// Determines if a request is completely off-topic and should be rejected
    /// </summary>
    private static bool IsCompletelyOffTopic(string message)
    {
        var offTopicKeywords = new[]
        {
            "recipe", "weather", "movie", "music", "sport", "health", "medicine", "personal",
            "cooking", "travel", "game", "entertainment", "poetry", "story", "joke",
            "thời tiết", "nấu ăn", "du lịch", "game", "giải trí", "thơ", "truyện", "đùa"
        };

        var normalizedMessage = message.ToLower();
        return offTopicKeywords.Any(keyword => normalizedMessage.Contains(keyword)) &&
               !ContainsBusinessKeywords(normalizedMessage);
    }

    /// <summary>
    /// Checks if the message contains business-related keywords
    /// </summary>
    private static bool ContainsBusinessKeywords(string message)
    {
        var businessKeywords = new[]
        {
            "company", "business", "financial", "revenue", "profit", "enterprise", "corporation",
            "công ty", "doanh nghiệp", "tài chính", "doanh thu", "lợi nhuận", "kinh doanh"
        };

        return businessKeywords.Any(keyword => message.Contains(keyword));
    }

    /// <summary>
    /// Determines the appropriate response type based on content
    /// </summary>
    private static ChatResponseType DetermineResponseType(string content, string? functionCall = null)
    {
        // If there's a function call, it's likely JSON data
        if (!string.IsNullOrEmpty(functionCall))
        {
            return ChatResponseType.Json;
        }

        // Check for markdown indicators
        if (content.Contains("**") || content.Contains("###") || content.Contains("#") ||
            content.Contains("- ") || content.Contains("1.") || content.Contains("```"))
        {
            return ChatResponseType.Markdown;
        }

        // Default to text
        return ChatResponseType.Text;
    }

    private async Task<ChatSession> GetOrCreateSessionAsync(ChatCompletionCommand request,
        CancellationToken cancellationToken)
    {
        if (request.SessionId.HasValue)
        {
            var session = await repository.GetByIdAsync(request.SessionId.Value, cancellationToken);
            if (session is { IsActive: true })
            {
                logger.LogInformation("Found existing active session with ID {SessionId} for user {UserId}",
                    session.Id, request.UserId);
                return session;
            }
        }

        var newSession = new ChatSession(
            userId: request.UserId,
            title: "New Chat"
        );

        await repository.AddAsync(newSession, cancellationToken);
        logger.LogInformation("Created new chat session with ID {SessionId} for user {UserId}",
            newSession.Id, request.UserId);

        return newSession;
    }

    private async Task<List<RetrievedChunk>> RetrieveContextAsync(ChatCompletionCommand request,
        CancellationToken cancellationToken)
    {
        var retrieveQuery = new RetrieveChunksQuery(
            request.Message,
            K: 8,
            Tables: request.TableFilter,
            MinSimilarityScore: 0.7
        );

        var retrieveResponse = await mediator.Send(retrieveQuery, cancellationToken);


        return retrieveResponse.Chunks;
    }

    private static string BuildEnhancedPrompt(string userMessage, List<RetrievedChunk> contextChunks,
        ChatSession session)
    {
        var prompt = new StringBuilder();

        prompt.AppendLine(@"
[1. CORE IDENTITY]
You are an expert AI assistant, designed exclusively for searching and analyzing corporate financial data. Your role is to act as an intelligent and secure interface for the SearchCompanies API.
[2. PRIMARY CAPABILITIES]
Your capabilities are strictly limited to:
1. Understanding user questions related to company search or financial data analysis
2. Translate user questions into structured JSON requests for the SearchCompanies API
3. Call the SearchCompanies API with the generated JSON request
4. Analyze and present the API response in a clear, user-friendly format
5. Filter and format the response based on the user's question
6. Always respond in the same language the user used

[3. OPERATING WORKFLOW]
You must strictly adhere to the following 4-step workflow:

3.1. Analyze & Validate Request:
- Examine the user's request in detail
- If input is invalid / incomplete / ambiguous, ask for clarification
- IMPORTANT: If user confirms or agrees with your clarification (e.g., 'đúng vậy', 'yes', 'tất cả'), proceed immediately with the search
- Look at conversation history to understand if the user is confirming a previous request

3.2. Construct API Query:
- Translate user questions into structured JSON requests
- If user confirms a location search (e.g., confirming 'huyện An Khánh'), search for ALL companies in that location
- Convert the validated user request into a complete JSON structure (see § 5)

3.3. Call API & Receive Results:
- Execute the call to SearchCompanies API with the JSON

3.4. Analyze & Present Findings:
- Analyze the API data
- Present results in the clearest, most structured, user-friendly way

[4. NON-NEGOTIABLE RULES]
These are absolute limits you must never violate:

4.1. Scope Limitation: NEVER answer any question that is not directly related to company search and financial data analysis. For out-of-scope requests (e.g., health advice, personal requests, poetry), respond immediately with:
""Sorry, I can only help with company search and financial data analysis.""

4.2. Data Source: You are STRICTLY PROHIBITED from accessing any external data sources or your own internal knowledge. The only permissible source of information is the response from the SearchCompanies API.

4.3. Objectivity: DO NOT generate answers based on personal opinions, inferences, or assumptions that are not present in the API data.

4.4. Handling Empty Results: If the API returns an empty result or no data matches the search criteria, you must inform the user:
""No relevant data was found based on your search criteria.""

[5. JSON STRUCTURE PROTOCOL]
When constructing the JSON for the API call, you must adhere to the following CRITICAL rules:

5.1. Always Return a Complete JSON Structure:
- Include every field defined by the function schema – both required & optional.
- if a value cannot be extracted, parsed, Or users do not provide → assign null or default value (applied both with Required Fields).
- No self -deduction or self -filling value
EXAMPLE -search_company_simple
- With function call `search_company_simple`
{
  ""search_text"": ""Example Company"",
  ""pageIndex"": 1,
  ""pageSize"": 10,
}

[6. OUTPUT REQUIREMENTS]
When presenting the final output to the user:

6.1. Language: Always respond in the exact same language the user used in their query (Vietnamese for Vietnamese questions, English for English questions)

6.2. Content: Use the data received from the API as your primary source, but format it for readability

6.3. Context: Always provide helpful context and brief explanations for the presented data to help the user understand the meaning behind the numbers

6.4. Structure: Return results in a logical, easy-to-follow structure

[7. CONFIRMATION HANDLING]
When users respond with confirmations to clarification requests:

7.1. Common Confirmations: 'đúng', 'đúng vậy', 'phải', 'yes', 'correct', 'tất cả', 'toàn bộ'

7.2. Action: Immediately proceed with the search based on the confirmed understanding

7.3. Example Flow:
- User: ""Doanh nghiệp ở huyện An Khánh, Tp Hà Nội""
- You: ""Có phải bạn muốn tìm kiếm tất cả doanh nghiệp ở huyện An Khánh, TP Hà Nội không?""
- User: ""Đúng vậy"" or ""Tất cả""
- You: MUST call search function immediately with location parameters, NOT ask for more clarification
");

        if (contextChunks.Any())
        {
            prompt.AppendLine("=== RELEVANT CONTEXT FROM CHUNK ===");
            foreach (var chunk in contextChunks)
            {
                prompt.AppendLine($"Source: {chunk.TableName} (Score: {chunk.SimilarityScore:F3})");
                prompt.AppendLine(chunk.Content);
                prompt.AppendLine();
            }

            prompt.AppendLine("=== END CONTEXT ===");
            prompt.AppendLine();
        }

        // Add recent conversation history (last 10 messages)
        var recentMessages = session.Messages.TakeLast(10).ToList();
        if (recentMessages.Count != 0)
        {
            prompt.AppendLine("=== CONVERSATION HISTORY ===");
            foreach (var msg in recentMessages)
            {
                prompt.AppendLine($"{msg.Role.ToUpper()}: {msg.Content}");
            }

            prompt.AppendLine("=== END HISTORY ===");
            prompt.AppendLine();
        }

        prompt.AppendLine("USER: " + userMessage);

        return prompt.ToString();
    }

    private static List<Tool> GetAvailableFunctions()
    {
        return CompanySearchFunctions.GetFunctionJsonStrings();
    }

    private string FormatSuggestionsAsContent(PromptSuggestionResponse suggestions, string? reason)
    {
        var content = new StringBuilder();

        content.AppendLine(
            "Tôi nhận thấy câu hỏi của bạn cần làm rõ thêm. Dưới đây là một số gợi ý để giúp bạn tìm kiếm thông tin công ty hiệu quả hơn:");

        if (!string.IsNullOrEmpty(reason))
        {
            content.AppendLine($"\n**Lý do:** {reason}");
        }

        content.AppendLine("\n**Các gợi ý tìm kiếm:**");

        for (int i = 0; i < suggestions.Suggestions.Count; i++)
        {
            var suggestion = suggestions.Suggestions[i];
            content.AppendLine($"\n{i + 1}. **{suggestion.Text}**");

            if (!string.IsNullOrEmpty(suggestion.Description))
            {
                content.AppendLine($"   - {suggestion.Description}");
            }

            if (!string.IsNullOrEmpty(suggestion.Example))
            {
                content.AppendLine($"   - *Ví dụ: {suggestion.Example}*");
            }
        }

        content.AppendLine(
            "\nVui lòng chọn một trong các gợi ý trên hoặc cung cấp thêm thông tin cụ thể để tôi có thể hỗ trợ bạn tốt hơn.");

        return content.ToString();
    }

    private async Task<(ChatCompletionResponse, string, string)> HandleFunctionCallAsync(
        ChatCompletionResponse completion,
        string originalPrompt,
        ChatCompletionCommand request,
        CancellationToken cancellationToken)
    {
        logger.LogInformation("Handling function call: {FunctionCall}", completion.FunctionCall);

        if (!string.IsNullOrEmpty(completion.FunctionCall))
        {
            logger.LogInformation("Processing function call in HandleFunctionCallAsync");
            var functionResult = await ExecuteFunctionAsync(completion.FunctionCall, request, cancellationToken);

            // Nếu function call có data, trả về ngay mà không cần gọi AI
            if (functionResult.result is { HasData: true, IsSuccessful: true })
            {
                logger.LogInformation("Function call returned data, returning direct response without AI call");

                // Parse JSON data and create pagination info
                object? jsonData = null;
                PaginationInfo? pagination = null;

                try
                {
                    var jsonElement = JsonSerializer.Deserialize<JsonElement>(functionResult.result.Content);
                    jsonData = jsonElement;

                    // Extract pagination info from Response property if available (new structure)
                    JsonElement responseElement = jsonElement;
                    if (jsonElement.TryGetProperty("Response", out var responseProperty))
                    {
                        responseElement = responseProperty;
                    }

                    if (responseElement.TryGetProperty("PageIndex", out var pageIndex) &&
                        responseElement.TryGetProperty("PageSize", out var pageSize) &&
                        responseElement.TryGetProperty("TotalItem", out var totalItems) &&
                        responseElement.TryGetProperty("PageCount", out var totalPages))
                    {
                        var currentPage = pageIndex.GetInt32();
                        var currentPageSize = pageSize.GetInt32();
                        var currentTotalItems = totalItems.GetInt32();
                        var currentTotalPages = totalPages.GetInt32();

                        var hasNext = currentPage < currentTotalPages;
                        var hasPrev = currentPage > 1;

                        // Generate next/previous page endpoints
                        string? nextEndpoint = null;
                        string? prevEndpoint = null;

                        if (hasNext)
                        {
                            var nextPage = currentPage + 1;
                            nextEndpoint = functionResult.functionName switch
                            {
                                "search_company_simple" => $"/api/v1/companies/search/simple?page={nextPage}&size={currentPageSize}",
                                "search_company_advanced" => $"/api/v1/companies/search/advanced?page={nextPage}&size={currentPageSize}",
                                _ => null
                            };
                        }

                        if (hasPrev)
                        {
                            var prevPage = currentPage - 1;
                            prevEndpoint = functionResult.functionName switch
                            {
                                "search_company_simple" => $"/api/v1/companies/search/simple?page={prevPage}&size={currentPageSize}",
                                "search_company_advanced" => $"/api/v1/companies/search/advanced?page={prevPage}&size={currentPageSize}",
                                _ => null
                            };
                        }

                        pagination = new PaginationInfo(
                            currentPage, currentPageSize, currentTotalItems, currentTotalPages,
                            hasNext, hasPrev, nextEndpoint, prevEndpoint);
                    }
                }
                catch (System.Text.Json.JsonException ex)
                {
                    logger.LogWarning(ex, "Failed to parse function result as JSON, treating as text");
                }

                return (new ChatCompletionResponse
                {
                    Content = functionResult.result.Content,
                    SessionId = completion.SessionId,
                    MessageId = completion.MessageId,
                    TokensUsed = 0,
                    ContextChunksUsed = 0,
                    FunctionCall = completion.FunctionCall,
                    ResponseType = jsonData != null ? ChatResponseType.Json : ChatResponseType.Text,
                    JsonData = jsonData,
                    Pagination = pagination
                }, functionResult.functionName ?? "", functionResult.arguments ?? "");
            }

            logger.LogInformation("Function call returned no data or error, sending to AI for processing");
            var updatedPrompt = originalPrompt + "\n\nFUNCTION_RESULT: " + functionResult.result.Content;

            var finalResponse = await openAiService.ChatCompletionAsync(
                updatedPrompt,
                request.MaxTokens,
                request.Temperature,
                functions: GetAvailableFunctions(),
                cancellationToken: cancellationToken);

            logger.LogInformation("AI call completed. Response length: {Length}",
                finalResponse.Content?.Length ?? 0);
            return (finalResponse, functionResult.functionName ?? "", functionResult.arguments ?? "");
        }

        logger.LogWarning("No function call found in completion response.");
        return (
            new ChatCompletionResponse
            {
                Content = "No function call found in the response.",
                SessionId = completion.SessionId,
                MessageId = completion.MessageId,
                TokensUsed = 0,
                ContextChunksUsed = 0,
                FunctionCall = null
            }, "", "");
    }

    private async Task<(FunctionExecutionResult result, string? functionName, string? arguments)> ExecuteFunctionAsync(
        string functionCall,
        ChatCompletionCommand request,
        CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Raw function call received: {FunctionCall}", functionCall);

            var functionCallData = JsonSerializer.Deserialize<JsonElement>(functionCall);
            var functionName = functionCallData.GetProperty("function").GetProperty("name").GetString();
            var arguments = functionCallData.GetProperty("function").GetProperty("arguments").GetString();

            logger.LogInformation("Executing function: {FunctionName} with arguments: {Arguments}", functionName,
                arguments);

            var result = functionName switch
            {
                "search_company_simple" => await ExecuteSimpleCompanySearchAsync(arguments, request.AuthToken,
                    cancellationToken),
                "search_company_advanced" => await ExecuteAdvancedCompanySearchAsync(arguments, request.AuthToken,
                    cancellationToken),
                _ => new FunctionExecutionResult($"Unknown function: {functionName}", false, false)
            };

            logger.LogInformation(
                "Function execution result - HasData: {HasData}, IsSuccessful: {IsSuccessful}",
                result.HasData, result.IsSuccessful);
            return (result, functionName, arguments);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error executing function: {FunctionCall}", functionCall);
            var errorMessage = await responseBeautificationService.FormatErrorResponseAsync(
                ex.Message, "Function execution", cancellationToken);
            return (new FunctionExecutionResult(errorMessage, false, false), null, null);
        }
    }

    private async Task<FunctionExecutionResult> ExecuteSimpleCompanySearchAsync(string? arguments, string? authToken,
        CancellationToken cancellationToken)
    {
        try
        {
            var searchParams = JsonConvert.DeserializeObject<SimpleSearchRequest>(arguments ?? "{}");
            if (searchParams == null)
            {
                return new FunctionExecutionResult("Invalid search parameters", false, false);
            }

            var result = await companySearchService.SimpleSearchAsync(searchParams, authToken, cancellationToken);

            if (result is { Success: true, Data: not null })
            {
                var hasData = result.Data.Companies?.Count > 0;

                logger.LogInformation(
                    "Simple search completed - Success: {Success}, HasData: {HasData}, ItemCount: {ItemCount}",
                    result.Success, hasData, result.Data.Companies?.Count ?? 0);

                if (hasData)
                {
                    // Create combined response with both request and response data
                    var combinedResponse = new
                    {
                        Request = new
                        {
                            FunctionName = "search_company_simple",
                            Parameters = searchParams
                        },
                        Response = result.Data,
                        Metadata = new
                        {
                            ExecutedAt = DateTime.UtcNow,
                            HasData = true,
                            TotalResults = result.Data.Companies?.Count ?? 0
                        }
                    };

                    var json = JsonConvert.SerializeObject(combinedResponse);
                    return new FunctionExecutionResult(json, true, true);
                }

                var noDataMessage = $"No companies found matching the search criteria: '{searchParams.SearchText}'";
                return new FunctionExecutionResult(noDataMessage, false, true);
            }

            var errorMessage = await responseBeautificationService.FormatErrorResponseAsync(result.Message,
                searchParams.SearchText ?? "", cancellationToken);
            return new FunctionExecutionResult(errorMessage, false, false);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in simple company search");
            var errorMessage = await responseBeautificationService.FormatErrorResponseAsync(ex.Message,
                "Simple company search",
                cancellationToken);
            return new FunctionExecutionResult(errorMessage, false, false);
        }
    }

    private async Task<FunctionExecutionResult> ExecuteAdvancedCompanySearchAsync(string? arguments, string? authToken,
        CancellationToken cancellationToken)
    {
        try
        {
            var searchParams = JsonConvert.DeserializeObject<AdvancedSearchRequest>(arguments ?? "{}");
            if (searchParams == null)
            {
                return new FunctionExecutionResult("Invalid search parameters", false, false);
            }

            var result = await companySearchService.AdvancedSearchAsync(searchParams, authToken, cancellationToken);

            if (result is { Success: true, Data: not null })
            {
                // Kiểm tra xem có data không (items count > 0)
                var hasData = result.Data.Companies?.Count > 0;

                logger.LogInformation(
                    "Advanced search completed - Success: {Success}, HasData: {HasData}, ItemCount: {ItemCount}",
                    result.Success, hasData, result.Data.Companies?.Count ?? 0);

                if (hasData)
                {
                    // Create combined response with both request and response data
                    var combinedResponse = new
                    {
                        Request = new
                        {
                            FunctionName = "search_company_advanced",
                            Parameters = searchParams
                        },
                        Response = result.Data,
                        Metadata = new
                        {
                            ExecutedAt = DateTime.UtcNow,
                            HasData = true,
                            TotalResults = result.Data.Companies?.Count ?? 0,
                            FilterCriteria = new
                            {
                                HasAreaFilters = searchParams.Areas?.Count > 0,
                                HasVsicFilters = searchParams.Vsics?.Count > 0,
                                HasFinancialFilters = searchParams.Financials?.Count > 0,
                                HasCompanyTypeFilters = searchParams.CompanyTypes?.Count > 0,
                                HasLegalFilters = searchParams.Legals?.Count > 0,
                                HasOwnerFilters = searchParams.Owners?.Count > 0,
                                HasImportExportFilters = searchParams.ImportExportTurnover != null,
                                HasRegistrationDateFilters = searchParams.RegistrationDates?.Count > 0
                            }
                        }
                    };

                    return new FunctionExecutionResult(JsonConvert.SerializeObject(combinedResponse), true, true);
                }

                var noDataMessage = "No companies found matching the advanced search criteria";
                return new FunctionExecutionResult(noDataMessage, false, true);
            }

            // API call failed, để AI xử lý
            var errorMessage = await responseBeautificationService.FormatErrorResponseAsync(result.Message,
                "Advanced search query",
                cancellationToken);
            return new FunctionExecutionResult(errorMessage, false, false);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in advanced company search");
            var errorMessage = await responseBeautificationService.FormatErrorResponseAsync(ex.Message,
                "Advanced search query",
                cancellationToken);
            return new FunctionExecutionResult(errorMessage, false, false);
        }
    }
}

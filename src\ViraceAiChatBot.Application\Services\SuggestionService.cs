using System.Diagnostics;
using System.Text.Json;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;
using ViraceAiChatBot.Application.Dtos;
using ViraceAiChatBot.Application.Interfaces;

namespace ViraceAiChatBot.Application.Services;

public class SuggestionService(
    Interfaces.OpenAiService openAiService,
    Interfaces.InputValidationService inputValidationService,
    ILogger<SuggestionService> logger) : Interfaces.SuggestionService
{
    private readonly string promptSuggestionTemplate = @"
You are an AI assistant specialized in company search and financial data analysis.
When a user's input is unclear or invalid, you must provide helpful prompt suggestions.

Your domain expertise includes:
- Company name searches
- Tax code lookups
- Financial data analysis
- Business registration information
- Import/Export data
- Company ownership and legal representatives
- Industry (VSIC) classification
- Geographic/location-based searches

USER INPUT: {0}
VALIDATION ISSUE REASON: {1}

CRITICAL LANGUAGE REQUIREMENTS:
1. FIRST: Detect the language of USER INPUT (Vietnamese/English/etc.)
2. MANDATORY: Translate the VALIDATION ISSUE REASON to match USER INPUT language exactly
3. NEVER mix languages - everything must be in USER INPUT language
4. If USER INPUT is Vietnamese → REASON must be in Vietnamese
5. If USER INPUT is English → REASON must be in English
6. If unclear → Default to Vietnamese
7. DO NOT copy VALIDATION ISSUE REASON as-is - ALWAYS translate it

TRANSLATION EXAMPLES:
- USER INPUT: 'tìm công ty FPT' → REASON: 'Câu hỏi cần thêm thông tin chi tiết' (Vietnamese)
- USER INPUT: 'find FPT company' → REASON: 'The request needs more specific details' (English)
- REASON: 'Input needs clarification' + Vietnamese USER → 'Đầu vào cần làm rõ thêm'
- REASON: 'Too broad' + Vietnamese USER → 'Quá chung chung'
- REASON: 'Missing details' + Vietnamese USER → 'Thiếu chi tiết'

Generate {2} helpful prompt suggestions that:
1. Are clear and specific
2. Follow the company search domain rules
3. Show the user how to properly format their request
4. Include example data when helpful
5. Cover different aspects of company search
6. ALL TEXT must be in the same language as USER INPUT

Return JSON format - ENSURE ALL CONTENT MATCHES USER INPUT LANGUAGE:
{{
  ""suggestions"": [
    {{
      ""text"": ""example prompt"",
      ""category"": ""company_search|financial_data|location_search|advanced_filter"",
      ""description"": ""brief explanation"",
      ""confidence"": 0.9,
      ""example"": ""optional example result""
    }}
  ]
}}";

    private readonly string nextWordTemplate = @"
You are helping users complete their company search queries. Based on the current text and cursor position, suggest the most relevant next words.

Context: Company search and financial data analysis
Current text: {0}
Cursor position: {1}
Context: {2}

Suggest {3} most likely next words/phrases that would help complete a valid company search query.
Consider:
- Company names
- Financial terms
- Location names
- Industry terms
- Search operators

Important:
-  Always respond in the exact same language the user used in their query (Vietnamese for Vietnamese questions, English for English questions)

Return JSON format:
{{
  ""suggestions"": [
    {{
      ""word"": ""suggested word"",
      ""confidence"": 0.9,
      ""context"": ""why this word makes sense"",
      ""completionType"": ""word|phrase""
    }}
  ]
}}";

    private readonly string sentenceSuggestionTemplate = @"
You are helping users complete or start new sentences for company search queries.

Current text: {0}
Cursor position: {1}
Context: {2}
Include new sentences: {3}

Generate {4} sentence suggestions that:
1. Complete the current sentence if incomplete
2. Suggest new related sentences if requested
3. Follow company search domain rules
4. Are grammatically correct and professional

Important:
-  Always respond in the exact same language the user used in their query (Vietnamese for Vietnamese questions, English for English questions)

Return JSON format:
{{
  ""suggestions"": [
    {{
      ""text"": ""suggested sentence"",
      ""type"": ""completion|new_sentence"",
      ""confidence"": 0.9,
      ""context"": ""explanation"",
      ""category"": ""search_query|filter_criteria|data_request""
    }}
  ]
}}";

    private readonly string enhancePromptTemplate = @"
You are an expert at enhancing user prompts for company search and financial data analysis.

Original prompt: {0}
Intended context: {1}
Include examples: {2}

Enhance this prompt to:
1. Be more specific and clear
2. Include relevant search criteria
3. Follow company search domain rules
4. Be more likely to return useful results
5. Include helpful context or examples if requested

Important:
-  Always respond in the exact same language the user used in their query (Vietnamese for Vietnamese questions, English for English questions)

Return JSON format:
{{
  ""enhancedPrompt"": ""enhanced version"",
  ""improvements"": [""list of improvements made""],
  ""examples"": [""example queries if requested""]
}}";

    public async Task<PromptSuggestionResponse> GetPromptSuggestionsAsync(
        string currentInput,
        InputValidationResult validationResult,
        int maxSuggestions = 8,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            logger.LogInformation("Generating prompt suggestions for input: {Input}", currentInput);

            var prompt = string.Format(promptSuggestionTemplate,
                currentInput,
                validationResult.Reason,
                maxSuggestions);

            var response = await openAiService.ChatCompletionAsync(
                prompt,
                maxTokens: 2000,
                temperature: 0.8,
                cancellationToken: cancellationToken);

            var suggestions = ParsePromptSuggestions(response.Content);

            return new PromptSuggestionResponse(
                suggestions,
                validationResult.Reason,
                !validationResult.Status.HasFlag(InputValidationStatus.NeedsClarification),
                stopwatch.Elapsed
            );
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error generating prompt suggestions");
            return new PromptSuggestionResponse(
                GetDefaultPromptSuggestions(),
                "Error occurred during suggestion generation",
                false,
                stopwatch.Elapsed
            );
        }
    }

    public async Task<PromptSuggestionResponse> GetPromptSuggestionsAsync(
        string currentInput,
        int maxSuggestions = 8,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            logger.LogInformation("Generating prompt suggestions for input: {Input}", currentInput);

            var validation = await inputValidationService.ValidateInputAsync(currentInput, cancellationToken);

            var prompt = string.Format(promptSuggestionTemplate,
                currentInput,
                validation.Reason ?? "Input needs clarification",
                maxSuggestions);

            var response = await openAiService.ChatCompletionAsync(
                prompt,
                maxTokens: 2000,
                temperature: 0.8,
                cancellationToken: cancellationToken);

            var suggestions = ParsePromptSuggestions(response.Content);

            return new PromptSuggestionResponse(
                suggestions,
                validation.Reason,
                !validation.Status.HasFlag(InputValidationStatus.NeedsClarification),
                stopwatch.Elapsed
            );
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error generating prompt suggestions");
            return new PromptSuggestionResponse(
                GetDefaultPromptSuggestions(),
                "Error occurred during suggestion generation",
                false,
                stopwatch.Elapsed
            );
        }
    }

    public async Task<NextWordSuggestionResponse> GetNextWordSuggestionsAsync(
        string currentText,
        int cursorPosition,
        string? context = null,
        int maxSuggestions = 5,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            logger.LogInformation("Generating next word suggestions for text: {Text} at position: {Position}",
                currentText, cursorPosition);

            var prompt = string.Format(nextWordTemplate,
                currentText,
                cursorPosition,
                context ?? "company search context",
                maxSuggestions);

            var response = await openAiService.ChatCompletionAsync(
                prompt,
                maxTokens: 1000,
                temperature: 0.7,
                cancellationToken: cancellationToken);

            var suggestions = ParseWordSuggestions(response.Content);

            return new NextWordSuggestionResponse(
                suggestions,
                cursorPosition,
                context,
                stopwatch.Elapsed
            );
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error generating next word suggestions");
            return new NextWordSuggestionResponse(
                [],
                cursorPosition,
                "Error occurred during suggestion generation",
                stopwatch.Elapsed
            );
        }
    }

    public async Task<SentenceSuggestionResponse> GetSentenceSuggestionsAsync(
        string currentText,
        int cursorPosition,
        string? context = null,
        bool includeNewSentences = true,
        int maxSuggestions = 5,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            logger.LogInformation("Generating sentence suggestions for text: {Text}", currentText);

            var prompt = string.Format(sentenceSuggestionTemplate,
                currentText,
                cursorPosition,
                context ?? "company search context",
                includeNewSentences,
                maxSuggestions);

            var response = await openAiService.ChatCompletionAsync(
                prompt,
                maxTokens: 1500,
                temperature: 0.8,
                cancellationToken: cancellationToken);

            var suggestions = ParseSentenceSuggestions(response.Content);

            return new SentenceSuggestionResponse(
                suggestions,
                cursorPosition,
                context,
                stopwatch.Elapsed
            );
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error generating sentence suggestions");
            return new SentenceSuggestionResponse(
                [],
                cursorPosition,
                "Error occurred during suggestion generation",
                stopwatch.Elapsed
            );
        }
    }

    public async Task<EnhancedPromptResponse> EnhancePromptAsync(
        string originalPrompt,
        string? intendedContext = null,
        bool includeExamples = true,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            logger.LogInformation("Enhancing prompt: {Prompt}", originalPrompt);

            var prompt = string.Format(enhancePromptTemplate,
                originalPrompt,
                intendedContext ?? "general company search",
                includeExamples);

            var response = await openAiService.ChatCompletionAsync(
                prompt,
                maxTokens: 2000,
                temperature: 0.6,
                cancellationToken: cancellationToken);

            var parsedResponse = ParseEnhancedPrompt(response.Content);

            return new EnhancedPromptResponse(
                parsedResponse.EnhancedPrompt,
                parsedResponse.Improvements,
                originalPrompt,
                parsedResponse.Examples,
                stopwatch.Elapsed
            );
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error enhancing prompt");
            return new EnhancedPromptResponse(
                originalPrompt,
                ["Error occurred during enhancement"],
                originalPrompt,
                [],
                stopwatch.Elapsed
            );
        }
    }

    private List<SuggestedPrompt> ParsePromptSuggestions(string jsonResponse)
    {
        try
        {
            var cleaned = CleanJsonResponse(jsonResponse);
            var result = JsonSerializer.Deserialize<JsonElement>(cleaned);

            if (result.TryGetProperty("suggestions", out var suggestionsElement))
            {
                var suggestions = new List<SuggestedPrompt>();
                foreach (var item in suggestionsElement.EnumerateArray())
                {
                    if (item.TryGetProperty("text", out var text) &&
                        item.TryGetProperty("category", out var category))
                    {
                        suggestions.Add(new SuggestedPrompt(
                            text.GetString() ?? "",
                            category.GetString() ?? "general",
                            item.TryGetProperty("description", out var desc) ? desc.GetString() : null,
                            item.TryGetProperty("confidence", out var conf) ? (float)conf.GetDouble() : 0.8f,
                            item.TryGetProperty("example", out var ex) ? ex.GetString() : null
                        ));
                    }
                }

                return suggestions;
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error parsing prompt suggestions: {Response}", jsonResponse);
        }

        return GetDefaultPromptSuggestions();
    }

    private List<WordSuggestion> ParseWordSuggestions(string response)
    {
        try
        {
            var cleaned = CleanJsonResponse(response);
            var result = JsonSerializer.Deserialize<JsonElement>(cleaned);

            if (result.TryGetProperty("suggestions", out var suggestionsElement))
            {
                var suggestions = new List<WordSuggestion>();
                foreach (var item in suggestionsElement.EnumerateArray())
                {
                    if (item.TryGetProperty("word", out var word))
                    {
                        suggestions.Add(new WordSuggestion(
                            word.GetString() ?? "",
                            item.TryGetProperty("confidence", out var conf) ? (float)conf.GetDouble() : 0.8f,
                            item.TryGetProperty("context", out var ctx) ? ctx.GetString() : null,
                            item.TryGetProperty("completionType", out var type) ? type.GetString() : "word"
                        ));
                    }
                }

                return suggestions;
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error parsing word suggestions: {Response}", response);
        }

        return [];
    }

    private List<SentenceSuggestion> ParseSentenceSuggestions(string response)
    {
        try
        {
            var cleaned = CleanJsonResponse(response);
            var result = JsonSerializer.Deserialize<JsonElement>(cleaned);

            if (result.TryGetProperty("suggestions", out var suggestionsElement))
            {
                var suggestions = new List<SentenceSuggestion>();
                foreach (var item in suggestionsElement.EnumerateArray())
                {
                    if (item.TryGetProperty("text", out var text) &&
                        item.TryGetProperty("type", out var type))
                    {
                        suggestions.Add(new SentenceSuggestion(
                            text.GetString() ?? "",
                            type.GetString() ?? "completion",
                            item.TryGetProperty("confidence", out var conf) ? (float)conf.GetDouble() : 0.8f,
                            item.TryGetProperty("context", out var ctx) ? ctx.GetString() : null,
                            item.TryGetProperty("category", out var cat) ? cat.GetString() : null
                        ));
                    }
                }

                return suggestions;
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error parsing sentence suggestions: {Response}", response);
        }

        return [];
    }

    private EnhancedPromptResponse ParseEnhancedPrompt(string response)
    {
        try
        {
            var cleaned = CleanJsonResponse(response);
            var result = JsonSerializer.Deserialize<JsonElement>(cleaned);

            var enhancedPrompt =
                result.TryGetProperty("enhancedPrompt", out var prompt) ? prompt.GetString() ?? "" : "";
            var improvements = new List<string>();
            var examples = new List<string>();

            if (result.TryGetProperty("improvements", out var improvementsElement))
            {
                improvements.AddRange(improvementsElement.EnumerateArray().Select(item => item.GetString() ?? ""));
            }

            if (result.TryGetProperty("examples", out var examplesElement))
            {
                examples.AddRange(examplesElement.EnumerateArray().Select(item => item.GetString() ?? ""));
            }

            return new EnhancedPromptResponse(
                enhancedPrompt,
                improvements,
                null,
                examples,
                TimeSpan.Zero
            );
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error parsing enhanced prompt: {Response}", response);
            return new EnhancedPromptResponse(
                "",
                [],
                null,
                [],
                TimeSpan.Zero
            );
        }
    }

    private static string CleanJsonResponse(string response)
    {
        // Remove markdown code blocks
        var cleaned = Regex.Replace(response, @"```(?:json)?\s*([\s\S]*?)```", "$1", RegexOptions.Compiled).Trim();

        // Ensure proper JSON format
        if (!cleaned.StartsWith('{') || !cleaned.EndsWith('}'))
        {
            // Try to extract JSON from the response
            var match = Regex.Match(cleaned, @"\{[\s\S]*\}", RegexOptions.Compiled);
            if (match.Success)
            {
                cleaned = match.Value;
            }
        }

        return cleaned;
    }

    private static List<SuggestedPrompt> GetDefaultPromptSuggestions()
    {
        return
        [
            new SuggestedPrompt(
                "Tìm kiếm công ty theo tên",
                "company_search",
                "Tìm kiếm công ty bằng tên hoặc một phần tên",
                0.9f,
                "Tìm công ty có tên chứa 'Vingroup'"
            ),
            new SuggestedPrompt(
                "Tìm kiếm theo mã số thuế",
                "company_search",
                "Tìm kiếm công ty bằng mã số thuế chính xác",
                0.9f,
                "Tìm công ty có mã số thuế 0101234567"
            ),
            new SuggestedPrompt(
                "Phân tích dữ liệu tài chính",
                "financial_data",
                "Xem thông tin tài chính của công ty",
                0.8f,
                "Xem báo cáo tài chính của công ty Vingroup năm 2023"
            )
        ];
    }

    private static List<WordSuggestion> GetDefaultWordSuggestions(string currentText, int cursorPosition)
    {
        return
        [
            new WordSuggestion("công", 0.8f, null, "word"),
            new WordSuggestion("ty", 0.8f, null, "word"),
            new WordSuggestion("tìm", 0.7f, null, "word")
        ];
    }

    private static List<SentenceSuggestion> GetDefaultSentenceSuggestions(bool includeNewSentences)
    {
        var suggestions = new List<SentenceSuggestion>
        {
            new SentenceSuggestion(
                "Tìm kiếm thông tin công ty",
                "completion",
                0.8f,
                null,
                "search_query"
            )
        };

        if (includeNewSentences)
        {
            suggestions.Add(new SentenceSuggestion(
                "Tôi muốn tìm hiểu về dữ liệu tài chính của công ty này.",
                "new_sentence",
                0.7f,
                null,
                "data_request"
            ));
        }

        return suggestions;
    }
}

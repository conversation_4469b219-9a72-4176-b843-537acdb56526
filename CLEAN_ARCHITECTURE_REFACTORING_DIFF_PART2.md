# Clean Architecture Refactoring - Complete Diff (Part 2)

## 4. Repository Pattern Improvements

### `src/ViraceAiChatBot.Domain/Interfaces/IChatSessionRepository.cs`
```diff
 using ViraceAiChatBot.Domain.Aggregates;

 namespace ViraceAiChatBot.Domain.Interfaces;

-public interface ChatSessionRepository
+/// <summary>
+/// Repository interface for ChatSession aggregate root.
+/// Follows Clean Architecture principles by defining contracts in the Domain layer.
+/// </summary>
+public interface IChatSessionRepository
 {
+    // Basic CRUD operations
     Task<ChatSession?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
+    Task<ChatSession?> GetByIdWithTrackingAsync(Guid id, CancellationToken cancellationToken = default);
+    Task<ChatSession?> GetSessionWithMessagesAsync(Guid id, CancellationToken cancellationToken = default);
+    
     Task<ChatSession> AddAsync(ChatSession session, CancellationToken cancellationToken = default);
     Task UpdateAsync(ChatSession session, CancellationToken cancellationToken = default);
+    Task DeleteAsync(ChatSession session, CancellationToken cancellationToken = default);
+    
+    // Query operations
     Task<List<ChatSession>> GetByUserIdAsync(
         string userId,
         bool includeDeleted = false,
         int pageSize = 20,
         int pageNumber = 1,
         CancellationToken cancellationToken = default);

-    Task<int> GetCountByUserIdAsync(
-        string userId,
-        CancellationToken cancellationToken = default);

     Task<List<ChatSession>> GetActiveSessionsAsync(
         string userId,
         CancellationToken cancellationToken = default);

-    Task<ChatSession?> GetSessionWithMessagesAsync(Guid id, CancellationToken cancellationToken = default);
+    Task<List<ChatSession>> GetRecentSessionsAsync(
+        string userId, 
+        int limit = 10, 
+        CancellationToken cancellationToken = default);
+
+    // Utility operations
+    Task<int> GetCountByUserIdAsync(
+        string userId,
+        CancellationToken cancellationToken = default);
+
+    Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default);
+
+    // Business operations
+    Task ArchiveOldSessionsAsync(
+        TimeSpan olderThan, 
+        CancellationToken cancellationToken = default);
 }
```

### `src/ViraceAiChatBot.Infrastructure/Persistence/Repositories/ChatSessionRepository.cs`
```diff
 using Microsoft.EntityFrameworkCore;
+using Microsoft.Extensions.Logging;
 using ViraceAiChatBot.Domain.Aggregates;
 using ViraceAiChatBot.Domain.Interfaces;
-using ViraceAiChatBot.Infrastructure.Persistence;

 namespace ViraceAiChatBot.Infrastructure.Persistence.Repositories;

-public class ChatSessionRepository(ChatBotDbContext context) : Domain.Interfaces.ChatSessionRepository
+/// <summary>
+/// Repository implementation for ChatSession aggregate root.
+/// Handles data persistence and retrieval while maintaining Clean Architecture principles.
+/// </summary>
+public sealed class ChatSessionRepository : IChatSessionRepository
 {
+    private readonly ChatBotDbContext _context;
+    private readonly ILogger<ChatSessionRepository> _logger;
+
+    public ChatSessionRepository(ChatBotDbContext context, ILogger<ChatSessionRepository> logger)
+    {
+        _context = context ?? throw new ArgumentNullException(nameof(context));
+        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
+    }
+
     public async Task<ChatSession?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
     {
-        return await context.ChatSessions
-            .AsNoTracking()
-            .FirstOrDefaultAsync(x => x.Id == id, cancellationToken);
+        try
+        {
+            return await _context.ChatSessions
+                .AsNoTracking()
+                .FirstOrDefaultAsync(x => x.Id == id, cancellationToken);
+        }
+        catch (Exception ex)
+        {
+            _logger.LogError(ex, "Error retrieving chat session with ID {SessionId}", id);
+            throw;
+        }
     }

     public async Task<ChatSession> AddAsync(ChatSession chatSession, CancellationToken cancellationToken = default)
     {
-        context.ChatSessions.Add(chatSession);
-        await context.SaveChangesAsync(cancellationToken);
-        return chatSession;
+        ArgumentNullException.ThrowIfNull(chatSession);
+
+        try
+        {
+            _context.ChatSessions.Add(chatSession);
+            await _context.SaveChangesAsync(cancellationToken);
+            
+            _logger.LogInformation("Successfully added chat session {SessionId} for user {UserId}", 
+                chatSession.Id, chatSession.UserId);
+            
+            return chatSession;
+        }
+        catch (Exception ex)
+        {
+            _logger.LogError(ex, "Error adding chat session for user {UserId}", chatSession.UserId);
+            throw;
+        }
     }

     public async Task UpdateAsync(ChatSession chatSession, CancellationToken cancellationToken = default)
     {
-        var trackedEntity = context.ChangeTracker.Entries<ChatSession>()
-            .FirstOrDefault(e => e.Entity.Id == chatSession.Id)?.Entity;
-
-        if (trackedEntity != null)
-        {
-        }
-        else
+        ArgumentNullException.ThrowIfNull(chatSession);
+
+        try
         {
-            var existingEntity = await context.ChatSessions
+            var existingEntity = await _context.ChatSessions
                 .Include(x => x.Messages)
                 .FirstOrDefaultAsync(x => x.Id == chatSession.Id, cancellationToken);

-            if (existingEntity != null)
+            if (existingEntity == null)
             {
-                context.Entry(existingEntity).CurrentValues.SetValues(chatSession);
+                throw new InvalidOperationException($"Chat session with ID {chatSession.Id} not found for update");
+            }

-                var existingMessageIds = existingEntity.Messages.Select(m => m.Id).ToHashSet();
-                var newMessages = chatSession.Messages.Where(m => !existingMessageIds.Contains(m.Id)).ToList();
+            // Update scalar properties
+            _context.Entry(existingEntity).CurrentValues.SetValues(chatSession);

-                foreach (var newMessage in newMessages)
-                {
-                    existingEntity.Messages.Add(newMessage);
-                }
+            // Handle messages collection
+            var existingMessageIds = existingEntity.Messages.Select(m => m.Id).ToHashSet();
+            var newMessages = chatSession.Messages.Where(m => !existingMessageIds.Contains(m.Id)).ToList();
+
+            foreach (var newMessage in newMessages)
+            {
+                existingEntity.Messages.Add(newMessage);
             }
-            else
+
+            await _context.SaveChangesAsync(cancellationToken);
+            
+            _logger.LogInformation("Successfully updated chat session {SessionId}", chatSession.Id);
+        }
+        catch (Exception ex)
+        {
+            _logger.LogError(ex, "Error updating chat session {SessionId}", chatSession.Id);
+            throw;
+        }
+    }
+
+    // Additional methods with proper error handling and logging...
+    public async Task<List<ChatSession>> GetByUserIdAsync(string userId, bool includeDeleted = false,
+        int pageSize = 20, int pageNumber = 1, CancellationToken cancellationToken = default)
+    {
+        ArgumentException.ThrowIfNullOrWhiteSpace(userId);
+
+        try
+        {
+            var query = _context.ChatSessions
+                .Where(x => x.UserId == userId)
+                .AsNoTracking();
+
+            if (!includeDeleted)
             {
-                context.ChatSessions.Update(chatSession);
+                query = query.Where(x => x.IsActive);
             }
+
+            var result = await query
+                .OrderByDescending(x => x.UpdatedAt)
+                .Skip((pageNumber - 1) * pageSize)
+                .Take(pageSize)
+                .ToListAsync(cancellationToken);
+
+            return result;
+        }
+        catch (Exception ex)
+        {
+            _logger.LogError(ex, "Error retrieving chat sessions for user {UserId}", userId);
+            throw;
         }
-
-        await context.SaveChangesAsync(cancellationToken);
     }
 }
```

## 5. Entity Framework Configuration Improvements

### `src/ViraceAiChatBot.Infrastructure/Persistence/Configurations/ChatSessionConfiguration.cs`
```diff
 using Microsoft.EntityFrameworkCore;
 using Microsoft.EntityFrameworkCore.Metadata.Builders;
 using ViraceAiChatBot.Domain.Aggregates;

 namespace ViraceAiChatBot.Infrastructure.Persistence.Configurations;

+/// <summary>
+/// Entity Framework configuration for ChatSession aggregate root.
+/// Follows Clean Architecture principles by keeping infrastructure concerns separate from domain.
+/// </summary>
-public class ChatSessionConfiguration : IEntityTypeConfiguration<ChatSession>
+public sealed class ChatSessionConfiguration : IEntityTypeConfiguration<ChatSession>
 {
     public void Configure(EntityTypeBuilder<ChatSession> builder)
     {
+        // Table configuration
         builder.ToTable("ChatSessions");
-
         builder.HasKey(x => x.Id);

-        builder.Property(x => x.Id).HasColumnName("Id")
+        // Primary key configuration
+        builder.Property(x => x.Id)
             .ValueGeneratedOnAdd();

-        builder.Property(x => x.UserId).HasColumnName("UserId")
+        // Business properties with domain constraints
+        builder.Property(x => x.UserId)
             .IsRequired()
             .HasMaxLength(100);

-
-        builder.Property(x => x.Title).HasColumnName("Title")
+        builder.Property(x => x.Title)
             .IsRequired()
             .HasMaxLength(200);

-        builder.Property(x => x.IsActive).HasColumnName("IsActive")
+        builder.Property(x => x.IsActive)
             .IsRequired()
             .HasDefaultValue(true);

-        builder.Property(x => x.LastMessageAt).HasColumnName("LastMessageAt");
+        builder.Property(x => x.LastMessageAt)
+            .IsRequired(false);

-        builder.Property(x => x.CreatedAt).HasColumnName("CreatedAt")
+        // Audit properties
+        builder.Property(x => x.CreatedAt)
             .IsRequired(false);

-        builder.Property(x => x.UpdatedAt).HasColumnName("UpdatedAt")
+        builder.Property(x => x.UpdatedAt)
             .IsRequired(false);
+
+        // Performance indexes
+        builder.HasIndex(x => x.UserId)
+            .HasDatabaseName("ix_chat_sessions_user_id");
+
+        builder.HasIndex(x => new { x.UserId, x.IsActive })
+            .HasDatabaseName("ix_chat_sessions_user_active");
+
+        builder.HasIndex(x => x.LastMessageAt)
+            .HasDatabaseName("ix_chat_sessions_last_message");
+
+        // Configure relationship with messages
+        builder.HasMany(s => s.Messages)
+            .WithOne(m => m.Session)
+            .HasForeignKey(m => m.SessionId)
+            .OnDelete(DeleteBehavior.Cascade);
     }
 }

-public sealed class ChatMessageConfiguration : IEntityTypeConfiguration<ChatMessage>
-{
-    // ... 50+ lines of configuration removed and moved to separate file
-}
```

﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using Pgvector;
using ViraceAiChatBot.Infrastructure.Persistence;

#nullable disable

namespace ViraceAiChatBot.Infrastructure.Migrations
{
    [DbContext(typeof(ChatBotDbContext))]
    [Migration("20250623035325_FixChatMessagePersistence")]
    partial class FixChatMessagePersistence
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.HasPostgresExtension(modelBuilder, "vector");
            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("ViraceAiChatBot.Domain.Aggregates.ChatSession", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("Id");

                    b.Property<DateTimeOffset?>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("CreatedAt");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("IsActive");

                    b.Property<DateTimeOffset?>("LastMessageAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("LastMessageAt");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("Title");

                    b.Property<DateTimeOffset?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("UpdatedAt");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("UserId");

                    b.HasKey("Id")
                        .HasName("pk_chat_sessions");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("ix_chat_sessions_created_at");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_chat_sessions_user_id");

                    b.ToTable("ChatSessions", (string)null);
                });

            modelBuilder.Entity("ViraceAiChatBot.Domain.Entities.RagChunk", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("Id");

                    b.Property<int>("ChunkIndex")
                        .HasColumnType("integer")
                        .HasColumnName("ChunkIndex");

                    b.Property<string>("ColumnName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("ColumnName");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasMaxLength(4000)
                        .HasColumnType("character varying(4000)")
                        .HasColumnName("Content");

                    b.Property<DateTimeOffset?>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("CreatedAt");

                    b.Property<Vector>("Embedding")
                        .IsRequired()
                        .HasColumnType("vector(3072)")
                        .HasColumnName("Embedding");

                    b.Property<string>("Metadata")
                        .HasColumnType("jsonb")
                        .HasColumnName("Metadata");

                    b.Property<string>("RowId")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("RowId");

                    b.Property<string>("TableName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("TableName");

                    b.Property<DateTimeOffset?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("Id")
                        .HasName("pk_rag_chunks");

                    b.HasIndex("Embedding")
                        .HasDatabaseName("ix_rag_chunks_embedding_hnsw");

                    NpgsqlIndexBuilderExtensions.HasMethod(b.HasIndex("Embedding"), "hnsw");
                    NpgsqlIndexBuilderExtensions.HasOperators(b.HasIndex("Embedding"), new[] { "vector_cosine_ops" });

                    b.ToTable("RagChunks", (string)null);
                });

            modelBuilder.Entity("ViraceAiChatBot.Domain.Entities.ReferenceData.CompanyTypes", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("Id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("character varying(8)")
                        .HasColumnName("Code");

                    b.Property<string>("EnName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("EnName");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("Name");

                    b.HasKey("Id")
                        .HasName("pk_company_types");

                    b.HasIndex("Code")
                        .IsUnique()
                        .HasDatabaseName("ix_company_types_code");

                    b.HasIndex("EnName")
                        .HasDatabaseName("ix_company_types_en_name");

                    b.HasIndex("Name")
                        .HasDatabaseName("ix_company_types_name");

                    b.ToTable("CompanyTypes", (string)null);
                });

            modelBuilder.Entity("ViraceAiChatBot.Domain.Entities.ReferenceData.CountryIndex", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("Id");

                    b.Property<string>("Iso")
                        .HasMaxLength(2)
                        .HasColumnType("character varying(2)")
                        .HasColumnName("Iso");

                    b.Property<string>("Iso3")
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)")
                        .HasColumnName("Iso3");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("Name");

                    b.HasKey("Id")
                        .HasName("pk_country_index");

                    b.HasIndex("Iso")
                        .IsUnique()
                        .HasDatabaseName("ix_country_index_iso");

                    b.HasIndex("Iso3")
                        .IsUnique()
                        .HasDatabaseName("ix_country_index_iso3");

                    b.HasIndex("Name")
                        .HasDatabaseName("ix_country_index_name");

                    b.ToTable("CountryIndex", (string)null);
                });

            modelBuilder.Entity("ViraceAiChatBot.Domain.Entities.ReferenceData.CurrencyExchangeRate", b =>
                {
                    b.Property<int>("Year")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("Year");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Year"));

                    b.Property<int>("Value")
                        .HasColumnType("integer")
                        .HasColumnName("Value");

                    b.HasKey("Year")
                        .HasName("pk_currency_exchange_rate");

                    b.HasIndex("Year")
                        .HasDatabaseName("ix_currency_exchange_rate_year");

                    b.ToTable("CurrencyExchangeRate", (string)null);
                });

            modelBuilder.Entity("ViraceAiChatBot.Domain.Entities.ReferenceData.FinancialIndex", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("Id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("EnName")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("EnName");

                    b.Property<string>("Name")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("Name");

                    b.Property<int?>("ParentId")
                        .HasColumnType("integer")
                        .HasColumnName("ParentId");

                    b.Property<string>("Type")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("Type");

                    b.Property<string>("TypeIndex")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("TypeIndex");

                    b.Property<string>("Unit")
                        .HasMaxLength(30)
                        .HasColumnType("character varying(30)")
                        .HasColumnName("Unit");

                    b.HasKey("Id")
                        .HasName("pk_financial_index");

                    b.HasIndex("Name")
                        .HasDatabaseName("ix_financial_index_name");

                    b.HasIndex("ParentId")
                        .HasDatabaseName("ix_financial_index_parent");

                    b.HasIndex("Type")
                        .HasDatabaseName("ix_financial_index_type");

                    b.ToTable("FinancialIndex", (string)null);
                });

            modelBuilder.Entity("ViraceAiChatBot.Domain.Entities.ReferenceData.FinancialStatementIndex", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("Id");

                    b.Property<string>("EnType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("EnType");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("Type");

                    b.Property<string>("TypeIndex")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("TypeIndex");

                    b.HasKey("Id")
                        .HasName("pk_financial_statement_index");

                    b.HasIndex("Type")
                        .HasDatabaseName("ix_financial_statement_index_type");

                    b.HasIndex("TypeIndex")
                        .HasDatabaseName("ix_financial_statement_index_type_index");

                    b.ToTable("FinancialStatementIndex", (string)null);
                });

            modelBuilder.Entity("ViraceAiChatBot.Domain.Entities.ReferenceData.HSCode", b =>
                {
                    b.Property<string>("Code2")
                        .HasMaxLength(2)
                        .HasColumnType("character varying(2)")
                        .HasColumnName("code2");

                    b.Property<string>("Code4")
                        .IsRequired()
                        .HasMaxLength(16)
                        .HasColumnType("character varying(16)")
                        .HasColumnName("code4");

                    b.Property<string>("DescEn")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("descen");

                    b.Property<string>("DescVi")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("descvi");

                    b.HasKey("Code2")
                        .HasName("pk_hs_code");

                    b.HasIndex("Code2")
                        .HasDatabaseName("ix_hs_code_code2");

                    b.HasIndex("Code4")
                        .HasDatabaseName("ix_hs_code_code4");

                    b.ToTable("HSCode", (string)null);
                });

            modelBuilder.Entity("ViraceAiChatBot.Domain.Entities.ReferenceData.LocationIndex", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("Id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("EngName")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("EngName");

                    b.Property<int?>("Level")
                        .HasColumnType("integer")
                        .HasColumnName("Level");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("Name");

                    b.Property<int?>("ParentId")
                        .HasColumnType("integer")
                        .HasColumnName("ParentId");

                    b.HasKey("Id")
                        .HasName("pk_location_index");

                    b.HasIndex("Level")
                        .HasDatabaseName("ix_location_index_level");

                    b.HasIndex("Name")
                        .HasDatabaseName("ix_location_index_name");

                    b.HasIndex("ParentId")
                        .HasDatabaseName("ix_location_index_parent");

                    b.ToTable("LocationIndex", (string)null);
                });

            modelBuilder.Entity("ViraceAiChatBot.Domain.Entities.ReferenceData.OwnershipTypeIndex", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("Id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("EnName")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("EnName");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("Name");

                    b.HasKey("Id")
                        .HasName("pk_ownership_type_index");

                    b.HasIndex("Name")
                        .HasDatabaseName("ix_ownership_type_index_name");

                    b.ToTable("OwnershipTypeIndex", (string)null);
                });

            modelBuilder.Entity("ViraceAiChatBot.Domain.Entities.ReferenceData.RelatedCompanyTypeIndex", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("Id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("EnName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("EnName");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("Name");

                    b.HasKey("Id")
                        .HasName("pk_related_company_type_index");

                    b.HasIndex("Name")
                        .HasDatabaseName("ix_related_company_type_index_name");

                    b.ToTable("RelatedCompanyTypeIndex", (string)null);
                });

            modelBuilder.Entity("ViraceAiChatBot.Domain.Entities.ReferenceData.VSICIndex", b =>
                {
                    b.Property<string>("Code")
                        .HasMaxLength(8)
                        .HasColumnType("character varying(8)")
                        .HasColumnName("Code");

                    b.Property<string>("EnName")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("EnName");

                    b.Property<string>("Name")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("Name");

                    b.HasKey("Code")
                        .HasName("pk_vsic_index");

                    b.HasIndex("Code")
                        .IsUnique()
                        .HasDatabaseName("ix_vsic_index_code");

                    b.HasIndex("EnName")
                        .HasDatabaseName("ix_vsic_index_en_name");

                    b.HasIndex("Name")
                        .HasDatabaseName("ix_vsic_index_name");

                    b.ToTable("VSICIndex", (string)null);
                });

            modelBuilder.Entity("ViraceAiChatBot.Domain.Aggregates.ChatSession", b =>
                {
                    b.OwnsMany("ViraceAiChatBot.Domain.Aggregates.ChatMessage", "Messages", b1 =>
                        {
                            b1.Property<Guid>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("uuid")
                                .HasColumnName("Id");

                            b1.Property<string>("Content")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("Content");

                            b1.Property<DateTimeOffset?>("CreatedAt")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("CreatedAt");

                            b1.Property<string>("FunctionArguments")
                                .HasColumnType("jsonb")
                                .HasColumnName("FunctionArguments");

                            b1.Property<string>("FunctionName")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("FunctionName");

                            b1.Property<string>("Role")
                                .IsRequired()
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("Role");

                            b1.Property<Guid>("SessionId")
                                .HasColumnType("uuid")
                                .HasColumnName("SessionId");

                            b1.Property<int>("TokenCount")
                                .HasColumnType("integer")
                                .HasColumnName("TokenCount");

                            b1.Property<DateTimeOffset?>("UpdatedAt")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("UpdatedAt");

                            b1.HasKey("Id")
                                .HasName("pk_chat_messages");

                            b1.HasIndex("SessionId", "CreatedAt")
                                .HasDatabaseName("ix_chat_messages_session_created");

                            b1.ToTable("ChatMessages", (string)null);

                            b1.WithOwner()
                                .HasForeignKey("SessionId")
                                .HasConstraintName("fk_chat_messages_chat_sessions_session_id");
                        });

                    b.Navigation("Messages");
                });
#pragma warning restore 612, 618
        }
    }
}

namespace ViraceAiChatBot.Application.DTOs.Internal;

/// <summary>
/// Represents the health status of the application.
/// </summary>
/// <param name="ServiceStartTime">The timestamp when the service started.</param>
/// <param name="Uptime">The duration for which the service has been running.</param>
/// <param name="Providers">The list of statuses for various providers used by the application.</param>
public record HealthStatus(
    DateTime ServiceStartTime,
    TimeSpan Uptime,
    List<ProviderStatus> Providers
);

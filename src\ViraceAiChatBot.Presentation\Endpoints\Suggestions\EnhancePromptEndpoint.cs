using Microsoft.AspNetCore.Mvc;
using MediatR;
using ViraceAiChatBot.Application.Commands;
using ViraceAiChatBot.Application.Dtos;

namespace ViraceAiChatBot.Presentation.Endpoints.Suggestions;

/// <summary>
/// Endpoint x<PERSON> lý cải thiện prompt
/// </summary>
public static class EnhancePromptEndpoint
{
    /// <summary>
    /// Map Enhance Prompt endpoint
    /// </summary>
    /// <param name="group">Route group</param>
    public static void MapEnhancePromptEndpoint(this RouteGroupBuilder group)
    {
        group.MapPost("/suggestions/enhance-prompt", async (
                [FromBody] EnhancedPromptRequest request,
                IMediator mediator,
                CancellationToken cancellationToken) =>
            {
                try
                {
                    var command = new EnhancePromptCommand(
                        request.UserId,
                        request.SessionId,
                        request.OriginalPrompt,
                        request.IntendedContext,
                        request.IncludeExamples);

                    var result = await mediator.Send(command, cancellationToken);
                    return Results.Ok(result);
                }
                catch (Exception ex)
                {
                    return Results.BadRequest(new { error = ex.Message });
                }
            })
            .WithName("EnhancePrompt")
            .WithSummary("Enhance user prompt for better search results")
            .Produces<EnhancedPromptResponse>(200)
            .Produces<object>(400);
    }
}

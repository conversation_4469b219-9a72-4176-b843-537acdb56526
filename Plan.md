# RAG Chatbot with PostgreSQL (pgvector) – .NET Clean Architecture

> **Purpose**
> Provide a comprehensive, step‑by‑step plan for building a retrieval‑augmented‑generation chatbot using **ASP.NET Core 9.0 Minimal APIs**, **Clean Architecture**, PostgreSQL with **pgvector**, and **IdentityServer 4** authentication. The system draws knowledge from multiple PostgreSQL tables, stores embeddings in pgvector, and exposes OpenAI-compatible function calls.

---

## 1 · Objectives

- **Answer quality** – Use semantic search (pgvector) + context injection to reduce hallucination.
- **Multi‑table coverage** – Index any number of source tables without changing business schemas.
- **Clean Architecture** – Separate Domain, Application, Infrastructure, and Presentation layers.
- **Modern .NET Stack** – ASP.NET Core 9.0, EF Core 9.0, PostgreSQL with Npgsql.
- **Enterprise Security** – IdentityServer 4 authentication with JWT bearer tokens.
- **Operational safety** – Support RBAC/tenant filtering, observability, automated data refresh, and CI/CD.

---

## 2 · Clean Architecture Layers

```
┌─────────────────────────────────────────────────────────────────┐
│                         Presentation Layer                      │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   Chat API      │  │  Retrieval API  │  │   Health API    │  │
│  │ (Minimal APIs)  │  │ (Minimal APIs)  │  │ (Minimal APIs)  │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────────┐
│                        Application Layer                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   Chat Service  │  │Embedding Service│  │Retrieval Service│  │
│  │   (CQRS/MediatR)│  │   (Commands)    │  │   (Queries)     │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────────┐
│                       Infrastructure Layer                      │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   EF Core       │  │   OpenAI Client │  │  IdentityServer │  │
│  │   (Npgsql)      │  │   (HTTP Client) │  │    (JWT Auth)   │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────────┐
│                         Domain Layer                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   RagChunk      │  │   ChatSession   │  │  EmbeddingJob   │  │
│  │   (Entity)      │  │   (Aggregate)   │  │   (Entity)      │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

---

## 3 · Technology Stack (.NET 9.0)

### Core Dependencies
```xml
<PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.*" />
<PackageReference Include="Scalar.AspNetCore" Version="1.*" />
<PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.*" />
<PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.*" />
<PackageReference Include="Duende.IdentityServer" Version="4.*" />
<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.*" />
<PackageReference Include="MediatR" Version="14.*" />
<PackageReference Include="FluentValidation.AspNetCore" Version="11.*" />
<PackageReference Include="Polly" Version="8.*" />
<PackageReference Include="Serilog.AspNetCore" Version="8.*" />
```

### Project Structure
```
src/
├── ViraceAiChatBot.Domain/
│   ├── Entities/
│   ├── Aggregates/
│   ├── ValueObjects/
│   └── Interfaces/
├── ViraceAiChatBot.Application/
│   ├── Commands/
│   ├── Queries/
│   ├── DTOs/
│   ├── Interfaces/
│   └── Validators/
├── ViraceAiChatBot.Infrastructure/
│   ├── Persistence/
│   ├── ExternalServices/
│   ├── Authentication/
│   └── Migrations/
└── ViraceAiChatBot.Presentation/
    ├── Program.cs
    ├── Extensions/
    └── Endpoints/
```

---

## 4 · Data Model (EF Core)

### Domain Entities

```csharp
// Domain/Entities/RagChunk.cs
public class RagChunk
{
    public long Id { get; set; }
    public required string TableName { get; set; }
    public required string RowId { get; set; }
    public required string ColumnName { get; set; }
    public int ChunkIndex { get; set; }
    public required string Content { get; set; }
    public required float[] Embedding { get; set; }
    public string? Metadata { get; set; }
    public DateTimeOffset CreatedAt { get; set; }
}

// Domain/Aggregates/ChatSession.cs
public class ChatSession
{
    public Guid Id { get; set; }
    public required string UserId { get; set; }
    public required string Title { get; set; }
    public List<ChatMessage> Messages { get; set; } = [];
    public DateTimeOffset CreatedAt { get; set; }
    public string? TenantId { get; set; }
}
```

### EF Core Configuration

```csharp
// Infrastructure/Persistence/Configurations/RagChunkConfiguration.cs
public class RagChunkConfiguration : IEntityTypeConfiguration<RagChunk>
{
    public void Configure(EntityTypeBuilder<RagChunk> builder)
    {
        builder.ToTable("rag_chunks");

        builder.HasKey(x => x.Id);
        builder.Property(x => x.Id).HasColumnName("id");

        builder.Property(x => x.TableName).HasColumnName("table_name").IsRequired();
        builder.Property(x => x.RowId).HasColumnName("row_id").IsRequired();
        builder.Property(x => x.ColumnName).HasColumnName("col_name").IsRequired();
        builder.Property(x => x.ChunkIndex).HasColumnName("chunk_index");
        builder.Property(x => x.Content).HasColumnName("content").IsRequired();

        // pgvector configuration
        builder.Property(x => x.Embedding)
            .HasColumnName("embedding")
            .HasColumnType("vector(1536)");

        builder.Property(x => x.Metadata).HasColumnName("metadata").HasColumnType("jsonb");
        builder.Property(x => x.CreatedAt).HasColumnName("created_at");
        builder.Property(x => x.TenantId).HasColumnName("tenant_id");

        // HNSW Index will be created via migration
        builder.HasIndex(x => x.Embedding).HasMethod("hnsw");
    }
}
```

---

## 5 · Application Layer (CQRS with MediatR)

### Commands & Queries

```csharp
// Application/Commands/ProcessEmbeddingCommand.cs
public record ProcessEmbeddingCommand(
    string TableName,
    string RowId,
    string Content,
    string? TenantId = null
) : IRequest<Result>;

// Application/Queries/RetrieveChunksQuery.cs
public record RetrieveChunksQuery(
    string Query,
    int K = 8,
    string[]? Tables = null,
    string? TenantId = null
) : IRequest<List<RetrievedChunk>>;

// Application/Commands/ChatCompletionCommand.cs
public record ChatCompletionCommand(
    string UserId,
    string Message,
    Guid? SessionId = null,
    string? TenantId = null
) : IRequest<ChatCompletionResponse>;
```

### Handlers

```csharp
// Application/Handlers/ChatCompletionHandler.cs
public class ChatCompletionHandler : IRequestHandler<ChatCompletionCommand, ChatCompletionResponse>
{
    private readonly IRetrievalService _retrievalService;
    private readonly IOpenAiService _openAiService;
    private readonly IChatRepository _chatRepository;

    public async Task<ChatCompletionResponse> Handle(ChatCompletionCommand request, CancellationToken cancellationToken)
    {
        // 1. Retrieve relevant chunks
        var chunks = await _retrievalService.RetrieveAsync(request.Message, request.TenantId, cancellationToken);

        // 2. Build context-aware prompt
        var prompt = BuildPrompt(request.Message, chunks);

        // 3. Call OpenAI with function calling
        var response = await _openAiService.ChatCompletionAsync(prompt, cancellationToken);

        // 4. Handle function calls if present
        if (response.FunctionCall != null)
        {
            var functionResult = await ExecuteFunctionAsync(response.FunctionCall, cancellationToken);
            response = await _openAiService.ChatCompletionAsync(prompt + functionResult, cancellationToken);
        }

        // 5. Save chat session
        await _chatRepository.SaveChatAsync(request.UserId, request.Message, response.Content, request.SessionId, cancellationToken);

        return response;
    }
}
```

---

## 6 · Infrastructure Layer

### Repository Implementation

```csharp
// Infrastructure/Persistence/Repositories/RagChunkRepository.cs
public class RagChunkRepository : IRagChunkRepository
{
    private readonly ChatBotDbContext _context;

    public async Task<List<RetrievedChunk>> RetrieveChunksAsync(
        float[] queryEmbedding,
        int k,
        string? tenantId,
        string[]? tableFilter,
        CancellationToken cancellationToken)
    {
        var query = _context.RagChunks.AsQueryable();

        if (!string.IsNullOrEmpty(tenantId))
            query = query.Where(x => x.TenantId == tenantId);

        if (tableFilter?.Length > 0)
            query = query.Where(x => tableFilter.Contains(x.TableName));

        // pgvector cosine similarity
        var results = await query
            .OrderBy(x => x.Embedding.CosineDistance(queryEmbedding))
            .Take(k)
            .Select(x => new RetrievedChunk
            {
                Content = x.Content,
                TableName = x.TableName,
                RowId = x.RowId,
                Metadata = x.Metadata,
                Score = x.Embedding.CosineDistance(queryEmbedding)
            })
            .ToListAsync(cancellationToken);

        return results;
    }
}
```

### External Services

```csharp
// Infrastructure/ExternalServices/OpenAiService.cs
public class OpenAiService : IOpenAiService
{
    private readonly HttpClient _httpClient;
    private readonly OpenAiOptions _options;

    public OpenAiService(HttpClient httpClient, IOptions<OpenAiOptions> options)
    {
        _httpClient = httpClient;
        _options = options.Value;

        _httpClient.DefaultRequestHeaders.Authorization =
            new AuthenticationHeaderValue("Bearer", _options.ApiKey);
    }

    public async Task<ChatCompletionResponse> ChatCompletionAsync(
        string prompt,
        CancellationToken cancellationToken)
    {
        var request = new
        {
            model = "gpt-4o",
            messages = new[] { new { role = "user", content = prompt } },
            functions = GetAvailableFunctions(),
            function_call = "auto"
        };

        var response = await _httpClient.PostAsJsonAsync("/v1/chat/completions", request, cancellationToken);
        response.EnsureSuccessStatusCode();

        var result = await response.Content.ReadFromJsonAsync<OpenAiChatResponse>(cancellationToken);
        return MapToResponse(result);
    }

    public async IAsyncEnumerable<string> StreamChatCompletionAsync(
        string prompt,
        [EnumeratorCancellation] CancellationToken cancellationToken)
    {
        var request = new
        {
            model = "gpt-4o",
            messages = new[] { new { role = "user", content = prompt } },
            stream = true
        };

        var response = await _httpClient.PostAsJsonAsync("/v1/chat/completions", request, cancellationToken);
        response.EnsureSuccessStatusCode();

        await using var stream = await response.Content.ReadAsStreamAsync(cancellationToken);
        using var reader = new StreamReader(stream);

        while (!reader.EndOfStream && !cancellationToken.IsCancellationRequested)
        {
            var line = await reader.ReadLineAsync();
            if (line?.StartsWith("data: ") == true)
            {
                var json = line[6..];
                if (json != "[DONE]")
                {
                    var chunk = JsonSerializer.Deserialize<OpenAiStreamChunk>(json);
                    if (chunk?.Choices?[0]?.Delta?.Content != null)
                    {
                        yield return chunk.Choices[0].Delta.Content;
                    }
                }
            }
        }
    }
}
```

---

## 7 · Presentation Layer (Minimal APIs)

### Program.cs

```csharp
var builder = WebApplication.CreateBuilder(args);

// Add services
builder.Services.AddOpenApi();
builder.Services.AddDbContext<ChatBotDbContext>(options =>
    options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection")));

// Authentication
builder.Services.AddAuthentication("Bearer")
    .AddJwtBearer("Bearer", options =>
    {
        options.Authority = builder.Configuration["IdentityServer:Authority"];
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateAudience = false
        };
    });

builder.Services.AddAuthorization();

// MediatR
builder.Services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(ChatCompletionCommand).Assembly));

// HTTP Clients with Polly
builder.Services.AddHttpClient<IOpenAiService, OpenAiService>(client =>
{
    client.BaseAddress = new Uri(builder.Configuration["OpenAi:BaseUrl"]!);
})
.AddPolicyHandler(GetRetryPolicy());

// Repositories
builder.Services.AddScoped<IRagChunkRepository, RagChunkRepository>();
builder.Services.AddScoped<IChatRepository, ChatRepository>();

var app = builder.Build();

// Configure pipeline
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
    app.MapScalarApiReference();
}

app.UseAuthentication();
app.UseAuthorization();

// Map endpoint groups
app.MapChatEndpoints();
app.MapRetrievalEndpoints();
app.MapHealthEndpoints();

await app.RunAsync();

static IAsyncPolicy<HttpResponseMessage> GetRetryPolicy()
{
    return Policy
        .HandleResult<HttpResponseMessage>(r => !r.IsSuccessStatusCode)
        .WaitAndRetryAsync(
            retryCount: 3,
            sleepDurationProvider: retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)),
            onRetry: (outcome, timespan, retryCount, context) =>
            {
                // Log retry attempts
            });
}
```

### Endpoint Extensions

```csharp
// Presentation/Extensions/ChatEndpoints.cs
public static class ChatEndpoints
{
    public static void MapChatEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/v1/chat")
            .WithTags("Chat")
            .RequireAuthorization();

        group.MapPost("/completions", async (
            ChatCompletionRequest request,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var command = new ChatCompletionCommand(
                request.UserId,
                request.Message,
                request.SessionId,
                request.TenantId);

            var response = await mediator.Send(command, cancellationToken);
            return Results.Ok(response);
        })
        .WithName("ChatCompletion")
        .WithSummary("Generate chat completion with RAG context");

        group.MapPost("/completions/stream", async (
            ChatCompletionRequest request,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var command = new StreamChatCompletionCommand(
                request.UserId,
                request.Message,
                request.SessionId,
                request.TenantId);

            var stream = mediator.CreateStream(command, cancellationToken);

            return Results.Stream(async (responseStream, ct) =>
            {
                await foreach (var chunk in stream.WithCancellation(ct))
                {
                    await responseStream.WriteAsync(Encoding.UTF8.GetBytes($"data: {JsonSerializer.Serialize(chunk)}\n\n"), ct);
                    await responseStream.FlushAsync(ct);
                }
                await responseStream.WriteAsync(Encoding.UTF8.GetBytes("data: [DONE]\n\n"), ct);
            }, "text/event-stream");
        })
        .WithName("StreamChatCompletion")
        .WithSummary("Stream chat completion with RAG context");
    }
}
```

---

## 8 · Security & Authentication

### IdentityServer 4 Integration

```csharp
// Infrastructure/Authentication/JwtConfiguration.cs
public static class JwtConfiguration
{
    public static void AddJwtAuthentication(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddAuthentication("Bearer")
            .AddJwtBearer("Bearer", options =>
            {
                options.Authority = configuration["IdentityServer:Authority"];
                options.RequireHttpsMetadata = !Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT").Equals("Development");

                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateAudience = false,
                    ValidateIssuer = true,
                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.FromMinutes(5)
                };

                options.Events = new JwtBearerEvents
                {
                    OnTokenValidated = context =>
                    {
                        // Extract tenant information from claims
                        var tenantId = context.Principal?.FindFirst("tenant_id")?.Value;
                        if (!string.IsNullOrEmpty(tenantId))
                        {
                            context.HttpContext.Items["TenantId"] = tenantId;
                        }
                        return Task.CompletedTask;
                    }
                };
            });
    }
}
```

### Tenant Isolation

```csharp
// Application/Services/TenantService.cs
public class TenantService : ITenantService
{
    private readonly IHttpContextAccessor _httpContextAccessor;

    public string? GetCurrentTenantId()
    {
        return _httpContextAccessor.HttpContext?.Items["TenantId"]?.ToString();
    }
}

// Infrastructure/Persistence/Interceptors/TenantInterceptor.cs
public class TenantInterceptor : SaveChangesInterceptor
{
    private readonly ITenantService _tenantService;

    public override ValueTask<InterceptionResult<int>> SavingChangesAsync(
        DbContextEventData eventData,
        InterceptionResult<int> result,
        CancellationToken cancellationToken = default)
    {
        var tenantId = _tenantService.GetCurrentTenantId();

        if (eventData.Context != null && !string.IsNullOrEmpty(tenantId))
        {

        }

        return base.SavingChangesAsync(eventData, result, cancellationToken);
    }
}
```

---

## 9 · Embedding Pipeline (Background Service)

```csharp
// Infrastructure/BackgroundServices/EmbeddingProcessorService.cs
public class EmbeddingProcessorService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<EmbeddingProcessorService> _logger;
    private readonly EmbeddingOptions _options;

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var processor = scope.ServiceProvider.GetRequiredService<IEmbeddingProcessor>();

                await processor.ProcessPendingEmbeddingsAsync(stoppingToken);

                await Task.Delay(_options.ProcessingInterval, stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing embeddings");
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
            }
        }
    }
}

// Application/Services/EmbeddingProcessor.cs
public class EmbeddingProcessor : IEmbeddingProcessor
{
    private readonly IEmbeddingService _embeddingService;
    private readonly IRagChunkRepository _repository;
    private readonly ILogger<EmbeddingProcessor> _logger;

    public async Task ProcessPendingEmbeddingsAsync(CancellationToken cancellationToken)
    {
        // Read from configured source tables
        var sourceConfigs = await GetSourceTableConfigsAsync(cancellationToken);

        foreach (var config in sourceConfigs)
        {
            await ProcessTableAsync(config, cancellationToken);
        }
    }

    private async Task ProcessTableAsync(SourceTableConfig config, CancellationToken cancellationToken)
    {
        var lastProcessedId = await _repository.GetLastProcessedIdAsync(config.TableName, cancellationToken);
        var batchSize = 100;

        while (!cancellationToken.IsCancellationRequested)
        {
            var rows = await GetRowsAsync(config, lastProcessedId, batchSize, cancellationToken);
            if (!rows.Any()) break;

            var tasks = rows.Select(async row =>
            {
                var chunks = ChunkText(row.Content, maxTokens: 400);

                for (int i = 0; i < chunks.Count; i++)
                {
                    var embedding = await _embeddingService.GetEmbeddingAsync(chunks[i], cancellationToken);

                    await _repository.UpsertChunkAsync(new RagChunk
                    {
                        TableName = config.TableName,
                        RowId = row.Id,
                        ColumnName = config.TextColumn,
                        ChunkIndex = i,
                        Content = chunks[i],
                        Embedding = embedding,
                        Metadata = JsonSerializer.Serialize(row.Metadata)
                    }, cancellationToken);
                }
            });

            await Task.WhenAll(tasks);
            lastProcessedId = rows.Max(r => r.Id);
        }
    }
}
```

---

## 10 · Function Calling Framework

```csharp
// Domain/Functions/FunctionDefinition.cs
public record FunctionDefinition(
    string Name,
    string Description,
    Dictionary<string, object> Parameters
);

// Application/Functions/AvailableFunctions.cs
public static class AvailableFunctions
{
    public static List<FunctionDefinition> GetFunctions() => new()
    {
        new FunctionDefinition(
            "search_knowledge_base",
            "Search the company knowledge base for specific information",
            new Dictionary<string, object>
            {
                ["type"] = "object",
                ["properties"] = new Dictionary<string, object>
                {
                    ["query"] = new { type = "string", description = "Search query" },
                    ["tables"] = new { type = "array", items = new { type = "string" }, description = "Specific tables to search" }
                },
                ["required"] = new[] { "query" }
            }
        ),
        new FunctionDefinition(
            "get_user_profile",
            "Retrieve user profile information",
            new Dictionary<string, object>
            {
                ["type"] = "object",
                ["properties"] = new Dictionary<string, object>
                {
                    ["user_id"] = new { type = "string", description = "User identifier" }
                },
                ["required"] = new[] { "user_id" }
            }
        )
    };
}

// Application/Functions/FunctionExecutor.cs
public class FunctionExecutor : IFunctionExecutor
{
    private readonly IServiceProvider _serviceProvider;

    public async Task<string> ExecuteAsync(string functionName, Dictionary<string, object> parameters, CancellationToken cancellationToken)
    {
        return functionName switch
        {
            "search_knowledge_base" => await SearchKnowledgeBase(parameters, cancellationToken),
            "get_user_profile" => await GetUserProfile(parameters, cancellationToken),
            _ => throw new NotSupportedException($"Function '{functionName}' is not supported")
        };
    }

    private async Task<string> SearchKnowledgeBase(Dictionary<string, object> parameters, CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

        var query = parameters["query"].ToString()!;
        var tables = parameters.ContainsKey("tables")
            ? ((JsonElement)parameters["tables"]).EnumerateArray().Select(x => x.GetString()!).ToArray()
            : null;

        var command = new RetrieveChunksQuery(query, K: 5, tables);
        var results = await mediator.Send(command, cancellationToken);

        return JsonSerializer.Serialize(results);
    }
}
```

---

## 11 · Deployment & DevOps

### Docker Configuration

```dockerfile
# Dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app
EXPOSE 8080
EXPOSE 8081

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src
COPY ["src/ViraceAiChatBot.Presentation/ViraceAiChatBot.Presentation.csproj", "src/ViraceAiChatBot.Presentation/"]
COPY ["src/ViraceAiChatBot.Application/ViraceAiChatBot.Application.csproj", "src/ViraceAiChatBot.Application/"]
COPY ["src/ViraceAiChatBot.Infrastructure/ViraceAiChatBot.Infrastructure.csproj", "src/ViraceAiChatBot.Infrastructure/"]
COPY ["src/ViraceAiChatBot.Domain/ViraceAiChatBot.Domain.csproj", "src/ViraceAiChatBot.Domain/"]

RUN dotnet restore "src/ViraceAiChatBot.Presentation/ViraceAiChatBot.Presentation.csproj"
COPY . .
WORKDIR "/src/src/ViraceAiChatBot.Presentation"
RUN dotnet build "ViraceAiChatBot.Presentation.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "ViraceAiChatBot.Presentation.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "ViraceAiChatBot.Presentation.dll"]
```

### docker-compose.yml

```yaml
services:
  chatbot-api:
    build: .
    ports:
      - "8080:8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=viraceai_chatbot;Username=postgres;Password=postgres
      - IdentityServer__Authority=https://your-identity-server.com
      - OpenAi__BaseUrl=https://api.openai.com
      - OpenAi__ApiKey=${OPENAI_API_KEY}
    depends_on:
      - postgres

  postgres:
    image: pgvector/pgvector:pg16
    environment:
      - POSTGRES_DB=viraceai_chatbot
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql

volumes:
  postgres_data:
```

---

## 12 · Testing Strategy

### Unit Tests

```csharp
// Tests/ViraceAiChatBot.Tests.Unit/Handlers/ChatCompletionHandlerTests.cs
public class ChatCompletionHandlerTests
{
    private readonly Mock<IRetrievalService> _retrievalServiceMock;
    private readonly Mock<IOpenAiService> _openAiServiceMock;
    private readonly Mock<IChatRepository> _chatRepositoryMock;
    private readonly ChatCompletionHandler _handler;

    [Fact]
    public async Task Handle_WithValidRequest_ReturnsResponse()
    {
        // Arrange
        var command = new ChatCompletionCommand("user123", "What is the weather?");
        var chunks = new List<RetrievedChunk> { /* test data */ };
        var expectedResponse = new ChatCompletionResponse { Content = "Test response" };

        _retrievalServiceMock.Setup(x => x.RetrieveAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(chunks);
        _openAiServiceMock.Setup(x => x.ChatCompletionAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Content.Should().Be("Test response");
    }
}
```

### Integration Tests

```csharp
// Tests/ViraceAiChatBot.Tests.Integration/ChatEndpointsTests.cs
public class ChatEndpointsTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;

    public ChatEndpointsTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
    }

    [Fact]
    public async Task PostChatCompletion_WithValidRequest_ReturnsOk()
    {
        // Arrange
        var request = new ChatCompletionRequest
        {
            UserId = "test-user",
            Message = "Hello, world!"
        };

        // Act
        var response = await _client.PostAsJsonAsync("/v1/chat/completions", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        var content = await response.Content.ReadFromJsonAsync<ChatCompletionResponse>();
        content.Should().NotBeNull();
    }
}
```

---

## 13 · Monitoring & Observability

### Structured Logging

```csharp
// Infrastructure/Logging/LoggingConfiguration.cs
public static class LoggingConfiguration
{
    public static void AddStructuredLogging(this IServiceCollection services, IConfiguration configuration)
    {
        Log.Logger = new LoggerConfiguration()
            .ReadFrom.Configuration(configuration)
            .Enrich.FromLogContext()
            .Enrich.WithMachineName()
            .Enrich.WithEnvironmentName()
            .WriteTo.Console(new JsonFormatter())
            .WriteTo.Seq(configuration.GetConnectionString("Seq") ?? "http://localhost:5341")
            .CreateLogger();

        services.AddSerilog();
    }
}
```

### Health Checks

```csharp
// Presentation/Extensions/HealthEndpoints.cs
public static class HealthEndpoints
{
    public static void MapHealthEndpoints(this IEndpointRouteBuilder app)
    {
        app.MapHealthChecks("/health", new HealthCheckOptions
        {
            ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
        });

        app.MapHealthChecks("/health/ready", new HealthCheckOptions
        {
            Predicate = check => check.Tags.Contains("ready"),
            ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
        });

        app.MapHealthChecks("/health/live", new HealthCheckOptions
        {
            Predicate = _ => false,
            ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
        });
    }
}

// Program.cs health check registration
builder.Services.AddHealthChecks()
    .AddNpgSql(builder.Configuration.GetConnectionString("DefaultConnection")!, tags: new[] { "ready" })
    .AddHttpClient(options =>
    {
        options.AddUri(new Uri(builder.Configuration["OpenAi:BaseUrl"]!));
    }, tags: new[] { "ready" });
```

---

## 14 · Performance Optimization

### Caching Strategy

```csharp
// Infrastructure/Caching/CacheService.cs
public class CacheService : ICacheService
{
    private readonly IMemoryCache _memoryCache;
    private readonly IDistributedCache _distributedCache;

    public async Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default)
    {
        // L1: Memory cache
        if (_memoryCache.TryGetValue(key, out T? value))
            return value;

        // L2: Distributed cache (Redis)
        var distributedValue = await _distributedCache.GetStringAsync(key, cancellationToken);
        if (distributedValue != null)
        {
            value = JsonSerializer.Deserialize<T>(distributedValue);
            _memoryCache.Set(key, value, TimeSpan.FromMinutes(5));
            return value;
        }

        return default;
    }

    public async Task SetAsync<T>(string key, T value, TimeSpan expiry, CancellationToken cancellationToken = default)
    {
        // Set in both caches
        _memoryCache.Set(key, value, expiry);

        var serialized = JsonSerializer.Serialize(value);
        await _distributedCache.SetStringAsync(key, serialized, new DistributedCacheEntryOptions
        {
            AbsoluteExpirationRelativeToNow = expiry
        }, cancellationToken);
    }
}
```

### Database Optimization

```sql
-- Performance indexes for rag_chunks table

CREATE INDEX CONCURRENTLY idx_rag_chunks_created_at
ON rag_chunks (created_at DESC);

-- Partition table by tenant for large datasets
CREATE TABLE rag_chunks_partitioned (
    LIKE rag_chunks INCLUDING ALL
) PARTITION BY HASH (tenant_id);

-- Create partitions
CREATE TABLE rag_chunks_part_0 PARTITION OF rag_chunks_partitioned
FOR VALUES WITH (modulus 4, remainder 0);

CREATE TABLE rag_chunks_part_1 PARTITION OF rag_chunks_partitioned
FOR VALUES WITH (modulus 4, remainder 1);

-- Continue for remainder 2 and 3...
```

---

## 15 · Security Hardening

### Rate Limiting

```csharp
// Infrastructure/Middleware/RateLimitingMiddleware.cs
public class RateLimitingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly IMemoryCache _cache;
    private readonly RateLimitOptions _options;

    public async Task InvokeAsync(HttpContext context)
    {
        var key = GetClientIdentifier(context);
        var windowStart = DateTime.UtcNow.Ticks / _options.WindowTicks * _options.WindowTicks;
        var cacheKey = $"rate_limit:{key}:{windowStart}";

        var count = _cache.GetOrCreate(cacheKey, entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = _options.Window;
            return 0;
        });

        if (count >= _options.Limit)
        {
            context.Response.StatusCode = 429;
            await context.Response.WriteAsync("Rate limit exceeded");
            return;
        }

        _cache.Set(cacheKey, count + 1, _options.Window);
        await _next(context);
    }
}
```

### Input Validation

```csharp
// Application/Validators/ChatCompletionValidator.cs
public class ChatCompletionValidator : AbstractValidator<ChatCompletionCommand>
{
    public ChatCompletionValidator()
    {
        RuleFor(x => x.UserId)
            .NotEmpty()
            .MaximumLength(100)
            .Matches(@"^[a-zA-Z0-9_-]+$");

        RuleFor(x => x.Message)
            .NotEmpty()
            .MaximumLength(10000)
            .Must(NotContainMaliciousContent)
            .WithMessage("Message contains prohibited content");


    }

    private bool NotContainMaliciousContent(string message)
    {
        var prohibitedPatterns = new[]
        {
            @"<script[^>]*>.*?</script>",
            @"javascript:",
            @"on\w+\s*=",
            @"eval\s*\(",
            @"exec\s*\("
        };

        return !prohibitedPatterns.Any(pattern =>
            Regex.IsMatch(message, pattern, RegexOptions.IgnoreCase));
    }
}
```

---

## 16 · Future Enhancements

### Planned Features

1. **Hybrid Search** – Combine full-text search with vector similarity
2. **Multi-modal Support** – Handle images and documents with pgvector
3. **Fine-tuning Pipeline** – Custom model training based on user feedback
4. **Advanced Analytics** – Query analysis, usage patterns, and performance metrics
5. **Federated Search** – Query multiple knowledge bases simultaneously
6. **Conversation Memory** – Long-term context awareness across sessions

### Architecture Evolution

```mermaid
graph TB
    A[Current: Single API] --> B[Phase 2: Microservices]
    B --> C[Phase 3: Event-Driven]
    C --> D[Phase 4: AI-Native]

    B1[Chat Service] --> B
    B2[Embedding Service] --> B
    B3[Retrieval Service] --> B

    C1[Event Bus] --> C
    C2[CQRS/ES] --> C
    C3[Saga Pattern] --> C

    D1[Agent Framework] --> D
    D2[Multi-Agent System] --> D
    D3[Auto-scaling AI] --> D
```

---

### References

- [pgvector Documentation](https://github.com/pgvector/pgvector)
- [ASP.NET Core 9.0 Documentation](https://docs.microsoft.com/en-us/aspnet/core/)
- [Clean Architecture by Robert Martin](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html)
- [EF Core 9.0 Documentation](https://docs.microsoft.com/en-us/ef/core/)
- [IdentityServer4 Documentation](https://identityserver4.readthedocs.io/)
- [OpenAI API Documentation](https://platform.openai.com/docs/api-reference)

---

*This plan provides a comprehensive, production-ready architecture for building a RAG chatbot using modern .NET technologies and Clean Architecture principles.*

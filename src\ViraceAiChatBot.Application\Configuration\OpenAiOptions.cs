namespace ViraceAiChatBot.Application.Configuration;

/// <summary>
/// Represents configuration options for OpenAI integration.
/// </summary>
public class OpenAiOptions
{
    /// <summary>
    /// Gets or sets the API key used for authentication with OpenAI.
    /// </summary>
    public string Api<PERSON>ey { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the base URL for the OpenAI API.
    /// </summary>
    public string BaseUrl { get; set; } = "https://localhost:7289";

    /// <summary>
    /// Gets or sets the default provider to use for OpenAI services.
    /// </summary>
    public string DefaultProvider { get; set; } = "openai";

    /// <summary>
    /// Gets or sets the default model to use for generating responses.
    /// </summary>
    public string DefaultModel { get; set; } = "gpt-4o";

    /// <summary>
    /// Gets or sets the default embedding model to use for text embeddings.
    /// </summary>
    public string DefaultEmbeddingModel { get; set; } = "text-embedding-3-large";

    /// <summary>
    /// Gets or sets the maximum number of tokens allowed in a response.
    /// </summary>
    public int MaxTokens { get; set; } = 4000;

    /// <summary>
    /// Gets or sets the temperature value for response generation, controlling randomness.
    /// </summary>
    public double Temperature { get; set; } = 0.7;

    /// <summary>
    /// Gets or sets the streaming configuration options.
    /// </summary>
    public StreamingOptions Streaming { get; set; } = new StreamingOptions();
}

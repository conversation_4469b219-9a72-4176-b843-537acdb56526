namespace ViraceAiChatBot.Domain.Entities.ReferenceData;

public class HSCode
{
    public string? Code2 { get; init; }
    public string? Code4 { get; init; }
    public string? DescVi { get; init; }
    public string? DescEn { get; init; }

    // Parameterless constructor for EF Core
    private HSCode() { }

    public HSCode(string code2, string code4, string descVi, string descEn)
    {
        Code2 = code2 ?? throw new ArgumentNullException(nameof(code2));
        Code4 = code4 ?? throw new ArgumentNullException(nameof(code4));
        DescVi = descVi ?? throw new ArgumentNullException(nameof(descVi));
        DescEn = descEn ?? throw new ArgumentNullException(nameof(descEn));

        ValidateInvariants();
    }

    private void ValidateInvariants()
    {
        if (string.IsNullOrWhiteSpace(Code2))
            throw new ArgumentException("Code2 cannot be empty", nameof(Code2));

        if (Code2.Length > 2)
            throw new ArgumentException("Code2 cannot exceed 2 characters", nameof(Code2));

        if (string.IsNullOrWhiteSpace(Code4))
            throw new ArgumentException("Code4 cannot be empty", nameof(Code4));

        if (Code4.Length > 16)
            throw new ArgumentException("Code4 cannot exceed 16 characters", nameof(Code4));

        if (string.IsNullOrWhiteSpace(DescVi))
            throw new ArgumentException("DescVi cannot be empty", nameof(DescVi));

        if (DescVi.Length > 1000)
            throw new ArgumentException("DescVi cannot exceed 1000 characters", nameof(DescVi));

        if (string.IsNullOrWhiteSpace(DescEn))
            throw new ArgumentException("DescEn cannot be empty", nameof(DescEn));

        if (DescEn.Length > 1000)
            throw new ArgumentException("DescEn cannot exceed 1000 characters", nameof(DescEn));
    }

    public string GetSearchableContent()
    {
        return $"{Code2} {Code4} {DescVi} {DescEn}";
    }

    public override string ToString()
    {
        return $"HSCode [Code2: {Code2}, Code4: {Code4}, DescEn: {DescEn}]";
    }
}

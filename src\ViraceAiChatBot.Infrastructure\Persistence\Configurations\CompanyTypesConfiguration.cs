using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ViraceAiChatBot.Domain.Entities.ReferenceData;

namespace ViraceAiChatBot.Infrastructure.Persistence.Configurations;

public class CompanyTypesConfiguration : IEntityTypeConfiguration<CompanyTypes>
{
    public void Configure(EntityTypeBuilder<CompanyTypes> builder)
    {
        builder.ToTable("CompanyTypes");

        builder.<PERSON><PERSON><PERSON>(x => x.Id);

        builder.Property(x => x.Id)
            .HasColumnName("Id")
            .ValueGeneratedOnAdd();

        builder.Property(x => x.Code)
            .HasColumnName("Code")
            .IsRequired()
            .HasMaxLength(8);

        builder.Property(x => x.Name)
            .HasColumnName("Name")
            .IsRequired()
            .HasMaxLength(255);

        builder.Property(x => x.EnName)
            .HasColumnName("EnName")
            .IsRequired()
            .HasMaxLength(255);


        // Indexes for performance
        builder.HasIndex(x => x.Code)
            .IsUnique()
            .HasDatabaseName("ix_company_types_code");

        builder.HasIndex(x => x.Name)
            .HasDatabaseName("ix_company_types_name");

        builder.HasIndex(x => x.EnName)
            .HasDatabaseName("ix_company_types_en_name");
    }
}

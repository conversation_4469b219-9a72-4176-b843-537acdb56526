using Microsoft.EntityFrameworkCore;
using Pgvector;
using Pgvector.EntityFrameworkCore;
using ViraceAiChatBot.Domain.Entities;
using ViraceAiChatBot.Domain.Interfaces;

namespace ViraceAiChatBot.Infrastructure.Persistence.Repositories;

public class RagChunkRepository(ChatBotDbContext context) : IRagChunkRepository
{
    private readonly ChatBotDbContext _context = context ?? throw new ArgumentNullException(nameof(context));

    public async Task<RagChunk?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.RagChunks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == id, cancellationToken);
    }

    public async Task<List<RagChunk>> GetBySourceAsync(string tableName, string rowId,
        CancellationToken cancellationToken = default)
    {
        return await _context.RagChunks
            .AsNoTracking()
            .Where(x => x.TableName == tableName && x.RowId == rowId)
            .OrderBy(x => x.ChunkIndex)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<RagChunk>> SearchSimilarAsync(
        float[] queryEmbedding,
        int limit = 10,
        string[]? tableFilter = null,
        CancellationToken cancellationToken = default)
    {
        IQueryable<RagChunk> query = _context.RagChunks.AsNoTracking();

        if (tableFilter is { Length: > 0 })
        {
            query = query.Where(x => tableFilter.Contains(x.TableName));
        }

        var queryVector = new Vector(queryEmbedding);

        const float maxDistance = 0.3f; // ~0.7 similarity

        var results = await query
            .OrderBy(x => x.Embedding.CosineDistance(queryVector))
            .Where(x => x.Embedding.CosineDistance(queryVector) <= maxDistance)
            .Take(limit)
            .ToListAsync(cancellationToken);

        return results;
    }

    public async Task<List<RagChunk>> SearchSimilarWithMetadataAsync(
        float[] queryEmbedding,
        int limit = 10,
        string[]? tableFilter = null,
        Dictionary<string, object>? metadataFilter = null,
        CancellationToken cancellationToken = default)
    {
        IQueryable<RagChunk> query = _context.RagChunks.AsNoTracking();

        if (tableFilter is { Length: > 0 })
        {
            query = query.Where(x => tableFilter.Contains(x.TableName));
        }

        if (metadataFilter is { Count: > 0 })
        {
            var jsonFilter = metadataFilter.ToDictionary(kv => kv.Key, kv => kv.Value.ToString());
            query = query.Where(x => x.Metadata != null && EF.Functions.JsonContains(x.Metadata, jsonFilter));
        }


        var queryVector = new Vector(queryEmbedding);
        var results = await query
            .OrderBy(x => x.Embedding.CosineDistance(queryVector))
            .Where(x => x.Embedding.CosineDistance(queryVector) >= 0)
            .Take(limit)
            .ToListAsync(cancellationToken);

        return results;
    }

    public async Task<RagChunk> AddAsync(RagChunk ragChunk, CancellationToken cancellationToken = default)
    {
        _context.RagChunks.Add(ragChunk);
        await _context.SaveChangesAsync(cancellationToken);
        return ragChunk;
    }

    public async Task AddRangeAsync(IEnumerable<RagChunk> ragChunks, CancellationToken cancellationToken = default)
    {
        _context.RagChunks.AddRange(ragChunks);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task UpdateAsync(RagChunk ragChunk, CancellationToken cancellationToken = default)
    {
        _context.RagChunks.Update(ragChunk);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task DeleteAsync(RagChunk ragChunk, CancellationToken cancellationToken = default)
    {
        _context.RagChunks.Remove(ragChunk);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task DeleteBySourceAsync(string tableName, string rowId, CancellationToken cancellationToken = default)
    {
        var chunks = await _context.RagChunks
            .Where(x => x.TableName == tableName && x.RowId == rowId)
            .ToListAsync(cancellationToken);

        if (chunks.Count > 0)
        {
            _context.RagChunks.RemoveRange(chunks);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task<bool> ExistsAsync(string tableName, string rowId, string columnName, int chunkIndex,
        CancellationToken cancellationToken = default)
    {
        return await _context.RagChunks
            .AnyAsync(x => x.TableName == tableName &&
                           x.RowId == rowId &&
                           x.ColumnName == columnName &&
                           x.ChunkIndex == chunkIndex, cancellationToken);
    }

    public async Task<int> GetCountAsync(CancellationToken cancellationToken = default)
    {
        IQueryable<RagChunk> query = _context.RagChunks;
        return await query.CountAsync(cancellationToken);
    }

    public async Task<List<string>> GetAvailableTablesAsync(CancellationToken cancellationToken = default)
    {
        IQueryable<string> query;


        query = _context.RagChunks
            .Select(x => x.TableName)
            .Distinct();


        return await query.ToListAsync(cancellationToken);
    }

    public async Task<DateTime?> GetLastProcessedTimeAsync(string tableName,
        CancellationToken cancellationToken = default)
    {
        var lastChunk = await _context.RagChunks
            .Where(x => x.TableName == tableName)
            .OrderByDescending(x => x.UpdatedAt)
            .FirstOrDefaultAsync(cancellationToken);

        return lastChunk?.UpdatedAt?.DateTime;
    }

    public async Task SetLastProcessedTimeAsync(string tableName, DateTime processedTime,
        CancellationToken cancellationToken = default)
    {
        // This method would typically update a separate metadata table
        // For now, we'll skip the implementation as it requires additional infrastructure
        // TODO: Implement with a separate ProcessingMetadata table
        var lastChunk = await _context.RagChunks
            .Where(x => x.TableName == tableName)
            .OrderByDescending(x => x.UpdatedAt)
            .FirstOrDefaultAsync(cancellationToken);

        if (lastChunk != null)
        {
            lastChunk.UpdatedAt = new DateTime?(processedTime);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }
}

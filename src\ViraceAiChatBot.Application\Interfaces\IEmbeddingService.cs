namespace ViraceAiChatBot.Application.Interfaces;

public interface EmbeddingService
{
    Task<float[]> GenerateEmbeddingAsync(string text, CancellationToken cancellationToken = default);

    Task<List<float[]>> GenerateEmbeddingsAsync(IEnumerable<string> texts, CancellationToken cancellationToken = default);

    Task<List<string>> ChunkTextAsync(string text, int maxTokens = 400, CancellationToken cancellationToken = default);

    int EstimateTokenCount(string text);

    double CalculateCosineSimilarity(float[] embedding1, float[] embedding2);
}

# ViraceAI ChatBot Company Search

A modern .NET 9.0 AI-powered chatbot API built with Clean Architecture, designed for intelligent company data search and retrieval using RAG (Retrieval-Augmented Generation) technology.

## 🏗️ Architecture

This project follows **Clean Architecture** principles with clear separation of concerns:

```
src/
├── ViraceAiChatBot.Domain/          # Business entities and domain logic
│   ├── Aggregates/                  # Domain aggregates (ChatSession)
│   ├── Entities/                    # Business entities and reference data
│   ├── Interfaces/                  # Domain service contracts
│   └── ValueObjects/                # Domain value objects
├── ViraceAiChatBot.Application/     # Application logic and use cases
│   ├── Commands/                    # CQRS command patterns
│   ├── Handlers/                    # Request handlers
│   ├── Interfaces/                  # Application service contracts
│   ├── Queries/                     # Query patterns
│   └── Validators/                  # Input validation
├── ViraceAiChatBot.Infrastructure/  # External concerns implementation
│   ├── Persistence/                 # Database context and repositories
│   └── Services/                    # External service implementations
└── ViraceAiChatBot.Presentation/    # API endpoints and controllers
    └── Endpoints/                   # Minimal API endpoint definitions
```

## 🚀 Features

- **AI-Powered Chat**: Intelligent conversation using OpenAI-compatible models
- **RAG Implementation**: Vector-based document retrieval for accurate responses
- **Company Data Search**: Specialized search across multiple reference data sources:
  - Country Index
  - Company Types
  - HS Codes
  - VSIC Industry Classifications
  - Financial Indices
  - Location Data
  - Ownership Types
- **Real-time Streaming**: Support for streaming chat responses
- **Caching**: Redis-based caching for improved performance
- **Extensible**: Clean architecture allows easy addition of new data sources

## 🛠️ Technology Stack

### Core Framework
- **.NET 9.0** - Latest .NET framework
- **ASP.NET Core Minimal APIs** - Lightweight web API framework
- **Entity Framework Core 9** - ORM with PostgreSQL provider

### Database & Vector Search
- **PostgreSQL** - Primary database
- **pgvector** - Vector similarity search for embeddings
- **Redis** - Caching layer

### AI & ML
- **OpenAI API** - Chat completions and embeddings
- **RestSharp** - HTTP client for AI service communication
- **Newtonsoft.Json** - JSON serialization

### Development Tools
- **MediatR** - CQRS and mediator pattern
- **FluentValidation** - Input validation
- **Scalar** - API documentation
- **Docker** - Containerization

## 🔧 Setup Instructions

### Prerequisites

- .NET 9.0 SDK
- PostgreSQL 14+ with pgvector extension
- Redis (optional, for caching)
- Docker (optional)

### 1. Clone Repository

```bash
git clone https://github.com/your-repo/ViraceAiChatBot.git
cd ViraceAiChatBot
```

### 2. Database Setup

#### Using Docker Compose (Recommended)

```bash
docker-compose up -d
```

This will start:
- PostgreSQL with pgvector extension
- Redis for caching

#### Manual PostgreSQL Setup

```sql
-- Create database
CREATE DATABASE "AI-POC-SEARCH";

-- Enable pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Run migrations (will be created automatically)
```

### 3. Configuration

Create `appsettings.Development.json`:

```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Port=5432;Database=AI-POC-SEARCH;User Id=postgres;Password=yourpassword;",
    "Redis": "localhost:6379"
  },
  "OpenAI": {
    "BaseUrl": "https://api.openai.com",
    "ApiKey": "your-openai-api-key",
    "DefaultProvider": "openai",
    "DefaultModel": "gpt-4o",
    "DefaultEmbeddingModel": "text-embedding-3-large"
  }
}
```

### 4. Database Migration

```bash
cd src/ViraceAiChatBot.Presentation
dotnet ef database update
```

### 5. Seed Reference Data

```bash
# Run the SQL scripts to populate reference tables
psql -d "AI-POC-SEARCH" -f create_reference_tables.sql
psql -d "AI-POC-SEARCH" -f scripts/init-db.sql
```

### 6. Run Application

```bash
cd src/ViraceAiChatBot.Presentation
dotnet run
```

The API will be available at:
- **HTTP**: `http://localhost:5000`
- **HTTPS**: `https://localhost:5001`
- **API Documentation**: `https://localhost:5001/scalar/v1`

## 📚 API Endpoints

### Chat Endpoints

#### Send Chat Message
```http
POST /api/chat/completion
Content-Type: application/json

{
  "prompt": "Tell me about technology companies in Vietnam",
  "maxTokens": 4000,
  "temperature": 0.7
}
```

#### Stream Chat Response
```http
POST /api/chat/stream
Content-Type: application/json

{
  "prompt": "What are the main industries in Vietnam?",
  "maxTokens": 4000,
  "temperature": 0.7
}
```

### Data Chunking Endpoints

#### Get Available Tables
```http
GET /api/chunking/tables
```

#### Chunk Specific Table
```http
POST /api/chunking/chunk/{tableName}
```

#### Chunk All Tables
```http
POST /api/chunking/chunk-all
```

#### Get Chunking Status
```http
GET /api/chunking/status/{tableName}
```

#### Refresh Table Chunks
```http
POST /api/chunking/refresh/{tableName}
```

### Health Check
```http
GET /api/health
```

## 🔧 Configuration Options

### OpenAI Settings

```json
{
  "OpenAI": {
    "BaseUrl": "https://api.openai.com",
    "ApiKey": "your-api-key",
    "DefaultProvider": "openai",
    "DefaultModel": "gpt-4o",
    "DefaultEmbeddingModel": "text-embedding-3-large",
    "MaxTokens": 4000,
    "Temperature": 0.7
  }
}
```

### Database Settings

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Port=5432;Database=AI-POC-SEARCH;User Id=postgres;Password=password;Command Timeout=15",
    "Redis": "localhost:6379"
  }
}
```

## 🐳 Docker Deployment

### Build Image

```bash
docker build -t virace-ai-chatbot .
```

### Run with Docker Compose

```bash
docker-compose up -d
```

The `docker-compose.yml` includes:
- PostgreSQL with pgvector
- Redis
- Application container

## 🧪 Development

### Running Tests

```bash
dotnet test
```

### Code Style

The project follows:
- **Clean Architecture** principles
- **SOLID** design principles
- **Microsoft C# Coding Conventions**
- **Async/await** patterns throughout

### Adding New Reference Data Sources

1. Create entity in `Domain/Entities/ReferenceData/`
2. Add DbSet to `ChatBotDbContext`
3. Create EF configuration in `Infrastructure/Persistence/Configurations/`
4. Add chunking method to `DataChunkingService`
5. Update switch cases in relevant methods

### Example: Adding New Reference Table

```csharp
// 1. Create Entity
public class IndustryClassification : Entity<int>
{
    public required string Code { get; init; }
    public required string Name { get; init; }
    public required string Description { get; init; }
}

// 2. Add to DbContext
public DbSet<IndustryClassification> IndustryClassifications { get; set; }

// 3. Add Chunking Method
private async Task<DataChunkingResult> ChunkIndustryClassificationAsync(CancellationToken cancellationToken)
{
    var industries = await _context.IndustryClassifications.AsNoTracking().ToListAsync(cancellationToken);
    var chunks = new List<RagChunk>();

    foreach (var industry in industries)
    {
        var content = $"Industry Classification: {industry.Name} (Code: {industry.Code}), Description: {industry.Description}";
        var embedding = await _openAiService.GetEmbeddingAsync(content, cancellationToken: cancellationToken);

        var chunk = new RagChunk(
            tableName: "IndustryClassification",
            rowId: industry.Id.ToString(),
            columnName: "SearchableContent",
            chunkIndex: 0,
            content: content,
            embedding: embedding
        );

        chunks.Add(chunk);
    }

    await _ragChunkRepository.AddRangeAsync(chunks, cancellationToken);

    return new DataChunkingResult
    {
        TableName = "IndustryClassification",
        ProcessedRecords = industries.Count,
        GeneratedChunks = chunks.Count,
        Success = true
    };
}
```

## 📊 Database Schema

### Core Tables

- **RagChunks** - Vector embeddings for search
- **ChatSessions** - Chat conversation tracking
- **CountryIndex** - Country reference data
- **CompanyTypes** - Company type classifications
- **HSCode** - Harmonized System codes
- **VSICIndex** - Vietnamese Standard Industrial Classification
- **FinancialIndex** - Financial indicators
- **LocationIndex** - Geographic location data

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🏢 About Virace

Developed by **ITE - Virace** team for intelligent company data search and analysis.

---

**Copyright © 2024 ITE - Virace. All rights reserved.**

## Streaming API Configuration

The application supports comprehensive streaming configuration for real-time AI interactions:

```json
{
  "OpenAI": {
    "BaseUrl": "https://localhost:7289",
    "ApiKey": "your-jwt-bearer-token-here",
    "DefaultProvider": "openai",
    "DefaultModel": "gpt-4o",
    "DefaultEmbeddingModel": "text-embedding-3-large",
    "MaxTokens": 4000,
    "Temperature": 0.7,
    "Streaming": {
      "Enabled": true,
      "ConnectionTimeoutSeconds": 30,
      "ReadTimeoutSeconds": 120,
      "BufferSize": 8192,
      "MaxRetryAttempts": 3,
      "RetryDelayMs": 1000,
      "AutoReconnect": true,
      "ChunkSize": 1024,
      "EnableHeartbeat": false,
      "HeartbeatIntervalSeconds": 30,
      "CustomHeaders": {
        "User-Agent": "ViraceAI-ChatBot/1.0",
        "X-Request-ID": "virace-streaming",
        "X-Client-Version": "1.0.0"
      }
    }
  }
}
```

#### Streaming Configuration Options

| Setting | Description | Default | Notes |
|---------|-------------|---------|--------|
| `Enabled` | Enable/disable streaming | `true` | When false, falls back to non-streaming |
| `ConnectionTimeoutSeconds` | HTTP connection timeout | `30` | Time to wait for initial connection |
| `ReadTimeoutSeconds` | Stream read timeout | `120` | Time to wait for data chunks |
| `BufferSize` | Stream buffer size in bytes | `8192` | Buffer for reading stream data |
| `MaxRetryAttempts` | Number of retry attempts | `3` | Retries on connection failures |
| `RetryDelayMs` | Delay between retries | `1000` | Milliseconds to wait between attempts |
| `AutoReconnect` | Enable automatic reconnection | `true` | Auto-retry on failures |
| `ChunkSize` | Processing chunk size | `1024` | Size of data chunks to process |
| `EnableHeartbeat` | Keep-alive heartbeat | `false` | Send periodic keep-alive messages |
| `HeartbeatIntervalSeconds` | Heartbeat frequency | `30` | Seconds between heartbeat messages |
| `CustomHeaders` | Additional HTTP headers | `{}` | Custom headers for requests |

### API Endpoints

#### Streaming Chat Completion
```
POST /api/v1/chat/stream
Content-Type: application/json
Accept: text/event-stream

{
  "prompt": "Hello, how are you?",
  "maxTokens": 1000,
  "temperature": 0.7
}
```

Response: Server-Sent Events stream with JSON chunks:
```
data: {"content": "Hello", "isComplete": false, "tokenUsed": 1}
data: {"content": "!", "isComplete": false, "tokenUsed": 2}
data: [DONE]
```

#### Regular Chat Completion
```
POST /api/v1/chat
Content-Type: application/json

{
  "prompt": "Hello, how are you?",
  "maxTokens": 1000,
  "temperature": 0.7
}
```

## Development

### Prerequisites
- .NET 9.0 SDK
- PostgreSQL 14+
- pgvector extension

### Setup
1. Clone the repository
2. Configure connection strings in `appsettings.Development.json`
3. Run database migrations
4. Configure your AI service endpoint and API key
5. Run the application

### Environment Variables
- `ASPNETCORE_ENVIRONMENT`: Set to `Development` for development mode
- `OpenAI__ApiKey`: Your AI service API key
- `OpenAI__BaseUrl`: Your AI service base URL

## Logging

The application uses Serilog for structured logging with:
- Console output for development
- File logging with rotation
- Different log levels for different environments

Log files are stored in the `logs/` directory with daily rotation.

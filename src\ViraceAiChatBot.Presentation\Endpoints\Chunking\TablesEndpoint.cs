using ViraceAiChatBot.Application.Interfaces;

namespace ViraceAiChatBot.Presentation.Endpoints.Chunking;

/// <summary>
/// Endpoint xử lý danh sách tables có sẵn cho chunking
/// </summary>
public static class TablesEndpoint
{
    /// <summary>
    /// Map Tables endpoint
    /// </summary>
    /// <param name="group">Route group</param>
    public static void MapTablesEndpoint(this RouteGroupBuilder group)
    {
        group.MapGet("/tables", async (DataChunkingService chunkingService, CancellationToken cancellationToken) =>
        {
            var tables = await chunkingService.GetAvailableTablesAsync(cancellationToken);
            return Results.Ok(new { Tables = tables });
        })
        .WithName("GetAvailableTables")
        .WithSummary("Get list of tables available for chunking")
        .Produces<object>(200);
    }
}

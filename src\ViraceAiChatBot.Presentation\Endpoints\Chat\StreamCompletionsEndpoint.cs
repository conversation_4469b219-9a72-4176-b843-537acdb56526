using Microsoft.AspNetCore.Mvc;
using MediatR;
using ViraceAiChatBot.Application.Commands;
using ViraceAiChatBot.Application.Interfaces;
using ViraceAiChatBot.Presentation.DTOs;
using System.Runtime.CompilerServices;
using System.Text;
using System.Text.Json;

namespace ViraceAiChatBot.Presentation.Endpoints.Chat;

/// <summary>
/// Endpoint xử lý streaming chat completions
/// </summary>
public static class StreamCompletionsEndpoint
{
    /// <summary>
    /// Map Streaming Chat Completions endpoint
    /// </summary>
    /// <param name="group">Route group</param>
    public static void MapStreamCompletionsEndpoint(this RouteGroupBuilder group)
    {
        group.MapPost("/completions/stream", async (
                [FromBody] ChatCompletionRequest request,
                IMediator mediator,
                OpenAiService openAiService,
                HttpContext httpContext,
                CancellationToken cancellationToken) =>
            {
                try
                {
                    httpContext.Response.Headers.Append("Content-Type", "text/event-stream");
                    httpContext.Response.Headers.Append("Cache-Control", "no-cache");
                    httpContext.Response.Headers.Append("Connection", "keep-alive");
                    httpContext.Response.Headers.Append("Access-Control-Allow-Origin", "*");

                    await foreach (var chunk in StreamChatCompletion(request, mediator, openAiService,
                                       cancellationToken))
                    {
                        var bytes = Encoding.UTF8.GetBytes(chunk);
                        await httpContext.Response.Body.WriteAsync(bytes, cancellationToken);
                        await httpContext.Response.Body.FlushAsync(cancellationToken);
                    }
                }
                catch (OperationCanceledException)
                {
                }
                catch (Exception ex)
                {
                    var errorEvent = FormatAsServerSentEvent(new { error = ex.Message }, "error");
                    var errorBytes = Encoding.UTF8.GetBytes(errorEvent);
                    await httpContext.Response.Body.WriteAsync(errorBytes, cancellationToken);
                    await httpContext.Response.Body.FlushAsync(cancellationToken);
                }
            })
            .WithName("StreamChatCompletions")
            .WithSummary("Create a streaming chat completion")
            .Produces(200, contentType: "text/event-stream");
    }

    /// <summary>
    /// Stream chat completion responses
    /// </summary>
    /// <param name="request">Chat completion request</param>
    /// <param name="mediator">Mediator instance</param>
    /// <param name="openAiService">OpenAI service</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Async enumerable of string chunks</returns>
    private static async IAsyncEnumerable<string> StreamChatCompletion(
        ChatCompletionRequest request,
        IMediator mediator,
        OpenAiService openAiService,
        [EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        var chatId = $"chatcmpl-{Guid.NewGuid()}";
        var created = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
        var model = request.Model ?? "gpt-4";

        // Build prompt from the last message
        var prompt = request.Messages.LastOrDefault()?.Content ??
                     throw new ArgumentException("No message content provided");

        // Send initial event to confirm connection
        yield return FormatAsServerSentEvent(new { id = chatId, status = "started", model = model }, "connection");

        var contentBuilder = new StringBuilder();

        await foreach (var chunk in openAiService.StreamChatCompletionAsync(
                           prompt,
                           request.MaxTokens ?? 4000,
                           request.Temperature ?? 0.7,
                           cancellationToken: cancellationToken))
        {
            // Check for cancellation
            cancellationToken.ThrowIfCancellationRequested();

            if (chunk.IsComplete)
            {
                // Send final chunk with completion info
                var finalChunk = new ChatCompletionStreamResponse(
                    chatId,
                    "chat.completion.chunk",
                    created,
                    model,
                    [
                        new StreamChoice(
                            0,
                            new ChatMessage("assistant", ""),
                            "stop"
                        )
                    ]
                );
                yield return FormatAsServerSentEvent(finalChunk);

                // Optionally save the complete conversation using mediator
                if (!string.IsNullOrEmpty(contentBuilder.ToString()))
                {
                    var saveCommand = new SaveChatMessageCommand(
                        UserId: request.UserId ?? "anonymous",
                        SessionId: request.SessionId,
                        UserMessage: prompt,
                        AssistantMessage: contentBuilder.ToString(),
                        Model: model,
                        TokensUsed: chunk.TokenUsed
                    );

                    await mediator.Send(saveCommand, cancellationToken);
                }

                break;
            }
            else if (!string.IsNullOrEmpty(chunk.Content))
            {
                // Accumulate content for saving later
                contentBuilder.Append(chunk.Content);

                // Send content chunk
                var contentChunk = new ChatCompletionStreamResponse(
                    chatId,
                    "chat.completion.chunk",
                    created,
                    model,
                    [
                        new StreamChoice(
                            0,
                            new ChatMessage("assistant", chunk.Content),
                            null
                        )
                    ]
                );
                yield return FormatAsServerSentEvent(contentChunk);
            }
        }

        yield return "data: [DONE]\n\n";
    }

    /// <summary>
    /// Format object as Server-Sent Event
    /// </summary>
    /// <param name="data">Data object</param>
    /// <param name="eventType">Event type</param>
    /// <returns>Formatted SSE string</returns>
    private static string FormatAsServerSentEvent(object data, string eventType = "data")
    {
        var json = JsonSerializer.Serialize(data,
            new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower,
                WriteIndented = false
            });

        if (eventType != "data")
        {
            return $"event: {eventType}\ndata: {json}\n\n";
        }

        return $"data: {json}\n\n";
    }

    /// <summary>
    /// Format chat completion stream response as Server-Sent Event
    /// </summary>
    /// <param name="chunk">Stream response chunk</param>
    /// <returns>Formatted SSE string</returns>
    private static string FormatAsServerSentEvent(ChatCompletionStreamResponse chunk)
    {
        var json = JsonSerializer.Serialize(chunk,
            new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower,
                WriteIndented = false
            });

        return $"data: {json}\n\n";
    }
}

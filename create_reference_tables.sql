-- =============================================
-- ViraceAI ChatBot Company Search - Reference Data Tables
-- This script creates all 10 reference data tables
-- =============================================

-- Enable required PostgreSQL extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =============================================
-- REFERENCE DATA TABLES (10 TABLES)
-- =============================================

-- Table 1: CountryIndex
CREATE TABLE IF NOT EXISTS "CountryIndex" (
    "Id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    "Iso" VARCHAR(2) NOT NULL,
    "Iso3" VARCHAR(3) NOT NULL,
    "Name" VARCHAR(100) NOT NULL,
    "CreatedAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Table 2: CompanyTypes
CREATE TABLE IF NOT EXISTS "CompanyTypes" (
    "Id" SMALLSERIAL PRIMARY KEY,
    "Code" VARCHAR(8) NOT NULL UNIQUE,
    "Name" VARCHAR(255) NOT NULL,
    "EnName" VARCHAR(255) NOT NULL,
    "CreatedAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Table 3: CurrencyExchangeRate
CREATE TABLE IF NOT EXISTS "CurrencyExchangeRate" (
    "Id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    "Year" INTEGER NOT NULL,
    "Value" INTEGER NOT NULL,
    "FromCurrency" VARCHAR(3),
    "ToCurrency" VARCHAR(3),
    "Rate" DECIMAL(18, 8),
    "RateDate" DATE,
    "CreatedAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Table 4: FinancialIndex
CREATE TABLE IF NOT EXISTS "FinancialIndex" (
    "Id" SERIAL PRIMARY KEY,
    "ParentId" SMALLINT,
    "Name" VARCHAR(500) NOT NULL,
    "EnName" VARCHAR(500),
    "Unit" VARCHAR(30),
    "Type" VARCHAR(500),
    "TypeIndex" VARCHAR(50),
    "CreatedAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Table 5: FinancialStatementIndex
CREATE TABLE IF NOT EXISTS "FinancialStatementIndex" (
    "Id" SERIAL PRIMARY KEY,
    "Type" VARCHAR(100) NOT NULL,
    "EnType" VARCHAR(100) NOT NULL,
    "TypeIndex" VARCHAR(50) NOT NULL,
    "CreatedAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Table 6: HSCode (Harmonized System Codes)
CREATE TABLE IF NOT EXISTS "HSCode" (
    "Id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    "code2" VARCHAR(2) NOT NULL,
    "code4" VARCHAR(16) NOT NULL,
    "descvi" VARCHAR(1000),
    "descen" VARCHAR(1000),
    "CreatedAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Table 7: LocationIndex
CREATE TABLE IF NOT EXISTS "LocationIndex" (
    "Id" SERIAL PRIMARY KEY,
    "ParentId" INTEGER,
    "Level" SMALLINT NOT NULL,
    "Name" VARCHAR(255) NOT NULL,
    "EngName" VARCHAR(255),
    "CreatedAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Table 8: OwnershipTypeIndex
CREATE TABLE IF NOT EXISTS "OwnershipTypeIndex" (
    "Id" SMALLSERIAL PRIMARY KEY,
    "Name" VARCHAR(20) NOT NULL,
    "EnName" VARCHAR(20) NOT NULL,
    "CreatedAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Table 9: RelatedCompanyTypeIndex
CREATE TABLE IF NOT EXISTS "RelatedCompanyTypeIndex" (
    "Id" SMALLSERIAL PRIMARY KEY,
    "Name" VARCHAR(50) NOT NULL,
    "EnName" VARCHAR(50) NOT NULL,
    "CreatedAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Table 10: VSICIndex (Vietnam Standard Industrial Classification)
CREATE TABLE IF NOT EXISTS "VSICIndex" (
    "Id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    "Code" VARCHAR(8) NOT NULL UNIQUE,
    "Name" VARCHAR(500) NOT NULL,
    "EnName" VARCHAR(500),
    "CreatedAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

-- CountryIndex Indexes
CREATE UNIQUE INDEX IF NOT EXISTS "ix_country_index_iso" ON "CountryIndex"("Iso");
CREATE UNIQUE INDEX IF NOT EXISTS "ix_country_index_iso3" ON "CountryIndex"("Iso3");
CREATE INDEX IF NOT EXISTS "ix_country_index_name" ON "CountryIndex"("Name");

-- CompanyTypes Indexes
CREATE INDEX IF NOT EXISTS "ix_company_types_name" ON "CompanyTypes"("Name");
CREATE INDEX IF NOT EXISTS "ix_company_types_en_name" ON "CompanyTypes"("EnName");

-- CurrencyExchangeRate Indexes
CREATE INDEX IF NOT EXISTS "ix_currency_exchange_rate_year" ON "CurrencyExchangeRate"("Year");
CREATE INDEX IF NOT EXISTS "ix_currency_exchange_rate_currencies" ON "CurrencyExchangeRate"("FromCurrency", "ToCurrency");
CREATE INDEX IF NOT EXISTS "ix_currency_exchange_rate_date" ON "CurrencyExchangeRate"("RateDate");

-- FinancialIndex Indexes
CREATE INDEX IF NOT EXISTS "ix_financial_index_parent" ON "FinancialIndex"("ParentId");
CREATE INDEX IF NOT EXISTS "ix_financial_index_name" ON "FinancialIndex"("Name");
CREATE INDEX IF NOT EXISTS "ix_financial_index_type" ON "FinancialIndex"("Type");

-- FinancialStatementIndex Indexes
CREATE INDEX IF NOT EXISTS "ix_financial_statement_index_type" ON "FinancialStatementIndex"("Type");
CREATE INDEX IF NOT EXISTS "ix_financial_statement_index_type_index" ON "FinancialStatementIndex"("TypeIndex");

-- HSCode Indexes
CREATE INDEX IF NOT EXISTS "ix_hs_code_code2" ON "HSCode"("code2");
CREATE INDEX IF NOT EXISTS "ix_hs_code_code4" ON "HSCode"("code4");

-- LocationIndex Indexes
CREATE INDEX IF NOT EXISTS "ix_location_index_parent" ON "LocationIndex"("ParentId");
CREATE INDEX IF NOT EXISTS "ix_location_index_level" ON "LocationIndex"("Level");
CREATE INDEX IF NOT EXISTS "ix_location_index_name" ON "LocationIndex"("Name");

-- OwnershipTypeIndex Indexes
CREATE INDEX IF NOT EXISTS "ix_ownership_type_index_name" ON "OwnershipTypeIndex"("Name");

-- RelatedCompanyTypeIndex Indexes
CREATE INDEX IF NOT EXISTS "ix_related_company_type_index_name" ON "RelatedCompanyTypeIndex"("Name");

-- VSICIndex Indexes
CREATE INDEX IF NOT EXISTS "ix_vsic_index_name" ON "VSICIndex"("Name");
CREATE INDEX IF NOT EXISTS "ix_vsic_index_en_name" ON "VSICIndex"("EnName");

-- =============================================
-- FOREIGN KEY CONSTRAINTS
-- =============================================

-- FinancialIndex self-referencing foreign key
ALTER TABLE "FinancialIndex"
ADD CONSTRAINT "fk_financial_index_parent"
FOREIGN KEY ("ParentId") REFERENCES "FinancialIndex"("Id");

-- LocationIndex self-referencing foreign key
ALTER TABLE "LocationIndex"
ADD CONSTRAINT "fk_location_index_parent"
FOREIGN KEY ("ParentId") REFERENCES "LocationIndex"("Id");

-- =============================================
-- TRIGGERS FOR UPDATED_AT TIMESTAMPS
-- =============================================

-- Function to update the updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW."UpdatedAt" = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to all tables
CREATE TRIGGER update_country_index_updated_at
    BEFORE UPDATE ON "CountryIndex"
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_company_types_updated_at
    BEFORE UPDATE ON "CompanyTypes"
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_currency_exchange_rate_updated_at
    BEFORE UPDATE ON "CurrencyExchangeRate"
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_financial_index_updated_at
    BEFORE UPDATE ON "FinancialIndex"
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_financial_statement_index_updated_at
    BEFORE UPDATE ON "FinancialStatementIndex"
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_hs_code_updated_at
    BEFORE UPDATE ON "HSCode"
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_location_index_updated_at
    BEFORE UPDATE ON "LocationIndex"
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ownership_type_index_updated_at
    BEFORE UPDATE ON "OwnershipTypeIndex"
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_related_company_type_index_updated_at
    BEFORE UPDATE ON "RelatedCompanyTypeIndex"
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_vsic_index_updated_at
    BEFORE UPDATE ON "VSICIndex"
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================
-- SAMPLE DATA (OPTIONAL)
-- =============================================

-- Sample Countries
INSERT INTO "CountryIndex" ("Iso", "Iso3", "Name") VALUES
    ('US', 'USA', 'United States'),
    ('VN', 'VNM', 'Vietnam'),
    ('SG', 'SGP', 'Singapore'),
    ('TH', 'THA', 'Thailand'),
    ('MY', 'MYS', 'Malaysia'),
    ('CN', 'CHN', 'China'),
    ('JP', 'JPN', 'Japan'),
    ('KR', 'KOR', 'South Korea')
ON CONFLICT ("Iso") DO NOTHING;

-- Sample Company Types
INSERT INTO "CompanyTypes" ("Code", "Name", "EnName") VALUES
    ('LLC', 'Công ty TNHH', 'Limited Liability Company'),
    ('JSC', 'Công ty Cổ phần', 'Joint Stock Company'),
    ('LTD', 'Công ty TNHH MTV', 'Private Limited Company'),
    ('PLC', 'Công ty Đại chúng', 'Public Limited Company'),
    ('COOP', 'Hợp tác xã', 'Cooperative'),
    ('SE', 'Doanh nghiệp tư nhân', 'Sole Enterprise'),
    ('GP', 'Công ty Hợp danh', 'General Partnership')
ON CONFLICT ("Code") DO NOTHING;

-- Sample Ownership Types
INSERT INTO "OwnershipTypeIndex" ("Name", "EnName") VALUES
    ('Nhà nước', 'State-owned'),
    ('Tư nhân', 'Private'),
    ('Liên doanh', 'Joint venture'),
    ('Nước ngoài', 'Foreign'),
    ('Hỗn hợp', 'Mixed')
ON CONFLICT DO NOTHING;

-- Sample Related Company Types
INSERT INTO "RelatedCompanyTypeIndex" ("Name", "EnName") VALUES
    ('Công ty mẹ', 'Parent Company'),
    ('Công ty con', 'Subsidiary'),
    ('Công ty liên kết', 'Associated Company'),
    ('Chi nhánh', 'Branch'),
    ('Văn phòng đại diện', 'Representative Office')
ON CONFLICT DO NOTHING;

-- Success message
SELECT 'All 10 reference data tables created successfully!' as status,
       'Tables: CountryIndex, CompanyTypes, CurrencyExchangeRate, FinancialIndex, FinancialStatementIndex, HSCode, LocationIndex, OwnershipTypeIndex, RelatedCompanyTypeIndex, VSICIndex' as tables;

namespace ViraceAiChatBot.Domain.Entities.ReferenceData;

public class CurrencyExchangeRate
{
    public int Year { get; init; }
    public int Value { get; init; }

    // Parameterless constructor for EF Core
    private CurrencyExchangeRate() { }

    public CurrencyExchangeRate(int year, int value)
    {
        Year = year;
        Value = value;

        ValidateInvariants();
    }

    private void ValidateInvariants()
    {
        if (Year < 1900 || Year > 2100)
            throw new ArgumentException("Year must be between 1900 and 2100", nameof(Year));

        if (Value < 0)
            throw new ArgumentException("Value cannot be negative", nameof(Value));
    }

    public string GetSearchableContent()
    {
        return $"Currency exchange rate {Year} {Value}";
    }

    public override string ToString()
    {
        return $"CurrencyExchangeRate [Year: {Year}, Value: {Value}]";
    }
}

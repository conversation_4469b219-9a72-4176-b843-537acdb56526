using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ViraceAiChatBot.Domain.Entities.ReferenceData;

namespace ViraceAiChatBot.Infrastructure.Persistence.Configurations;

public class OwnershipTypeIndexConfiguration : IEntityTypeConfiguration<OwnershipTypeIndex>
{
    public void Configure(EntityTypeBuilder<OwnershipTypeIndex> builder)
    {
        builder.ToTable("OwnershipTypeIndex");

        builder.HasKey(x => x.Id);

        builder.Property(x => x.Id)
            .HasColumnName("Id")
            .ValueGeneratedOnAdd();

        builder.Property(x => x.Name)
            .HasColumnName("Name")
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(x => x.EnName)
            .HasColumnName("EnName")
            .IsRequired()
            .HasMaxLength(20);


        // Indexes for performance
        builder.HasIndex(x => x.Name)
            .HasDatabaseName("ix_ownership_type_index_name");
    }
}

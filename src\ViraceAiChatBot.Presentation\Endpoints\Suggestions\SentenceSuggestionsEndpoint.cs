using Microsoft.AspNetCore.Mvc;
using MediatR;
using ViraceAiChatBot.Application.Queries;
using ViraceAiChatBot.Application.Dtos;

namespace ViraceAiChatBot.Presentation.Endpoints.Suggestions;

/// <summary>
/// Endpoint xử lý gợi ý câu
/// </summary>
public static class SentenceSuggestionsEndpoint
{
    /// <summary>
    /// Map Sentence Suggestions endpoint
    /// </summary>
    /// <param name="group">Route group</param>
    public static void MapSentenceSuggestionsEndpoint(this RouteGroupBuilder group)
    {
        group.MapPost("/suggestions/sentences", async (
                [FromBody] SentenceSuggestionRequest request,
                IMediator mediator,
                CancellationToken cancellationToken) =>
            {
                try
                {
                    var query = new GetSentenceSuggestionsQuery(
                        request.UserId,
                        request.SessionId,
                        request.CurrentText,
                        request.CursorPosition,
                        request.Context,
                        request.IncludeNewSentences,
                        request.MaxSuggestions);

                    var result = await mediator.Send(query, cancellationToken);
                    return Results.Ok(result);
                }
                catch (Exception ex)
                {
                    return Results.BadRequest(new { error = ex.Message });
                }
            })
            .WithName("GetSentenceSuggestions")
            .WithSummary("Get sentence completion or new sentence suggestions")
            .Produces<SentenceSuggestionResponse>(200)
            .Produces<object>(400);
    }
}

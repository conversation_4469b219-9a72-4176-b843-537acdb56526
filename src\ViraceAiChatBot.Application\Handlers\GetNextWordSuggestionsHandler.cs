using MediatR;
using Microsoft.Extensions.Logging;
using ViraceAiChatBot.Application.Dtos;
using ViraceAiChatBot.Application.Interfaces;
using ViraceAiChatBot.Application.Queries;

namespace ViraceAiChatBot.Application.Handlers;

public class GetNextWordSuggestionsHandler(
    SuggestionService suggestionService,
    ILogger<GetNextWordSuggestionsHandler> logger) : IRequestHandler<GetNextWordSuggestionsQuery, NextWordSuggestionResponse>
{
    public async Task<NextWordSuggestionResponse> Handle(GetNextWordSuggestionsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Processing next word suggestions for user {UserId}, text: {Text} at position {Position}",
                request.UserId, request.CurrentText, request.CursorPosition);

            var response = await suggestionService.GetNextWordSuggestionsAsync(
                request.CurrentText,
                request.CursorPosition,
                request.Context,
                request.MaxSuggestions,
                cancellationToken);

            logger.LogInformation("Generated {Count} word suggestions for user {UserId}",
                response.Suggestions.Count, request.UserId);

            return response;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing next word suggestions for user {UserId}", request.UserId);

            return new NextWordSuggestionResponse(
                [],
                request.CursorPosition,
                "Error occurred while generating suggestions",
                TimeSpan.Zero
            );
        }
    }
}

using MediatR;
using ViraceAiChatBot.Application.Response;

namespace ViraceAiChatBot.Application.Commands;

/// <summary>
/// Represents a command to generate a chat completion.
/// </summary>
/// <remarks>
/// This command is used to request a chat completion based on the provided user input, session details, and model parameters.
/// </remarks>
/// <param name="UserId">The identifier of the user initiating the chat completion.</param>
/// <param name="Message">The message input from the user to generate a response.</param>
/// <param name="SessionId">The optional unique identifier of the chat session.</param>
/// <param name="UseRag">Indicates whether Retrieval-Augmented Generation (RAG) should be used.</param>
/// <param name="MaxTokens">The maximum number of tokens to generate in the response.</param>
/// <param name="Temperature">The sampling temperature for response generation, controlling randomness.</param>
/// <param name="TopP">The nucleus sampling parameter, controlling diversity in the response.</param>
/// <param name="Model">The name of the model to use for generating the response.</param>
/// <param name="TableFilter">An optional array of table names to filter the response.</param>
/// <param name="AuthToken">An optional authentication token for the request.</param>
/// <returns>
/// A response of type <see cref="ChatCompletionResponse"/> containing the generated chat completion.
/// </returns>
public record ChatCompletionCommand(
    string UserId,
    string Message,
    Guid? SessionId = null,
    bool UseRag = true,
    int MaxTokens = 4000,
    float Temperature = 0.7f,
    float TopP = 1.0f,
    string Model = "gpt-4",
    string[]? TableFilter = null,
    string? AuthToken = null
) : IRequest<ChatCompletionResponse>;

using MediatR;

namespace ViraceAiChatBot.Application.Commands;

/// <summary>
/// Represents a command to save a chat message.
/// </summary>
/// <remarks>
/// This command is used to persist a chat message along with its associated metadata,
/// including the user message, assistant response, model used, and token count.
/// </remarks>
/// <param name="UserId">The identifier of the user who sent the message.</param>
/// <param name="SessionId">The optional unique identifier of the chat session.</param>
/// <param name="UserMessage">The message sent by the user.</param>
/// <param name="AssistantMessage">The response generated by the assistant.</param>
/// <param name="Model">The name of the model used to generate the assistant's response.</param>
/// <param name="TokensUsed">The number of tokens used in the interaction.</param>
/// <returns>
/// A response of type <see cref="Unit"/> indicating the result of the operation.
/// </returns>
public record SaveChatMessageCommand(
    string UserId,
    Guid? SessionId,
    string UserMessage,
    string AssistantMessage,
    string Model,
    int TokensUsed
) : IRequest<Unit>;

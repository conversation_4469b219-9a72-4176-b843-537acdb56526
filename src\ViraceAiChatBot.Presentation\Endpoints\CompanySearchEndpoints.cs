using ViraceAiChatBot.Application.Dtos.CompanySearch;
using ViraceAiChatBot.Application.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace ViraceAiChatBot.Presentation.Endpoints;

/// <summary>
/// Extension methods for mapping company search endpoints
/// </summary>
public static class CompanySearchEndpoints
{
    /// <summary>
    /// Maps company search endpoints to the route group
    /// </summary>
    /// <param name="app">Web application instance</param>
    /// <returns>The web application with mapped endpoints</returns>
    public static WebApplication MapCompanySearchEndpoints(this WebApplication app)
    {
        var group = app.MapGroup("/api/v1/companies")
            .WithTags("Company Search");

        group.MapPost("/search/simple", SimpleCompanySearchAsync)
            .WithName("SimpleCompanySearch")
            .WithSummary("Simple company search")
            .WithDescription("Search companies using simple text query with basic filters")
            .Produces<CompanySearchResponse<SearchCompanyResponseModel>>(200)
            .Produces(400)
            .Produces(401)
            .Produces(500);

        group.MapPost("/search/advanced", AdvancedCompanySearchAsync)
            .WithName("AdvancedCompanySearch")
            .WithSummary("Advanced company search")
            .WithDescription("Search companies using advanced filters and criteria")
            .Produces<CompanySearchResponse<SearchCompanyResponseModel>>(200)
            .Produces(400)
            .Produces(401)
            .Produces(500);

        return app;
    }

    /// <summary>
    /// Simple company search endpoint
    /// </summary>
    /// <param name="request">Simple search request</param>
    /// <param name="companySearchService">Company search service</param>
    /// <param name="authToken">Authorization token from header</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Company search response</returns>
    [Authorize]
    private static async Task<IResult> SimpleCompanySearchAsync(
        [FromBody] SimpleSearchRequest request,
        [FromServices] CompanySearchService companySearchService,
        [FromHeader(Name = "Authorization")] string? authToken,
        CancellationToken cancellationToken)
    {
        try
        {
            // Extract Bearer token
            var token = authToken?.Replace("Bearer ", "");

            var result = await companySearchService.SimpleSearchAsync(request, token, cancellationToken);

            if (result.Success)
            {
                return Results.Ok(result.Data);
            }

            return Results.BadRequest(new { error = result.Message });
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "Internal Server Error",
                detail: ex.Message,
                statusCode: 500);
        }
    }

    /// <summary>
    /// Advanced company search endpoint
    /// </summary>
    /// <param name="request">Advanced search request</param>
    /// <param name="companySearchService">Company search service</param>
    /// <param name="authToken">Authorization token from header</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Company search response</returns>
    [Authorize]
    private static async Task<IResult> AdvancedCompanySearchAsync(
        [FromBody] AdvancedSearchRequest request,
        [FromServices] CompanySearchService companySearchService,
        [FromHeader(Name = "Authorization")] string? authToken,
        CancellationToken cancellationToken)
    {
        try
        {
            // Extract Bearer token
            var token = authToken?.Replace("Bearer ", "");

            var result = await companySearchService.AdvancedSearchAsync(request, token, cancellationToken);

            if (result.Success)
            {
                return Results.Ok(result.Data);
            }

            return Results.BadRequest(new { error = result.Message });
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "Internal Server Error",
                detail: ex.Message,
                statusCode: 500);
        }
    }
}

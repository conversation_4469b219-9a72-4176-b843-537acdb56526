namespace ViraceAiChatBot.Domain.Entities.ReferenceData;

public class LocationIndex
{
    public int Id { get; init; }
    public int? ParentId { get; init; }
    public int? Level { get; init; } // Level of the location in the hierarchy (0 for country, 1 for state, etc.)
    public string? Name { get; init; }
    public string? EngName { get; init; }

    // Parameterless constructor for EF Core
    private LocationIndex() { }

    public LocationIndex(short level, string name, string engName, int? parentId = null)
    {
        Level = level;
        Name = name ?? throw new ArgumentNullException(nameof(name));
        EngName = engName ?? throw new ArgumentNullException(nameof(engName));
        ParentId = parentId;

        ValidateInvariants();
    }

    private void ValidateInvariants()
    {
        if (Level < 0)
            throw new ArgumentException("Level cannot be negative", nameof(Level));

        if (string.IsNullOrWhiteSpace(Name))
            throw new ArgumentException("Name cannot be empty", nameof(Name));

        if (Name.Length > 255)
            throw new ArgumentException("Name cannot exceed 255 characters", nameof(Name));

        if (string.IsNullOrWhiteSpace(EngName))
            throw new ArgumentException("EngName cannot be empty", nameof(EngName));

        if (EngName.Length > 255)
            throw new ArgumentException("EngName cannot exceed 255 characters", nameof(EngName));
    }

    public string GetSearchableContent()
    {
        return $"{Name} {EngName} Level {Level}";
    }

    public override string ToString()
    {
        return $"LocationIndex [Id: {Id}, Name: {Name}, EngName: {EngName}, Level: {Level}]";
    }
}

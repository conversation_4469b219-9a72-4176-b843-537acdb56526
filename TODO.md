# TODO - RAG Chatbot with PostgreSQL (pgvector) - .NET Clean Architecture

> **Project Status:** Planning & Architecture Phase
> **Target Framework:** .NET 9.0
> **Architecture:** Clean Architecture with CQRS/MediatR
> **Database:** PostgreSQL + pgvector extension

---

## ✅ Phase 1: Foundation & Infrastructure (COMPLETED)

### ✅ 1.1 Project Setup
- [x] **Create solution structure**
  - [x] Create new solution: `ViraceAiChatBot.sln`
  - [x] Create Domain project: `ViraceAiChatBot.Domain`
  - [x] Create Application project: `ViraceAiChatBot.Application`
  - [x] Create Infrastructure project: `ViraceAiChatBot.Infrastructure`
  - [x] Create Presentation project: `ViraceAiChatBot.Presentation`
  - [x] Create Test projects: `ViraceAiChatBot.Tests.Unit` & `ViraceAiChatBot.Tests.Integration`

- [x] **Configure project dependencies**
  - [x] Add required NuGet packages (MediatR, FluentValidation)
  - [x] Configure project references (Domain ← Application ← Infrastructure ← Presentation)
  - [x] Enable nullable reference types and implicit usings globally
  - [x] Configure file-scoped namespaces in .editorconfig

### ✅ 1.2 Database Infrastructure
- [x] **PostgreSQL + pgvector setup**
  - [x] Create docker-compose.yml with PostgreSQL + pgvector, Redis, Seq
  - [x] Create init-db.sql script with pgvector extension
  - [x] Configure health checks and proper volumes
  - [ ] Configure connection string in appsettings.json
  - [ ] Test pgvector installation and vector operations

- [ ] **EF Core Configuration**
  - [ ] Create ChatBotDbContext with snake_case naming convention
  - [ ] Configure DbContext registration in DI container
  - [ ] Set up connection string configuration
  - [ ] Configure database connection pooling

### ✅ 1.3 Domain Layer
- [x] **Core Entities**
  - [x] Create `RagChunk` entity with vector property and validation
  - [x] Create `ChatSession` aggregate root with domain events
  - [x] Create `ChatMessage` entity with role validation
  - [ ] Create `EmbeddingJob` entity for background processing
  - [x] Create base `Entity` and `AggregateRoot` classes

- [x] **Value Objects**
  - [ ] Create `EmbeddingVector` value object
  - [x] Create `TenantId` value object
  - [ ] Create `ChatContent` value object with validation

- [x] **Domain Interfaces**
  - [x] Create `IRagChunkRepository` interface with vector operations
  - [ ] Create `IChatRepository` interface
  - [x] Create `ITenantService` interface
  - [x] Create `IEmbeddingService` interface

---

## 🔥 Phase 2: Application Layer & Core Logic (IN PROGRESS)

### ✅ 2.1 CQRS Commands & Queries
- [x] **Chat Commands**
  - [x] Implement `ChatCompletionCommand` with validation
  - [x] Implement `StreamChatCompletionCommand`
  - [ ] Implement `CreateChatSessionCommand`
  - [x] Add FluentValidation rules for all commands

- [ ] **Embedding Commands**
  - [ ] Implement `ProcessEmbeddingCommand`
  - [ ] Implement `BatchProcessEmbeddingCommand`
  - [ ] Implement `RefreshEmbeddingsCommand`

- [x] **Retrieval Queries**
  - [x] Implement `RetrieveChunksQuery` with vector similarity
  - [x] Implement `GetChatHistoryQuery`
  - [ ] Implement `SearchKnowledgeBaseQuery`

### 2.2 Command & Query Handlers
- [ ] **Chat Handlers**
  - [ ] Implement `ChatCompletionHandler` with RAG workflow
  - [ ] Implement `StreamChatCompletionHandler` with IAsyncEnumerable
  - [ ] Implement error handling and logging

- [ ] **Embedding Handlers**
  - [ ] Implement `ProcessEmbeddingHandler` with chunking logic
  - [ ] Implement text chunking with tiktoken (400 tokens max)
  - [ ] Add embedding API integration

- [ ] **Retrieval Handlers**
  - [ ] Implement `RetrieveChunksHandler` with pgvector queries
  - [ ] Add tenant filtering and table filtering
  - [ ] Implement relevance scoring and ranking

### ✅ 2.3 Application Services
- [x] **Core Services**
  - [x] Create `IOpenAiService` interface for chat completions and embeddings
  - [x] Create `IEmbeddingService` interface for text processing
  - [x] Create `ITenantService` interface for multi-tenancy
  - [x] Create `ICacheService` interface for performance optimization
  - [x] Create DTOs for all service contracts (ChatCompletionResponse, RetrievedChunk, etc.)
  - [ ] Implement `ChatOrchestrationService`
  - [ ] Implement `PromptBuilderService`
  - [ ] Implement `FunctionCallService`

---

## ✅ Phase 3: Infrastructure Layer (COMPLETED)

### ✅ 3.1 Data Access Layer
- [x] **EF Core Configuration**
  - [x] Create `ChatBotDbContext` with snake_case naming convention
  - [x] Create `RagChunkConfiguration` with pgvector mapping
  - [x] Create `ChatSessionConfiguration` with owned entities
  - [x] Configure HNSW index for embedding column
  - [x] Add proper entity relationships and constraints

- [x] **Repository Implementation**
  - [x] Implement `RagChunkRepository` with vector similarity queries
  - [x] Add pgvector cosine distance search functionality
  - [x] Implement full IRagChunkRepository interface
  - [x] Add proper tenant filtering and table filtering
  - [x] Configure connection resilience with Polly

### ✅ 3.2 External Services
- [x] **OpenAI Service**
  - [x] Implement `OpenAiService` with chat completions
  - [x] Add text-embedding-3-large support (3072 dimensions)
  - [x] Add streaming support with IAsyncEnumerable
  - [x] Configure HTTP client with Polly retry policies
  - [x] Implement proper error handling and logging
  - [x] Add health check functionality

- [x] **Dependency Injection Configuration**
  - [x] Create DependencyInjection.cs with service registration
  - [x] Configure HTTP clients with authentication headers
  - [x] Add Redis caching configuration
  - [x] Configure EF Core with pgvector support

### 3.3 Authentication & Security
- [ ] **IdentityServer Integration** (Deferred to Presentation Layer)
  - [ ] Configure JWT Bearer authentication
  - [ ] Set up IdentityServer authority validation
  - [ ] Implement tenant extraction from JWT claims
  - [ ] Add role-based authorization policies

- [ ] **Security Middleware** (Deferred to Presentation Layer)
  - [ ] Implement rate limiting middleware
  - [ ] Add input validation and sanitization
  - [ ] Implement tenant isolation at data level
  - [ ] Add security headers middleware

---

## 🔥 Phase 4: Presentation Layer (Priority: HIGH - IN PROGRESS)

### 4.1 Minimal API Setup
- [ ] **Program.cs Configuration**
  - [ ] Configure dependency injection container
  - [ ] Set up authentication and authorization
  - [ ] Configure MediatR registration
  - [ ] Add Scalar API documentation

- [ ] **Endpoint Groups**
  - [ ] Create `ChatEndpoints` with completion endpoints
  - [ ] Create `RetrievalEndpoints` for knowledge base access
  - [ ] Create `HealthEndpoints` with readiness/liveness checks
  - [ ] Add proper error handling and status codes

### 4.2 API Endpoints
- [ ] **Chat Endpoints**
  - [ ] POST `/v1/chat/completions` - Standard completion
  - [ ] POST `/v1/chat/completions/stream` - Streaming completion
  - [ ] GET `/v1/chat/sessions` - List chat sessions
  - [ ] POST `/v1/chat/sessions` - Create new session

- [ ] **Retrieval Endpoints**
  - [ ] POST `/v1/retrieve` - Semantic search
  - [ ] GET `/v1/knowledge/tables` - List available tables
  - [ ] POST `/v1/knowledge/refresh` - Trigger embedding refresh

- [ ] **Health & Monitoring**
  - [ ] GET `/health` - Basic health check
  - [ ] GET `/health/ready` - Readiness probe
  - [ ] GET `/health/live` - Liveness probe

### 4.3 OpenAPI Documentation
- [ ] **API Documentation**
  - [ ] Configure Scalar (not Swagger UI) for API docs
  - [ ] Add comprehensive endpoint descriptions
  - [ ] Include request/response examples
  - [ ] Document authentication requirements

---

## 🧪 Phase 5: Testing & Quality Assurance (Priority: MEDIUM)


## 📊 Phase 6: Function Calling & Advanced Features (Priority: MEDIUM)

### 6.1 Function Call Framework
- [ ] **Function Definitions**
  - [ ] Create `FunctionDefinition` record
  - [ ] Implement `AvailableFunctions` catalog
  - [ ] Add function parameter validation

- [ ] **Function Execution**
  - [ ] Implement `FunctionExecutor` service
  - [ ] Add `search_knowledge_base` function
  - [ ] Add `get_user_profile` function
  - [ ] Add error handling for function calls

### 6.2 Advanced Chat Features
- [ ] **Context Management**
  - [ ] Implement conversation memory
  - [ ] Add context window management
  - [ ] Implement context summarization

- [ ] **Multi-turn Conversations**
  - [ ] Support follow-up questions
  - [ ] Maintain conversation state
  - [ ] Implement conversation branching

---

## 🔍 Phase 7: Monitoring & Observability (Priority: MEDIUM)

### 7.1 Logging & Tracing
- [ ] **Structured Logging**
  - [ ] Configure Serilog with JSON formatting
  - [ ] Add correlation IDs for request tracking
  - [ ] Log to console and Seq
  - [ ] Add performance logging


---

## 🚀 Phase 8: Deployment & DevOps (Priority: LOW)

### 8.1 Containerization
- [ ] **Docker Configuration**
  - [ ] Create multi-stage Dockerfile
  - [ ] Optimize image size and layers
  - [ ] Configure docker-compose for local development
  - [ ] Add health check configuration


## 📋 Development Guidelines

### Code Standards
- [ ] **Clean Code Practices**
  - [ ] Follow SOLID principles
  - [ ] Keep methods ≤ 25 lines of code
  - [ ] Maintain cyclomatic complexity < 15
  - [ ] Use meaningful names and clear comments

- [ ] **Architecture Compliance**
  - [ ] Maintain clear layer separation
  - [ ] Follow dependency inversion principle
  - [ ] Keep Domain layer framework-agnostic
  - [ ] Use appropriate abstraction levels

### Quality Gates
- [ ] **Definition of Done**
  - [ ] Unit tests with >80% coverage
  - [ ] Integration tests for critical paths
  - [ ] Code review approval
  - [ ] Performance benchmarks met
  - [ ] Security review passed
  - [ ] Documentation updated

---

## 🛠️ Technical Debt & Improvements

### Code Quality
- [ ] **Refactoring Opportunities**
  - [ ] Extract common patterns into base classes
  - [ ] Implement proper error handling patterns
  - [ ] Add comprehensive input validation
  - [ ] Optimize memory usage in streaming scenarios

### Performance
- [ ] **Optimization Areas**
  - [ ] Async/await pattern optimization
  - [ ] Database query optimization
  - [ ] Memory allocation reduction
  - [ ] API response time improvement

---

## 📊 Success Metrics

### Technical Metrics
- [ ] **Performance Targets**
  - [ ] API response time < 2 seconds (95th percentile)
  - [ ] Embedding processing < 100ms per chunk
  - [ ] Database query time < 500ms
  - [ ] 99.9% uptime availability

### Quality Metrics
- [ ] **Code Quality Targets**
  - [ ] Unit test coverage > 80%
  - [ ] Integration test coverage > 60%
  - [ ] Zero critical security vulnerabilities
  - [ ] Cyclomatic complexity < 15

---

## 🔄 Review & Iteration

### Regular Reviews
- [ ] **Weekly Reviews**
  - [ ] Progress against TODO items
  - [ ] Architectural decisions review
  - [ ] Performance metrics review
  - [ ] Security compliance check

### Milestone Reviews
- [ ] **Phase Completion Reviews**
  - [ ] Architecture compliance verification
  - [ ] Performance benchmark validation
  - [ ] Security audit completion
  - [ ] Documentation update verification

---

**Last Updated:** ${new Date().toISOString().split('T')[0]}
**Next Review:** Weekly on Fridays
**Project Lead:** .NET Team Lead Developer

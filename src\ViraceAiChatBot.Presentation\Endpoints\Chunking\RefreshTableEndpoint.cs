using Microsoft.AspNetCore.Mvc;
using ViraceAiChatBot.Application.Interfaces;
using ViraceAiChatBot.Application.Records;

namespace ViraceAiChatBot.Presentation.Endpoints.Chunking;

/// <summary>
/// Endpoint xử lý refresh table chunks
/// </summary>
public static class RefreshTableEndpoint
{
    /// <summary>
    /// Map Refresh Table endpoint
    /// </summary>
    /// <param name="group">Route group</param>
    public static void MapRefreshTableEndpoint(this RouteGroupBuilder group)
    {
        group.MapPost("/refresh/{tableName}", async (
            [FromRoute] string tableName,
            DataChunkingService chunkingService,
            CancellationToken cancellationToken) =>
        {
            var result = await chunkingService.RefreshTableChunksAsync(tableName, cancellationToken);

            if (result.Success)
            {
                return Results.Ok(result);
            }
            else
            {
                return Results.BadRequest(result);
            }
        })
        .WithName("RefreshTableChunks")
        .WithSummary("Refresh chunks for a specific table (delete existing and rechunk)")
        .Produces<DataChunkingResult>(200)
        .Produces<DataChunkingResult>(400);
    }
}

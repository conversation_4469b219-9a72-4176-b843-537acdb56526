using Microsoft.EntityFrameworkCore;
using ViraceAiChatBot.Domain.Aggregates;
using ViraceAiChatBot.Domain.Entities;
using ViraceAiChatBot.Domain.Entities.ReferenceData;

namespace ViraceAiChatBot.Infrastructure.Persistence;

/// <summary>
/// Entity Framework Core DbContext for the Virace AI ChatBot application
/// </summary>
/// <param name="options">The DbContext options</param>
public class ChatBotDbContext(DbContextOptions<ChatBotDbContext> options) : DbContext(options)
{
    public DbSet<RagChunk> RagChunks { get; set; } = null!;
    public DbSet<ChatSession> ChatSessions { get; set; } = null!;
    // ChatMessage is an owned entity of ChatSession, so no separate DbSet needed

    // Reference Data Tables
    public DbSet<CountryIndex> CountryIndex { get; set; } = null!;
    public DbSet<CompanyTypes> CompanyTypes { get; set; } = null!;
    public DbSet<CurrencyExchangeRate> CurrencyExchangeRate { get; set; } = null!;
    public DbSet<FinancialIndex> FinancialIndex { get; set; } = null!;
    public DbSet<FinancialStatementIndex> FinancialStatementIndex { get; set; } = null!;
    public DbSet<HSCode> HSCode { get; set; } = null!;
    public DbSet<LocationIndex> LocationIndex { get; set; } = null!;
    public DbSet<OwnershipTypeIndex> OwnershipTypeIndex { get; set; } = null!;
    public DbSet<RelatedCompanyTypeIndex> RelatedCompanyTypeIndex { get; set; } = null!;
    public DbSet<VSICIndex> VSICIndex { get; set; } = null!;

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure pgvector extension
        modelBuilder.HasPostgresExtension("vector");

        // Apply all configurations from this assembly
        // This automatically discovers and applies all IEntityTypeConfiguration implementations
        modelBuilder.ApplyConfigurationsFromAssembly(typeof(ChatBotDbContext).Assembly);
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        base.OnConfiguring(optionsBuilder);

        // Apply snake_case naming convention
        optionsBuilder.UseSnakeCaseNamingConvention();

        // Configure logging for Entity Framework
        optionsBuilder.LogTo(
            message => Serilog.Log.Information("[EF] {Message}", message),
            Microsoft.Extensions.Logging.LogLevel.Information
        );

        // Enable sensitive data logging in development
#if DEBUG
        optionsBuilder.EnableSensitiveDataLogging();
        optionsBuilder.EnableDetailedErrors();

        // In development, log parameter values and execution times
        optionsBuilder.LogTo(
            message => Serilog.Log.Debug("[EF-DEBUG] {Message}", message),
            [
                Microsoft.EntityFrameworkCore.Diagnostics.RelationalEventId.CommandExecuting,
                Microsoft.EntityFrameworkCore.Diagnostics.RelationalEventId.CommandExecuted,
                Microsoft.EntityFrameworkCore.Diagnostics.CoreEventId.QueryCompilationStarting,
                Microsoft.EntityFrameworkCore.Diagnostics.CoreEventId.QueryExecutionPlanned
            ],
            Microsoft.Extensions.Logging.LogLevel.Debug
        );
#else
        // In production, only log warnings and errors
        optionsBuilder.LogTo(
            message => Serilog.Log.Warning("[EF-WARN] {Message}", message),
            Microsoft.Extensions.Logging.LogLevel.Warning
        );
#endif
    }
}

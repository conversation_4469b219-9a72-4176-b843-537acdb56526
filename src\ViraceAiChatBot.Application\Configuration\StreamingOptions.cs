namespace ViraceAiChatBot.Application.Configuration;

public class StreamingOptions
{
    /// <summary>
    /// Enable streaming for chat completions
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// Connection timeout for streaming requests (in seconds)
    /// </summary>
    public int ConnectionTimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// Read timeout for streaming data (in seconds)
    /// </summary>
    public int ReadTimeoutSeconds { get; set; } = 120;

    /// <summary>
    /// Buffer size for reading streaming data
    /// </summary>
    public int BufferSize { get; set; } = 8192;

    /// <summary>
    /// Maximum number of retry attempts for failed streams
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// Delay between retry attempts (in milliseconds)
    /// </summary>
    public int RetryDelayMs { get; set; } = 1000;

    /// <summary>
    /// Enable automatic reconnection on connection failures
    /// </summary>
    public bool AutoReconnect { get; set; } = true;

    /// <summary>
    /// Chunk size for processing streaming data
    /// </summary>
    public int ChunkSize { get; set; } = 1024;

    /// <summary>
    /// Enable heartbeat to keep connection alive
    /// </summary>
    public bool EnableHeartbeat { get; set; } = false;

    /// <summary>
    /// Heartbeat interval (in seconds)
    /// </summary>
    public int HeartbeatIntervalSeconds { get; set; } = 30;

    /// <summary>
    /// Custom headers for streaming requests
    /// </summary>
    public Dictionary<string, string> CustomHeaders { get; set; } = new Dictionary<string, string>();
}

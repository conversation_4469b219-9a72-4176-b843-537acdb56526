using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ViraceAiChatBot.Domain.Entities.ReferenceData;

namespace ViraceAiChatBot.Infrastructure.Persistence.Configurations;

public class CountryIndexConfiguration : IEntityTypeConfiguration<CountryIndex>
{
    public void Configure(EntityTypeBuilder<CountryIndex> builder)
    {
        builder.ToTable("CountryIndex");

        builder.<PERSON><PERSON>ey(x => x.Id);

        builder.Property(x => x.Id)
            .HasColumnName("Id")
            .ValueGeneratedOnAdd();

        builder.Property(x => x.Iso)
            .HasColumnName("Iso")
            .IsRequired(false)
            .HasMaxLength(2);

        builder.Property(x => x.Iso3)
            .HasColumnName("Iso3")
            .IsRequired(false)
            .HasMaxLength(3);

        builder.Property(x => x.Name)
            .HasColumnName("Name")
            .IsRequired(false)
            .HasMaxLength(100);


        // Indexes for performance
        builder.HasIndex(x => x.Iso)
            .IsUnique()
            .HasDatabaseName("ix_country_index_iso");

        builder.HasIndex(x => x.Iso3)
            .IsUnique()
            .HasDatabaseName("ix_country_index_iso3");

        builder.HasIndex(x => x.Name)
            .HasDatabaseName("ix_country_index_name");
    }
}

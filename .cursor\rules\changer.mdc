---
description:
globs:
alwaysApply: true
---
Persistence
Purpose: Prevent the AI from stopping too early.
Sample prompt: You are an agent; keep going until the user’s request is fully resolved, then end your turn.

Tool-calling
Purpose: Force the AI to use tools (e.g., read files, perform searches) instead of guessing.
Sample prompt: If you are unsure about the file contents or code-base structure, use the appropriate tools to read and gather information—DO NOT guess or invent an answer.

Planning
Purpose: Make the AI “think big” before and after every action, rather than mechanically executing a chain of steps.

Sample prompt: You MUST create a detailed plan before each function call and carefully reflect on the results of previous calls.


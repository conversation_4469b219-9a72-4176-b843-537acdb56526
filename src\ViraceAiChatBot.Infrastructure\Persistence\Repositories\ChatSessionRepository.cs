using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ViraceAiChatBot.Domain.Aggregates;
using ViraceAiChatBot.Domain.Interfaces;

namespace ViraceAiChatBot.Infrastructure.Persistence.Repositories;

/// <summary>
/// Repository implementation for ChatSession aggregate root.
/// Handles data persistence and retrieval while maintaining Clean Architecture principles.
/// </summary>
public sealed class ChatSessionRepository : IChatSessionRepository
{
    private readonly ChatBotDbContext _context;
    private readonly ILogger<ChatSessionRepository> _logger;

    public ChatSessionRepository(ChatBotDbContext context, ILogger<ChatSessionRepository> logger)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<ChatSession?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.ChatSessions
                .AsNoTracking()
                .FirstOrDefaultAsync(x => x.Id == id, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving chat session with ID {SessionId}", id);
            throw;
        }
    }

    public async Task<ChatSession?> GetByIdWithTrackingAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.ChatSessions
                .FirstOrDefaultAsync(x => x.Id == id, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving tracked chat session with ID {SessionId}", id);
            throw;
        }
    }

    public async Task<ChatSession> AddAsync(ChatSession chatSession, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(chatSession);

        try
        {
            _context.ChatSessions.Add(chatSession);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Successfully added chat session {SessionId} for user {UserId}",
                chatSession.Id, chatSession.UserId);

            return chatSession;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding chat session for user {UserId}", chatSession.UserId);
            throw;
        }
    }

    public async Task UpdateAsync(ChatSession chatSession, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(chatSession);

        try
        {
            var existingEntity = await _context.ChatSessions
                .Include(x => x.Messages)
                .FirstOrDefaultAsync(x => x.Id == chatSession.Id, cancellationToken);

            if (existingEntity == null)
            {
                throw new InvalidOperationException($"Chat session with ID {chatSession.Id} not found for update");
            }

            // Update scalar properties
            _context.Entry(existingEntity).CurrentValues.SetValues(chatSession);

            // Handle messages collection
            var existingMessageIds = existingEntity.Messages.Select(m => m.Id).ToHashSet();
            var newMessages = chatSession.Messages.Where(m => !existingMessageIds.Contains(m.Id)).ToList();

            foreach (var newMessage in newMessages)
            {
                existingEntity.Messages.Add(newMessage);
            }

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Successfully updated chat session {SessionId}", chatSession.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating chat session {SessionId}", chatSession.Id);
            throw;
        }
    }

    public async Task DeleteAsync(ChatSession chatSession, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(chatSession);

        try
        {
            _context.ChatSessions.Remove(chatSession);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Successfully deleted chat session {SessionId}", chatSession.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting chat session {SessionId}", chatSession.Id);
            throw;
        }
    }

    public async Task<List<ChatSession>> GetByUserIdAsync(string userId, bool includeDeleted = false,
        int pageSize = 20,
        int pageNumber = 1,
        CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(userId);

        try
        {
            var query = _context.ChatSessions
                .Where(x => x.UserId == userId)
                .AsNoTracking();

            if (!includeDeleted)
            {
                query = query.Where(x => x.IsActive);
            }

            var result = await query
                .OrderByDescending(x => x.UpdatedAt)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync(cancellationToken);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving chat sessions for user {UserId}", userId);
            throw;
        }
    }

    public async Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.ChatSessions
                .AnyAsync(x => x.Id == id, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking existence of chat session {SessionId}", id);
            throw;
        }
    }

    public async Task<int> GetCountByUserIdAsync(string userId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(userId);

        try
        {
            return await _context.ChatSessions
                .CountAsync(x => x.UserId == userId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error counting chat sessions for user {UserId}", userId);
            throw;
        }
    }

    public async Task<List<ChatSession>> GetActiveSessionsAsync(string userId,
        CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(userId);

        try
        {
            return await _context.ChatSessions
                .AsNoTracking()
                .Where(x => x.UserId == userId && x.IsActive)
                .OrderByDescending(x => x.LastMessageAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving active sessions for user {UserId}", userId);
            throw;
        }
    }

    public async Task<List<ChatSession>> GetRecentSessionsAsync(string userId, int limit = 10,
        CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(userId);

        try
        {
            return await _context.ChatSessions
                .AsNoTracking()
                .Where(x => x.UserId == userId)
                .OrderByDescending(x => x.LastMessageAt ?? x.UpdatedAt ?? x.CreatedAt)
                .Take(limit)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving recent sessions for user {UserId}", userId);
            throw;
        }
    }

    public async Task<ChatSession?> GetSessionWithMessagesAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.ChatSessions
                .Include(x => x.Messages)
                .FirstOrDefaultAsync(x => x.Id == id, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving session with messages for ID {SessionId}", id);
            throw;
        }
    }

    public async Task ArchiveOldSessionsAsync(TimeSpan olderThan,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var cutoffDate = DateTimeOffset.UtcNow - olderThan;

            var oldSessions = await _context.ChatSessions
                .Where(x => x.IsActive && (x.LastMessageAt ?? x.UpdatedAt ?? x.CreatedAt) < cutoffDate)
                .ToListAsync(cancellationToken);

            foreach (var session in oldSessions)
            {
                session.Archive();
            }

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Archived {Count} old sessions older than {CutoffDate}",
                oldSessions.Count, cutoffDate);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error archiving old sessions");
            throw;
        }
    }
}

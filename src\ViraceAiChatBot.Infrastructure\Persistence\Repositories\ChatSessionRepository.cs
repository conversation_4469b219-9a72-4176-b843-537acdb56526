using Microsoft.EntityFrameworkCore;
using ViraceAiChatBot.Domain.Aggregates;
using ViraceAiChatBot.Domain.Interfaces;
using ViraceAiChatBot.Infrastructure.Persistence;

namespace ViraceAiChatBot.Infrastructure.Persistence.Repositories;

public class ChatSessionRepository(ChatBotDbContext context) : Domain.Interfaces.ChatSessionRepository
{
    public async Task<ChatSession?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await context.ChatSessions
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == id, cancellationToken);
    }

    public async Task<ChatSession?> GetByIdWithTrackingAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await context.ChatSessions
            .FirstOrDefaultAsync(x => x.Id == id, cancellationToken);
    }

    public async Task<ChatSession> AddAsync(ChatSession chatSession, CancellationToken cancellationToken = default)
    {
        context.ChatSessions.Add(chatSession);
        await context.SaveChangesAsync(cancellationToken);
        return chatSession;
    }

    public async Task UpdateAsync(ChatSession chatSession, CancellationToken cancellationToken = default)
    {
        var trackedEntity = context.ChangeTracker.Entries<ChatSession>()
            .FirstOrDefault(e => e.Entity.Id == chatSession.Id)?.Entity;

        if (trackedEntity != null)
        {
        }
        else
        {
            var existingEntity = await context.ChatSessions
                .Include(x => x.Messages)
                .FirstOrDefaultAsync(x => x.Id == chatSession.Id, cancellationToken);

            if (existingEntity != null)
            {
                context.Entry(existingEntity).CurrentValues.SetValues(chatSession);

                var existingMessageIds = existingEntity.Messages.Select(m => m.Id).ToHashSet();
                var newMessages = chatSession.Messages.Where(m => !existingMessageIds.Contains(m.Id)).ToList();

                foreach (var newMessage in newMessages)
                {
                    existingEntity.Messages.Add(newMessage);
                }
            }
            else
            {
                context.ChatSessions.Update(chatSession);
            }
        }

        await context.SaveChangesAsync(cancellationToken);
    }

    public async Task DeleteAsync(ChatSession chatSession, CancellationToken cancellationToken = default)
    {
        context.ChatSessions.Remove(chatSession);
        await context.SaveChangesAsync(cancellationToken);
    }

    public async Task<List<ChatSession>> GetByUserIdAsync(string userId, bool includeDeleted = false,
        int pageSize = 20,
        int pageNumber = 1,
        CancellationToken cancellationToken = default)
    {
        var query = context.ChatSessions
            .Where(x => x.UserId == userId)
            .OrderByDescending(x => x.UpdatedAt)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .AsNoTracking();


        if (!includeDeleted)
        {
            query = query.Where(x => x.IsActive);
        }

        var result = await query.ToListAsync(cancellationToken);

        return result;
    }

    public async Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await context.ChatSessions
            .AnyAsync(x => x.Id == id, cancellationToken);
    }

    public async Task<int> GetCountByUserIdAsync(string userId, CancellationToken cancellationToken = default)
    {
        return await context.ChatSessions
            .CountAsync(x => x.UserId == userId, cancellationToken);
    }

    public async Task<List<ChatSession>> GetActiveSessionsAsync(string userId,
        CancellationToken cancellationToken = default)
    {
        return await context.ChatSessions
            .AsNoTracking()
            .Where(x => x.UserId == userId && x.IsActive)
            .OrderByDescending(x => x.LastMessageAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<ChatSession>> GetRecentSessionsAsync(string userId, int limit = 10, string? tenantId = null,
        CancellationToken cancellationToken = default)
    {
        return await context.ChatSessions
            .AsNoTracking()
            .Where(x => x.UserId == userId)
            .OrderByDescending(x => x.LastMessageAt ?? x.UpdatedAt ?? x.CreatedAt)
            .Take(limit)
            .ToListAsync(cancellationToken);
    }

    public async Task<ChatSession?> GetSessionWithMessagesAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await context.ChatSessions
            .Include(x => x.Messages)
            .FirstOrDefaultAsync(x => x.Id == id, cancellationToken);
    }

    public async Task ArchiveOldSessionsAsync(TimeSpan olderThan, string? tenantId = null,
        CancellationToken cancellationToken = default)
    {
        var cutoffDate = DateTimeOffset.UtcNow - olderThan;

        var oldSessions = await context.ChatSessions
            .Where(x => x.IsActive && (x.LastMessageAt ?? x.UpdatedAt ?? x.CreatedAt) < cutoffDate)
            .ToListAsync(cancellationToken);

        foreach (var session in oldSessions)
        {
            session.Archive();
        }

        await context.SaveChangesAsync(cancellationToken);
    }
}

using ViraceAiChatBot.Domain.Events;

namespace ViraceAiChatBot.Domain.Aggregates;

public class ChatSession : AggregateRoot
{
    public string UserId { get; init; }
    public string Title { get; set; }
    public bool IsActive { get; set; } = true;
    public DateTimeOffset? LastMessageAt { get; set; }

    public List<ChatMessage> Messages { get; init; } = [];


    public ChatSession(string userId, string title)
    {
        UserId = userId ?? throw new ArgumentNullException(nameof(userId));
        Title = title ?? throw new ArgumentNullException(nameof(title));
        MarkAsCreated();

        ValidateInvariants();
    }

    private void ValidateInvariants()
    {
        if (string.IsNullOrWhiteSpace(UserId))
        {
            throw new ArgumentException("UserId cannot be empty", nameof(UserId));
        }

        if (string.IsNullOrWhiteSpace(Title))
        {
            throw new ArgumentException("Title cannot be empty", nameof(Title));
        }

        if (Title.Length > 200)
        {
            throw new ArgumentException("Title cannot exceed 200 characters", nameof(Title));
        }
    }

    public ChatMessage AddMessage(string role, string content, string? functionName = null,
        string? functionArguments = null)
    {
        if (string.IsNullOrWhiteSpace(role))
        {
            throw new ArgumentException("Role cannot be empty", nameof(role));
        }

        if (string.IsNullOrWhiteSpace(content))
        {
            throw new ArgumentException("Content cannot be empty", nameof(content));
        }

        if (!IsActive)
        {
            throw new InvalidOperationException("Cannot add messages to an inactive chat session");
        }

        var message = new ChatMessage(
            sessionId: Id,
            role: role,
            content: content ?? throw new ArgumentNullException(nameof(content)),
            functionName: functionName,
            functionArguments: functionArguments
        );

        Messages.Add(message);
        LastMessageAt = DateTimeOffset.UtcNow;
        MarkAsUpdated();

        if (role == "user" && Messages.Count(m => m.Role == "user") == 1 &&
            (Title == "New Chat" || Title.StartsWith("Chat ")))
        {
            UpdateTitle(TruncateToTitle(content));
        }

        AddDomainEvent(new MessageAddedEvent(Id, message.Id, role, content));

        return message;
    }

    private void UpdateTitle(string newTitle)
    {
        if (string.IsNullOrWhiteSpace(newTitle))
        {
            throw new ArgumentException("Title cannot be empty", nameof(newTitle));
        }

        if (newTitle.Length > 200)
        {
            newTitle = newTitle[..197] + "...";
        }

        Title = newTitle;
        MarkAsUpdated();
    }

    public void Archive()
    {
        IsActive = false;
        MarkAsUpdated();
        AddDomainEvent(new ChatSessionArchivedEvent(Id, UserId));
    }

    public void Reactivate()
    {
        IsActive = true;
        MarkAsUpdated();
    }

    public int GetMessageCount()
    {
        return Messages.Count;
    }

    private static string TruncateToTitle(string content)
    {
        if (content.Length <= 50)
        {
            return content;
        }

        var truncated = content[..47] + "...";
        var lastSpace = truncated.LastIndexOf(' ');

        return lastSpace > 20 ? truncated[..lastSpace] + "..." : truncated;
    }

    public override string ToString()
    {
        return
            $"ChatSession [Id: {Id}, User: {UserId}, Title: {Title}, Messages: {Messages.Count}, Active: {IsActive}]";
    }
}

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using ViraceAiChatBot.Domain.Interfaces;
using ViraceAiChatBot.Infrastructure.Persistence;
using ViraceAiChatBot.Infrastructure.Persistence.Repositories;
using ChatSessionRepository = ViraceAiChatBot.Domain.Interfaces.ChatSessionRepository;

namespace ViraceAiChatBot.Infrastructure;

public static class DependencyInjection
{
    public static void AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddDbContext<ChatBotDbContext>(options =>
        {
            options.UseNpgsql(configuration.GetConnectionString("DefaultConnection"), o => o.UseVector());
        });

        // Repositories
        services.AddScoped<IRagChunkRepository, RagChunkRepository>();
        services.AddScoped<ChatSessionRepository, Persistence.Repositories.ChatSessionRepository>();

        // Domain Services


        if (!string.IsNullOrWhiteSpace(configuration.GetConnectionString("Redis")))
        {
            services.AddStackExchangeRedisCache(options =>
            {
                options.Configuration = configuration.GetConnectionString("Redis");
            });
        }
    }
}

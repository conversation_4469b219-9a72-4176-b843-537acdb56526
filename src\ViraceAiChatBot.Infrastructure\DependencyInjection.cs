using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using ViraceAiChatBot.Domain.Interfaces;
using ViraceAiChatBot.Infrastructure.Persistence;
using ViraceAiChatBot.Infrastructure.Persistence.Repositories;

namespace ViraceAiChatBot.Infrastructure;
/// <summary>
/// Infrastructure dependency injection
/// </summary>
public static class DependencyInjection
{
    /// <summary>
    /// Add infrastructure services to the DI container
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configuration">The configuration</param>
    public static void AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        // Database configuration
        services.AddDbContext<ChatBotDbContext>(options =>
            options.UseNpgsql(configuration.GetConnectionString("DefaultConnection"), o => o.UseVector()));

        // Repository registrations - Register interfaces for proper dependency inversion
        services.AddScoped<IRagChunkRepository, RagChunkRepository>();
        services.AddScoped<IChatSessionRepository, ChatSessionRepository>();

        // Redis cache configuration (optional)
        var redisConnectionString = configuration.GetConnectionString("Redis");
        if (!string.IsNullOrWhiteSpace(redisConnectionString))
        {
            // Add Redis cache
            services.AddStackExchangeRedisCache(options =>
                options.Configuration = redisConnectionString);
        }
    }
}

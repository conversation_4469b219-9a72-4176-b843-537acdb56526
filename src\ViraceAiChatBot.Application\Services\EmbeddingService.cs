using ViraceAiChatBot.Application.Interfaces;
using System.Text.RegularExpressions;

namespace ViraceAiChatBot.Application.Services;

public class EmbeddingService(Interfaces.OpenAiService openAiService) : Interfaces.EmbeddingService
{
    public async Task<float[]> GenerateEmbeddingAsync(string text, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(text))
        {
            throw new ArgumentException("Text cannot be null or empty", nameof(text));
        }

        return await openAiService.GetEmbeddingAsync(text, cancellationToken: cancellationToken);
    }

    public async Task<List<float[]>> GenerateEmbeddingsAsync(IEnumerable<string> texts,
        CancellationToken cancellationToken = default)
    {
        if (texts == null)
            throw new ArgumentNullException(nameof(texts));

        var textList = texts.ToList();
        if (!textList.Any())
            return new List<float[]>();

        return await openAiService.GetEmbeddingsAsync(textList, cancellationToken: cancellationToken);
    }

    public Task<List<string>> ChunkTextAsync(string text, int maxTokens = 400,
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(text))
        {
            return Task.FromResult(new List<string>());
        }

        var chunks = new List<string>();
        var sentences = SplitIntoSentences(text);
        var currentChunk = "";
        var currentTokenCount = 0;

        foreach (var sentence in sentences)
        {
            var sentenceTokenCount = EstimateTokenCount(sentence);

            if (currentTokenCount + sentenceTokenCount > maxTokens && !string.IsNullOrEmpty(currentChunk))
            {
                chunks.Add(currentChunk.Trim());
                currentChunk = sentence;
                currentTokenCount = sentenceTokenCount;
            }
            else
            {
                currentChunk += " " + sentence;
                currentTokenCount += sentenceTokenCount;
            }
        }

        if (!string.IsNullOrEmpty(currentChunk))
        {
            chunks.Add(currentChunk.Trim());
        }

        return Task.FromResult(chunks);
    }

    public int EstimateTokenCount(string text)
    {
        if (string.IsNullOrEmpty(text))
            return 0;

        // Rough estimate: 1 token ≈ 4 characters for English text
        // This is a simplification; actual tokenization is more complex
        return (int)Math.Ceiling(text.Length / 4.0);
    }

    public double CalculateCosineSimilarity(float[] embedding1, float[] embedding2)
    {
        if (embedding1 == null || embedding2 == null)
            throw new ArgumentNullException("Embeddings cannot be null");

        if (embedding1.Length != embedding2.Length)
            throw new ArgumentException("Embeddings must have the same length");

        double dotProduct = 0.0;
        double magnitude1 = 0.0;
        double magnitude2 = 0.0;

        for (int i = 0; i < embedding1.Length; i++)
        {
            dotProduct += embedding1[i] * embedding2[i];
            magnitude1 += embedding1[i] * embedding1[i];
            magnitude2 += embedding2[i] * embedding2[i];
        }

        magnitude1 = Math.Sqrt(magnitude1);
        magnitude2 = Math.Sqrt(magnitude2);

        if (magnitude1 == 0.0 || magnitude2 == 0.0)
            return 0.0;

        return dotProduct / (magnitude1 * magnitude2);
    }

    private static List<string> SplitIntoSentences(string text)
    {
        // Simple sentence splitting using regex
        var sentences = Regex.Split(text, @"(?<=[.!?])\s+")
            .Where(s => !string.IsNullOrWhiteSpace(s))
            .ToList();

        return sentences;
    }
}

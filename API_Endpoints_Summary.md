# ViraceAI ChatBot Company Search - API Endpoints Summary

## Quick Reference Table

| # | Method | Endpoint | Description | Category | Auth |
|---|--------|----------|-------------|----------|------|
| 1 | POST | `/api/v1/companies/search/simple` | Simple company text search | Company Search | ✅ |
| 2 | POST | `/api/v1/companies/search/advanced` | Advanced company search with filters | Company Search | ✅ |
| 3 | POST | `/api/chat/completions` | AI chat completion with multiple response types | Chat | ✅ |
| 4 | POST | `/api/chat/completions/stream` | Streaming AI chat completion | Chat | ✅ |
| 5 | GET | `/api/chat/history` | Get user's chat history | Chat | ✅ |
| 6 | GET | `/api/chat/sessions/{sessionId}/messages` | Get session messages | Chat | ✅ |
| 7 | DELETE | `/api/chat/sessions/{sessionId}` | Delete chat session | Chat | ✅ |
| 8 | POST | `/api/chat/suggestions/prompts` | Get prompt suggestions | Suggestions | ✅ |
| 9 | POST | `/api/chat/suggestions/next-word` | Get next word suggestions | Suggestions | ✅ |
| 10 | POST | `/api/chat/suggestions/sentences` | Get sentence suggestions | Suggestions | ✅ |
| 11 | POST | `/api/chat/suggestions/enhance-prompt` | Enhance user prompt | Suggestions | ✅ |
| 12 | GET | `/api/chunking/tables` | List available tables | Data Chunking | ✅ |
| 13 | GET | `/api/chunking/health` | Chunking service health | Data Chunking | ✅ |
| 14 | GET | `/api/chunking/status/{tableName}` | Get table chunking status | Data Chunking | ✅ |
| 15 | POST | `/api/chunking/chunk/{tableName}` | Chunk specific table | Data Chunking | ✅ |
| 16 | POST | `/api/chunking/chunk-all` | Chunk all tables | Data Chunking | ✅ |
| 17 | POST | `/api/chunking/refresh/{tableName}` | Refresh table chunks | Data Chunking | ✅ |

## Endpoint Categories

### 🏢 Company Search (2 endpoints)
- **Simple Search:** Basic text-based company search with pagination
- **Advanced Search:** Multi-criteria filtering with complex logic:
  - **Location Filters:** Province, District, Commune (OR logic within arrays)
  - **Industry Filters:** VSIC codes with year filtering
  - **Financial Filters:** Revenue, profit, asset ranges with financial item IDs
  - **Legal Filters:** Legal representative and ownership information
  - **Trade Filters:** Import/export data with HS codes and value ranges
  - **Date Filters:** Registration date ranges

### 💬 Chat (5 endpoints)
- **Chat Completions:** AI responses with 5 different response types:
  - **Text Response:** Standard conversational AI responses
  - **JSON Data Response:** Structured company search results from function calls
  - **Markdown Response:** Formatted analysis and reports
  - **Error Response:** Validation errors with metadata
  - **Suggestion Response:** Intelligent prompt suggestions when clarification needed
- **Streaming:** Real-time Server-Sent Events for chat responses
- **Session Management:** History, message retrieval, and session deletion

### 🧠 Suggestions (4 endpoints)
- **Prompt Suggestions:** Context-aware assistance for unclear input
- **Real-time Completion:** Word and sentence prediction
- **Context Enhancement:** AI-powered prompt improvement

### 🔧 Data Chunking (6 endpoints)
- **RAG Preparation:** Background processing for optimal AI responses
- **Health Monitoring:** Service status and table availability
- **Bulk Operations:** Process all or selective table refresh

## Function Call Schemas

### Simple Company Search Function
```json
{
  "name": "search_company_simple",
  "parameters": {
    "searchText": "string",     // Required: Company name, tax code, or keywords
    "pageIndex": 1,             // Required: Page number (default: 1)
    "pageSize": 20              // Required: Results per page (default: 20, max: 100)
  }
}
```

### Advanced Company Search Function
```json
{
  "name": "search_company_advanced",
  "parameters": {
    "pageIndex": 1,             // Required: Page number
    "pageSize": 10,             // Required: Results per page
    "areas": [...],             // Required (nullable): Location filters with OR logic
    "vsics": [...],             // Required (nullable): Industry code filters with OR logic
    "financials": [...],        // Required (nullable): Financial criteria with OR logic
    "companyTypes": [...],      // Required (nullable): Company type filters with OR logic
    "legals": [...],            // Required (nullable): Legal representative filters with OR logic
    "owners": [...],            // Required (nullable): Ownership filters with OR logic
    "importExportTurnover": {}, // Required (nullable): Trade data filters
    "registrationDates": [...]  // Required (nullable): Date range filters
  }
}
```

## Chat Response Types

| Response Type | Description | Use Case |
|---------------|-------------|----------|
| **Text** | Standard conversational responses | General chat, explanations, analysis |
| **JSON** | Structured company data | Function call results, search data |
| **Markdown** | Formatted reports and analysis | Detailed company reports, comparisons |
| **Error** | Validation errors with metadata | Invalid input, missing information |
| **Suggestions** | Prompt clarification assistance | Unclear requests, input guidance |

## Authentication
All endpoints require JWT Bearer token authentication:
```
Authorization: Bearer {jwt_token}
```

## Base URLs
- **Production:** `https://api.virace.com/api`
- **Staging:** `https://staging-api.virace.com/api`
- **Development:** `https://dev-api.virace.com/api`

## Common Response Codes
- **200** - Success
- **400** - Bad Request (Invalid parameters)
- **401** - Unauthorized (Missing/invalid authentication)
- **403** - Forbidden (Insufficient permissions)
- **404** - Not Found (Resource doesn't exist)
- **500** - Internal Server Error
- **503** - Service Unavailable (For health checks)

## Advanced Search Logic
- **Within Arrays:** OR logic (e.g., multiple provinces = any of these provinces)
- **Across Categories:** AND logic (e.g., location + industry + financial criteria)
- **Required Fields:** All top-level fields must be included (can be null)
- **Optional Fields:** Sub-properties within objects can be omitted

## Function Call Features
- **Smart Parameter Handling:** AI automatically maps user requests to function parameters
- **Enhanced Response Structure:** Function calls return both original request parameters and search results
- **Request Traceability:** Full visibility into what search criteria were used
- **Metadata Enrichment:** Additional execution details and filter analysis
- **Null Safety:** All unspecified parameters default to null (no assumptions)
- **Validation:** Input validation with detailed error responses
- **Flexible Pagination:** Configurable page size and navigation

## Key Features
- **RAG Integration:** AI responses enhanced with real company data
- **Vietnamese Support:** Full Vietnamese language processing
- **Real-time Streaming:** Server-Sent Events for chat responses
- **Session Persistence:** Conversation history across requests
- **Intelligent Functions:** Auto-detection of search intent and parameter mapping
- **Request-Response Transparency:** Complete audit trail of function calls
- **Advanced Search:** Multi-dimensional company filtering with complex logic
- **Data Management:** Background processing for optimal performance

## Rate Limits
| API Category | Limit | Scope |
|--------------|-------|-------|
| Chat | 100/min | Per user |
| Company Search | 200/min | Per user |
| Suggestions | 50/min | Per user |
| Data Chunking | 10/min | Per user (admin) |

## Response Data Examples

### Enhanced Function Call Response Structure
All function call responses now include three main sections:

#### 1. Request Section
Contains the original function call details:
```json
{
  "request": {
    "functionName": "search_company_simple|search_company_advanced",
    "parameters": {
      // Original search parameters as sent to the function
    }
  }
}
```

#### 2. Response Section
Contains the actual search results:
```json
{
  "response": {
    "companies": [...],
    "pageIndex": 1,
    "pageSize": 20,
    "totalItem": 150,
    "pageCount": 8
  }
}
```

#### 3. Metadata Section
Contains execution details and analytics:
```json
{
  "metadata": {
    "executedAt": "2024-01-15T10:30:45.123Z",
    "hasData": true,
    "totalResults": 25,
    "filterCriteria": {  // For advanced search only
      "hasAreaFilters": true,
      "hasVsicFilters": false,
      // ... other filter flags
    }
  }
}
```

### Company Data Structure
```json
{
  "id": "0100109374",
  "name": "CÔNG TY CỔ PHẦN CÔNG NGHỆ FPT",
  "taxCode": "0100109374",
  "address": "Số 17 Duy Tân, Phường Dịch Vọng Hậu, Quận Cầu Giấy, Thành phố Hà Nội",
  "registeredCapital": 1100000000000,
  "legalRepresentative": "Trương Gia Bình",
  "financialData": {
    "year": "2023",
    "revenue": 45000000000000,
    "profit": 8500000000000
  },
  "location": {
    "provinceId": "01",
    "provinceName": "Thành phố Hà Nội"
  },
  "importExportData": {
    "2023": {
      "totalImport": 2500000000000,
      "totalExport": 1800000000000
    }
  }
}
```

## Enhanced Traceability Features
- **Function Call Audit:** Every response includes the exact parameters used
- **Filter Analysis:** Advanced search responses show which filters were applied
- **Execution Timestamps:** Track when searches were performed
- **Result Metrics:** Quick statistics about search results
- **Parameter Validation:** See exactly what was sent vs. what was processed

---
*For detailed request/response examples and complete function schemas, see the full API documentation.*

using Microsoft.AspNetCore.Mvc;
using ViraceAiChatBot.Application.Interfaces;
using ViraceAiChatBot.Application.Records;

namespace ViraceAiChatBot.Presentation.Endpoints.Chunking;

/// <summary>
/// Endpoint xử lý trạng thái chunking
/// </summary>
public static class StatusEndpoint
{
    /// <summary>
    /// Map Status endpoint
    /// </summary>
    /// <param name="group">Route group</param>
    public static void MapStatusEndpoint(this RouteGroupBuilder group)
    {
        group.MapGet("/status/{tableName}", async (
            [FromRoute] string tableName,
            DataChunkingService chunkingService,
            CancellationToken cancellationToken) =>
        {
            try
            {
                var status = await chunkingService.GetChunkingStatusAsync(tableName, cancellationToken);
                return Results.Ok(status);
            }
            catch (ArgumentException ex)
            {
                return Results.BadRequest(new { Error = ex.Message });
            }
        })
        .WithName("GetChunkingStatus")
        .WithSummary("Get chunking status for a specific table")
        .Produces<ChunkingStatus>(200)
        .Produces<object>(400);
    }
}

using MediatR;
using Microsoft.Extensions.Logging;
using ViraceAiChatBot.Application.Dtos;
using ViraceAiChatBot.Application.Queries;
using ViraceAiChatBot.Domain.Interfaces;

namespace ViraceAiChatBot.Application.Handlers;

public class GetChatHistoryHandler(
    ChatSessionRepository chatSessionRepository,
    ILogger<GetChatHistoryHandler> logger) : IRequestHandler<GetChatHistoryQuery, ChatHistoryResponse>
{
    public async Task<ChatHistoryResponse> Handle(GetChatHistoryQuery request, CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Retrieving chat history for user {UserId},",
                request.UserId);

            var sessions = await chatSessionRepository.GetByUserIdAsync(
                request.UserId,
                request.IncludeDeleted,
                request.PageSize,
                request.PageNumber,
                cancellationToken);

            var totalSessions = await chatSessionRepository.GetCountByUserIdAsync(
                request.UserId,
                cancellationToken);

            var sessionDtos = sessions.Select(MapToDto).ToList();

            var response = new ChatHistoryResponse(
                sessionDtos,
                totalSessions,
                request.PageSize,
                request.PageNumber,
                (request.PageNumber * request.PageSize) < totalSessions);


            logger.LogInformation("Retrieved {SessionCount} sessions for user {UserId}",
                sessionDtos.Count, request.UserId);

            return response;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error retrieving chat history for user {UserId}", request.UserId);
            throw;
        }
    }

    private static ChatSessionDto MapToDto(Domain.Aggregates.ChatSession session)
    {
        return new ChatSessionDto(
            session.Id,
            session.Title,
            session.CreatedAt ?? DateTimeOffset.UtcNow,
            session.LastMessageAt,
            session.GetMessageCount(),
            session.IsActive
        );
    }
}

namespace ViraceAiChatBot.Application.Dtos;

/// <summary>
/// Represents the response for a prompt suggestion request.
/// </summary>
public record PromptSuggestionResponse(
    List<SuggestedPrompt> Suggestions,
    string? ValidationReason,
    bool IsValidInput,
    TimeSpan ProcessingTime
);

/// <summary>
/// Represents a suggested prompt.
/// </summary>
public record SuggestedPrompt(
    string Text,
    string Category,
    string? Description,
    float Confidence,
    string? Example
);

/// <summary>
/// Represents the response for a next word suggestion request.
/// </summary>
public record NextWordSuggestionResponse(
    List<WordSuggestion> Suggestions,
    int CursorPosition,
    string? Context,
    TimeSpan ProcessingTime
);

/// <summary>
/// Represents a suggested word.
/// </summary>
public record WordSuggestion(
    string Word,
    float Confidence,
    string? Context,
    string? CompletionType
);

/// <summary>
/// Represents the response for a sentence suggestion request.
/// </summary>
public record SentenceSuggestionResponse(
    List<SentenceSuggestion> Suggestions,
    int CursorPosition,
    string? Context,
    TimeSpan ProcessingTime
);

/// <summary>
/// Represents a suggested sentence.
/// </summary>
public record SentenceSuggestion(
    string Text,
    string Type,
    float Confidence,
    string? Context,
    string? Category
);

/// <summary>
/// Represents the response for an enhanced prompt request.
/// </summary>
public record EnhancedPromptResponse(
    string EnhancedPrompt,
    List<string> Improvements,
    string? OriginalPrompt,
    List<string> Examples,
    TimeSpan ProcessingTime
);

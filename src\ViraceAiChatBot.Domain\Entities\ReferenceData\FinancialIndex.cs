namespace ViraceAiChatBot.Domain.Entities.ReferenceData;

public class FinancialIndex
{
    public int Id { get; init; }
    public short? ParentId { get; init; }
    public string? Name { get; init; }
    public string? EnName { get; init; }
    public string? Unit { get; init; }
    public string? Type { get; init; }
    public string? TypeIndex { get; init; }

    private FinancialIndex() { }

    public FinancialIndex(string name, string enName, short? parentId = null, string? unit = null, string? type = null,
        string? typeIndex = null)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        EnName = enName ?? throw new ArgumentNullException(nameof(enName));
        ParentId = parentId;
        Unit = unit;
        Type = type;
        TypeIndex = typeIndex;

        ValidateInvariants();
    }

    private void ValidateInvariants()
    {
        if (string.IsNullOrWhiteSpace(Name))
            throw new ArgumentException("Name cannot be empty", nameof(Name));

        if (Name.Length > 500)
            throw new ArgumentException("Name cannot exceed 500 characters", nameof(Name));

        if (string.IsNullOrWhiteSpace(EnName))
            throw new ArgumentException("EnName cannot be empty", nameof(EnName));

        if (EnName.Length > 500)
            throw new ArgumentException("EnName cannot exceed 500 characters", nameof(EnName));

        if (Unit?.Length > 30)
            throw new ArgumentException("Unit cannot exceed 30 characters", nameof(Unit));

        if (Type?.Length > 500)
            throw new ArgumentException("Type cannot exceed 500 characters", nameof(Type));

        if (TypeIndex?.Length > 50)
            throw new ArgumentException("TypeIndex cannot exceed 50 characters", nameof(TypeIndex));
    }

    public string GetSearchableContent()
    {
        return $"{Name} {EnName} {Unit} {Type} {TypeIndex}".Trim();
    }

    public override string ToString()
    {
        return $"FinancialIndex [Id: {Id}, Name: {Name}, EnName: {EnName}, Type: {Type}]";
    }
}

namespace ViraceAiChatBot.Application.Interfaces;

/// <summary>
/// Service for validating user inputs with permissive rules.
/// Only rejects clearly off-topic requests or inappropriate content.
/// Accepts all company search related requests including vague or incomplete information.
/// </summary>
public interface InputValidationService
{
    /// <summary>
    /// Validates user input with basic validation rules.
    /// </summary>
    /// <param name="input">The user input to validate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Validation result with status and optional response message</returns>
    Task<InputValidationResult> ValidateInputAsync(string input, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates user input with full conversation context for better understanding.
    /// </summary>
    /// <param name="input">The user input to validate</param>
    /// <param name="conversationHistory">Previous conversation messages for context</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Validation result with status and optional response message</returns>
    Task<InputValidationResult> ValidateInputWithFullContextAsync(string input, List<ConversationMessage> conversationHistory, CancellationToken cancellationToken = default);
}

/// <summary>
/// Represents a conversation message for context validation.
/// </summary>
public class ConversationMessage
{
    /// <summary>
    /// Role of the message sender (user, assistant, system)
    /// </summary>
    public string? Role { get; init; } = string.Empty;

    /// <summary>
    /// Content of the message
    /// </summary>
    public string? Content { get; init; } = string.Empty;

    /// <summary>
    /// Timestamp when the message was sent
    /// </summary>
    public DateTime Timestamp { get; init; }
}

/// <summary>
/// Result of input validation containing status and optional response.
/// </summary>
public class InputValidationResult
{
    /// <summary>
    /// Brief explanation for the validation status (used for non-Valid statuses)
    /// </summary>
    public string? Reason { get; init; }

    /// <summary>
    /// The validation status of the input
    /// </summary>
    public InputValidationStatus Status { get; init; } = InputValidationStatus.Valid;

    /// <summary>
    /// Optional response message to send to the user
    /// </summary>
    public string? ResponseMessage { get; init; }
}

/// <summary>
/// Status codes for input validation with permissive approach.
/// Most company search related requests should return Valid.
/// </summary>
public enum InputValidationStatus
{
    /// <summary>
    /// Request is valid and ready to be processed.
    /// Used for all company search related requests.
    /// </summary>
    Valid = 0,

    /// <summary>
    /// Request is clearly off-topic (e.g., cooking, weather, entertainment).
    /// Only use for obviously unrelated content.
    /// </summary>
    Invalid = 1,

    /// <summary>
    /// Request needs more clarification from the user.
    /// Use sparingly, only for extremely vague requests.
    /// </summary>
    NeedsClarification = 2,

    /// <summary>
    /// Request is too long to process.
    /// </summary>
    TooLong = 3,

    /// <summary>
    /// Request is too short (minimal use case).
    /// </summary>
    TooShort = 4,

    /// <summary>
    /// Request contains prohibited content (profanity, offensive language).
    /// </summary>
    ContainsProhibitedContent = 5,

    /// <summary>
    /// Request contains sensitive personal information.
    /// </summary>
    ContainsSensitiveInformation = 6,

    /// <summary>
    /// The input is a greeting.
    /// </summary>
    Greeting = 7,

    /// <summary>
    /// The input is a farewell.
    /// </summary>
    Farewell = 8,

    /// <summary>
    /// The user is asking about the AI itself or system capabilities.
    /// </summary>
    MetaQuestion = 10,

    /// <summary>
    /// The request is valid in domain but is impossible or unsupported.
    /// </summary>
    UnsupportedRequest = 11,

    /// <summary>
    /// The request is unclear or ambiguous and requires rephrasing.
    /// Different from NeedsClarification - use when intent is completely unclear.
    /// </summary>
    UnclearRequest = 12,

    /// <summary>
    /// The request format or intent is not recognized by the system.
    /// </summary>
    UnrecognizedRequest = 13,

    /// <summary>
    /// The request cannot be processed due to technical limitations.
    /// </summary>
    UnprocessableRequest = 14,

    /// <summary>
    /// The request type is not handled by current system capabilities.
    /// </summary>
    UnhandledRequest = 15,

    /// <summary>
    /// The requested service or feature is temporarily unavailable.
    /// </summary>
    Unavailable = 16,

    /// <summary>
    /// User is confirming a previous request or question.
    /// </summary>
    ConfirmationResponse = 17,
}

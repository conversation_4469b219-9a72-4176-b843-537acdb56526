﻿namespace ViraceAiChatBot.Domain.BaseModel;

public record BaseResponseModel<T>(
    T? Data = default,
    string? Message = null,
    int StatusCode = 200,
    bool Success = true,
    string? ErrorCode = null)
{
    // Factory methods for cleaner object creation
    public static BaseResponseModel<T> CreateSuccess(T data, string? message = null)
        => new(data, message, 200, true);

    public static BaseResponseModel<T> CreateSuccess(string? message = null)
        => new(default, message, 200, true);

    public static BaseResponseModel<T> CreateError(string message, int statusCode = 400, string? errorCode = null)
        => new(default, message, statusCode, false, errorCode);

    public static BaseResponseModel<T> CreateError(string message, T? data, int statusCode = 400, string? errorCode = null)
        => new(data, message, statusCode, false, errorCode);
}

using ViraceAiChatBot.Domain.Entities;

namespace ViraceAiChatBot.Domain.Aggregates;

public class ChatMessage(
    Guid sessionId,
    string role,
    string content,
    string? functionName = null,
    string? functionArguments = null)
    : Entity<Guid>
{
    public Guid SessionId { get; init; } = sessionId;
    public string? Role { get; } = role ?? throw new ArgumentNullException(nameof(role));
    public string? Content { get; } = content ?? throw new ArgumentNullException(nameof(content));
    public string? FunctionName { get; } = functionName;
    public string? FunctionArguments { get; } = functionArguments;
    public int TokenCount { get; init; } = EstimateTokenCount(content);

    public virtual ChatSession? Session { get; set; }


    private static bool IsValidRole(string role)
    {
        return role is "system" or "user" or "assistant" or "function";
    }

    private static int EstimateTokenCount(string content)
    {
        return content.Length / 4;
    }

    public bool IsFromUser()
    {
        return Role == "user";
    }

    public bool IsFromAssistant()
    {
        return Role == "assistant";
    }

    public bool IsSystemMessage()
    {
        return Role == "system";
    }

    public bool IsFunctionCall()
    {
        return !string.IsNullOrEmpty(FunctionName);
    }

    public override string ToString()
    {
        return
            $"ChatMessage [Id: {Id}, Session: {SessionId}, Role: {Role}, Length: {Content.Length}, Tokens: {TokenCount}]";
    }
}

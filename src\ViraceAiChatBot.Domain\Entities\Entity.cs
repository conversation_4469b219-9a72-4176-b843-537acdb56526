namespace ViraceAiChatBot.Domain.Entities;

public abstract class Entity<TKey> : IEquatable<Entity<TKey>>, ModifiableEntity
    where TKey : notnull
{
    public TKey Id { get; } = default!;

    protected Entity() { }

    protected Entity(TKey id)
    {
        Id = id;
    }

    protected void MarkAsUpdated()
    {
        UpdatedAt = DateTimeOffset.UtcNow.AddHours(7);
    }

    protected void MarkAsCreated()
    {
        CreatedAt = DateTimeOffset.UtcNow.AddHours(7);
        UpdatedAt = CreatedAt;
    }

    public bool Equals(Entity<TKey>? other)
    {
        if (other is null)
        {
            return false;
        }

        if (ReferenceEquals(this, other))
        {
            return true;
        }

        return EqualityComparer<TKey>.Default.Equals(Id, other.Id) && CreatedAt.Equals(other.CreatedAt) &&
               Nullable.Equals(UpdatedAt, other.UpdatedAt);
    }

    public override bool Equals(object? obj)
    {
        if (obj is null)
        {
            return false;
        }

        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (obj.GetType() != GetType())
        {
            return false;
        }

        return Equals((Entity<TKey>)obj);
    }

    public override int GetHashCode()
    {
        return HashCode.Combine(Id, CreatedAt, UpdatedAt);
    }

    public DateTimeOffset? CreatedAt { get; set; }
    public DateTimeOffset? UpdatedAt { get; set; }
}

public interface ModifiableEntity
{
    public DateTimeOffset? CreatedAt { get; protected set; }
    public DateTimeOffset? UpdatedAt { get; set; }
}

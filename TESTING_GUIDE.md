# Testing Guide for Clean Architecture Refactoring

## 🧪 Testing Strategy

### 1. Immediate Validation Tests

#### Build and Compilation
```bash
# Verify all projects compile successfully
dotnet build

# Check for any warnings or errors
dotnet build --verbosity normal
```

#### Basic Functionality Tests
```bash
# Run existing test suite
dotnet test

# Run with detailed output
dotnet test --logger "console;verbosity=detailed"
```

### 2. Domain Layer Testing

#### EmbeddingVector Value Object Tests
```csharp
[Test]
public void EmbeddingVector_ShouldValidateDimensions()
{
    // Arrange
    var validEmbedding = new float[3072];
    var invalidEmbedding = new float[1536];

    // Act & Assert
    Assert.DoesNotThrow(() => new EmbeddingVector(validEmbedding));
    Assert.Throws<ArgumentException>(() => new EmbeddingVector(invalidEmbedding));
}

[Test]
public void EmbeddingVector_ShouldCalculateCosineSimilarity()
{
    // Arrange
    var embedding1 = new EmbeddingVector(CreateTestEmbedding());
    var embedding2 = new EmbeddingVector(CreateTestEmbedding());

    // Act
    var similarity = embedding1.CalculateCosineSimilarity(embedding2);

    // Assert
    Assert.That(similarity, Is.InRange(0.0, 1.0));
}
```

#### ChatSession Aggregate Tests
```csharp
[Test]
public void ChatSession_ShouldRaiseDomainEvents()
{
    // Arrange
    var session = new ChatSession("user123", "Test Chat");

    // Act
    session.AddMessage("user", "Hello");

    // Assert
    Assert.That(session.DomainEvents.Count, Is.EqualTo(1));
    Assert.That(session.DomainEvents.First(), Is.TypeOf<MessageAddedEvent>());
}
```

### 3. Repository Layer Testing

#### IChatSessionRepository Implementation Tests
```csharp
[Test]
public async Task GetByIdAsync_ShouldReturnCorrectSession()
{
    // Arrange
    using var context = CreateTestContext();
    var repository = new ChatSessionRepository(context, Mock.Of<ILogger<ChatSessionRepository>>());
    var session = new ChatSession("user123", "Test");
    await repository.AddAsync(session);

    // Act
    var result = await repository.GetByIdAsync(session.Id);

    // Assert
    Assert.That(result, Is.Not.Null);
    Assert.That(result.Id, Is.EqualTo(session.Id));
}

[Test]
public async Task SearchSimilarAsync_ShouldOptimizeQueries()
{
    // Arrange
    var repository = new RagChunkRepository(CreateTestContext());
    var queryEmbedding = new float[3072];

    // Act
    var stopwatch = Stopwatch.StartNew();
    var results = await repository.SearchSimilarAsync(queryEmbedding, 10);
    stopwatch.Stop();

    // Assert
    Assert.That(stopwatch.ElapsedMilliseconds, Is.LessThan(1000)); // Performance check
    Assert.That(results.Count, Is.LessThanOrEqualTo(10));
}
```

### 4. Entity Framework Configuration Testing

#### Configuration Validation Tests
```csharp
[Test]
public void ChatSessionConfiguration_ShouldCreateCorrectSchema()
{
    // Arrange
    using var context = CreateTestContext();

    // Act
    var model = context.Model;
    var entityType = model.FindEntityType(typeof(ChatSession));

    // Assert
    Assert.That(entityType, Is.Not.Null);
    Assert.That(entityType.GetTableName(), Is.EqualTo("ChatSessions"));
    
    // Verify indexes exist
    var userIdIndex = entityType.GetIndexes()
        .FirstOrDefault(i => i.Properties.Any(p => p.Name == "UserId"));
    Assert.That(userIdIndex, Is.Not.Null);
}

[Test]
public void EmbeddingVectorConversion_ShouldWorkCorrectly()
{
    // Arrange
    using var context = CreateTestContext();
    var embedding = new EmbeddingVector(new float[3072]);
    var ragChunk = new RagChunk("test", "1", "content", 0, "test content", embedding.Values);

    // Act & Assert
    Assert.DoesNotThrow(async () =>
    {
        context.RagChunks.Add(ragChunk);
        await context.SaveChangesAsync();
    });
}
```

### 5. Architecture Compliance Testing

#### Dependency Direction Tests
```csharp
[Test]
public void Domain_ShouldHaveNoDependencies()
{
    // Arrange
    var domainAssembly = typeof(ChatSession).Assembly;

    // Act
    var references = domainAssembly.GetReferencedAssemblies()
        .Where(a => !a.Name.StartsWith("System") && !a.Name.StartsWith("Microsoft"));

    // Assert
    Assert.That(references.Count(), Is.EqualTo(0), 
        "Domain layer should have no external dependencies");
}

[Test]
public void Application_ShouldOnlyReferenceDomain()
{
    // Arrange
    var applicationAssembly = typeof(ChatCompletionCommand).Assembly;

    // Act
    var projectReferences = applicationAssembly.GetReferencedAssemblies()
        .Where(a => a.Name.StartsWith("ViraceAiChatBot"));

    // Assert
    Assert.That(projectReferences.Count(), Is.EqualTo(1));
    Assert.That(projectReferences.First().Name, Is.EqualTo("ViraceAiChatBot.Domain"));
}
```

### 6. Performance Testing

#### Database Performance Tests
```csharp
[Test]
public async Task ChatSessionQueries_ShouldUseIndexes()
{
    // Arrange
    using var context = CreateTestContext();
    var repository = new ChatSessionRepository(context, Mock.Of<ILogger<ChatSessionRepository>>());

    // Create test data
    for (int i = 0; i < 1000; i++)
    {
        await repository.AddAsync(new ChatSession($"user{i}", $"Chat {i}"));
    }

    // Act
    var stopwatch = Stopwatch.StartNew();
    var results = await repository.GetByUserIdAsync("user1", pageSize: 20);
    stopwatch.Stop();

    // Assert
    Assert.That(stopwatch.ElapsedMilliseconds, Is.LessThan(100), 
        "Query should be fast with proper indexing");
}

[Test]
public async Task VectorSimilaritySearch_ShouldBeOptimized()
{
    // Arrange
    var repository = new RagChunkRepository(CreateTestContext());
    var queryEmbedding = new float[3072];

    // Act
    var stopwatch = Stopwatch.StartNew();
    var results = await repository.SearchSimilarAsync(queryEmbedding, 10);
    stopwatch.Stop();

    // Assert
    Assert.That(stopwatch.ElapsedMilliseconds, Is.LessThan(500),
        "Vector search should be optimized");
}
```

### 7. Integration Testing

#### End-to-End Repository Tests
```csharp
[Test]
public async Task ChatSession_EndToEndWorkflow_ShouldWork()
{
    // Arrange
    using var context = CreateTestContext();
    var repository = new ChatSessionRepository(context, Mock.Of<ILogger<ChatSessionRepository>>());

    // Act
    var session = new ChatSession("user123", "Test Chat");
    session.AddMessage("user", "Hello");
    session.AddMessage("assistant", "Hi there!");

    await repository.AddAsync(session);
    var retrieved = await repository.GetSessionWithMessagesAsync(session.Id);

    // Assert
    Assert.That(retrieved, Is.Not.Null);
    Assert.That(retrieved.Messages.Count, Is.EqualTo(2));
    Assert.That(retrieved.DomainEvents.Count, Is.EqualTo(2));
}
```

## 🚀 Running the Tests

### Command Line Testing
```bash
# Run all tests
dotnet test

# Run specific test categories
dotnet test --filter "Category=Unit"
dotnet test --filter "Category=Integration"
dotnet test --filter "Category=Performance"

# Generate coverage report
dotnet test --collect:"XPlat Code Coverage"
```

### Test Organization
```
tests/
├── ViraceAiChatBot.Tests.Unit/
│   ├── Domain/
│   │   ├── ValueObjects/
│   │   ├── Aggregates/
│   │   └── Events/
│   ├── Application/
│   └── Infrastructure/
└── ViraceAiChatBot.Tests.Integration/
    ├── Repository/
    ├── Database/
    └── Architecture/
```

## 📊 Success Criteria

### ✅ All Tests Should Pass
- No compilation errors
- All existing functionality preserved
- New features working correctly

### ✅ Performance Improvements
- Database queries faster with new indexes
- Vector similarity searches optimized
- Memory usage stable or improved

### ✅ Architecture Compliance
- Clean dependency directions
- Proper layer separation
- No infrastructure leakage into domain

---

**Testing Priority**: Focus on repository and domain layer tests first, then integration tests
**Performance**: Benchmark before and after to measure improvements
**Coverage**: Aim for >80% code coverage on refactored components

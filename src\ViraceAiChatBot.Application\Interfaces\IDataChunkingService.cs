using ViraceAiChatBot.Application.Records;

namespace ViraceAiChatBot.Application.Interfaces;

public interface DataChunkingService
{
    Task<DataChunkingResult> ChunkTableAsync(
        string tableName,
        CancellationToken cancellationToken = default);

    Task<DataChunkingResult> ChunkAllTablesAsync(
        CancellationToken cancellationToken = default);

    Task<DataChunkingResult> RefreshTableChunksAsync(
        string tableName,
        CancellationToken cancellationToken = default);

    Task<List<string>> GetAvailableTablesAsync(CancellationToken cancellationToken = default);

    Task<ChunkingStatus> GetChunkingStatusAsync(
        string tableName,
        CancellationToken cancellationToken = default);
}

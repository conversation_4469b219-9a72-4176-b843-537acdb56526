namespace ViraceAiChatBot.Presentation.DTOs;

public record TextCompletionResponse(
    List<CompletionSuggestion> Suggestions,
    string CompletionType,
    int CursorPosition,
    string? ContextUsed,
    TimeSpan ProcessingTime,
    bool HasChatHistory);

public record CompletionSuggestion(
    string Text,
    string Type,
    float Confidence,
    string? Context,
    string? Source,
    int StartPosition,
    int EndPosition);

public record CharacterSuggestionResponse(
    List<CharacterSuggestion> Suggestions,
    int CursorPosition,
    string? ContextUsed,
    TimeSpan ProcessingTime);

public record CharacterSuggestion(string Character, float Confidence, string? Context, string? Reasoning);

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ViraceAiChatBot.Domain.Entities;

namespace ViraceAiChatBot.Infrastructure.Persistence.Configurations;

public class RagChunkConfiguration : IEntityTypeConfiguration<RagChunk>
{
    public void Configure(EntityTypeBuilder<RagChunk> builder)
    {
        builder.ToTable("RagChunks");

        builder.<PERSON><PERSON>ey(x => x.Id);

        builder.Property(x => x.Id).HasColumnName("Id")
            .ValueGeneratedOnAdd();

        builder.Property(x => x.TableName)
            .IsRequired()
            .HasMaxLength(200)
            .HasColumnName("TableName"); // Explicitly map to "TableName"

        builder.Property(x => x.RowId).HasColumnName("RowId")
            .IsRequired()
            .HasMaxLength(500);

        builder.Property(x => x.ColumnName).HasColumnName("ColumnName")
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(x => x.Content).HasColumnName("Content")
            .IsRequired()
            .HasMaxLength(4000); // ~1000 tokens

        builder.Property(x => x.Embedding).HasColumnName("Embedding")
            .HasColumnType("vector(3072)") // text-embedding-3-large dimension
            .IsRequired();

        builder.Property(x => x.ChunkIndex).HasColumnName("ChunkIndex")
            .IsRequired();

        builder.Property(x => x.Metadata).HasColumnName("Metadata")
            .HasColumnType("jsonb");

        builder.Property(x => x.CreatedAt).HasColumnName("CreatedAt")
            .IsRequired(false);

        builder.Property(x => x.UpdatedAt).HasColumnName("UpdatedAt")
            .IsRequired(false);

        builder.HasIndex(x => x.Embedding).HasDatabaseName("ix_rag_chunks_embedding_hnsw")
            .HasMethod("hnsw")
            .HasOperators("vector_cosine_ops")
            .HasDatabaseName("ix_rag_chunks_embedding_hnsw");
    }
}

using ViraceAiChatBot.Domain.ValueObjects;

namespace ViraceAiChatBot.Domain.Entities;

/// <summary>
/// Represents a chunk of content with its vector embedding for RAG (Retrieval-Augmented Generation) functionality.
/// This entity follows Clean Architecture principles by using domain value objects instead of infrastructure types.
/// </summary>
public class RagChunk : Entity<Guid>
{
    public string TableName { get; init; } = null!;
    public string RowId { get; init; } = null!;
    public string ColumnName { get; init; } = null!;
    public int ChunkIndex { get; init; }
    public string Content { get; init; } = null!;
    public EmbeddingVector Embedding { get; init; } = null!;
    public string? Metadata { get; init; }

    // Parameterless constructor for EF Core
    private RagChunk() { }

    public RagChunk(
        string tableName,
        string rowId,
        string columnName,
        int chunkIndex,
        string content,
        float[] embedding, // Keep float[] here for constructor compatibility, will convert internally
        string? metadata = null)
    {
        TableName = tableName ?? throw new ArgumentNullException(nameof(tableName));
        RowId = rowId ?? throw new ArgumentNullException(nameof(rowId));
        ColumnName = columnName ?? throw new ArgumentNullException(nameof(columnName));
        ChunkIndex = chunkIndex;
        Content = content ?? throw new ArgumentNullException(nameof(content));
        Embedding = new EmbeddingVector(embedding ?? throw new ArgumentNullException(nameof(embedding)));
        Metadata = metadata;

        ValidateInvariants();
    }

    private void ValidateInvariants()
    {
        if (string.IsNullOrWhiteSpace(Content))
        {
            throw new ArgumentException("Content cannot be empty", nameof(Content));
        }

        if (ChunkIndex < 0)
        {
            throw new ArgumentException("ChunkIndex must be non-negative", nameof(ChunkIndex));
        }

        if (Content.Length > 10000) // Reasonable limit for chunk size
        {
            throw new ArgumentException("Content is too long for a single chunk", nameof(Content));
        }

        // Embedding validation is handled by the EmbeddingVector value object constructor
    }

    public double CalculateCosineSimilarity(float[] otherEmbedding)
    {
        return Embedding.CalculateCosineSimilarity(otherEmbedding);
    }

    public string GetSourceIdentifier()
    {
        return $"{TableName}:{RowId}:{ColumnName}";
    }

    public override string ToString()
    {
        return
            $"RagChunk [Id: {Id}, Source: {GetSourceIdentifier()}, Chunk: {ChunkIndex}, ContentLength: {Content.Length}]";
    }

    public string GetSearchableContent()
    {
        return $"{TableName} {RowId} {ColumnName} {Content} {Metadata ?? string.Empty}";
    }
}

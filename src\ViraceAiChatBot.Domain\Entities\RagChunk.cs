using Vector = Pgvector.Vector;

namespace ViraceAiChatBot.Domain.Entities;

public class RagChunk : Entity<Guid>
{
    public string TableName { get; init; } = null!;
    public string RowId { get; init; } = null!;
    public string ColumnName { get; init; } = null!;
    public int ChunkIndex { get; init; }
    public string Content { get; init; } = null!;
    public Vector Embedding { get; init; } = null!;
    public string? Metadata { get; init; }

    // Parameterless constructor for EF Core
    private RagChunk() { }

    public RagChunk(
        string tableName,
        string rowId,
        string columnName,
        int chunkIndex,
        string content,
        float[] embedding, // Keep float[] here for constructor compatibility, will convert internally
        string? metadata = null)
    {
        TableName = tableName ?? throw new ArgumentNullException(nameof(tableName));
        RowId = rowId ?? throw new ArgumentNullException(nameof(rowId));
        ColumnName = columnName ?? throw new ArgumentNullException(nameof(columnName));
        ChunkIndex = chunkIndex;
        Content = content ?? throw new ArgumentNullException(nameof(content));
        Embedding = new Vector(embedding ??
                               throw new ArgumentNullException(nameof(embedding))); // Convert float[] to Vector
        Metadata = metadata;

        ValidateInvariants();
    }

    private void ValidateInvariants()
    {
        if (string.IsNullOrWhiteSpace(Content))
        {
            throw new ArgumentException("Content cannot be empty", nameof(Content));
        }

        if (ChunkIndex < 0)
        {
            throw new ArgumentException("ChunkIndex must be non-negative", nameof(ChunkIndex));
        }

        if (Embedding.ToArray().Length == 0) // Adjusted to use ToArray() for Vector
        {
            throw new ArgumentException("Embedding cannot be empty", nameof(Embedding));
        }

        if (Embedding.ToArray().Length != 3072) // OpenAI text-embedding-3-large dimension // Adjusted to use ToArray()
        {
            throw new ArgumentException($"Embedding must have 3072 dimensions, got {Embedding.ToArray().Length}",
                nameof(Embedding));
        }

        if (Content.Length > 10000) // Reasonable limit for chunk size
        {
            throw new ArgumentException("Content is too long for a single chunk", nameof(Content));
        }
    }

    public double CalculateCosineSimilarity(float[] otherEmbedding)
    {
        if (otherEmbedding.Length != Embedding.ToArray().Length) // Adjusted to use ToArray()
        {
            throw new ArgumentException("Embeddings must have the same dimensions");
        }

        var dotProduct = 0.0;
        var magnitudeA = 0.0;
        var magnitudeB = 0.0;
        var embeddingArray = Embedding.ToArray(); // Get the float array from Vector

        for (var i = 0; i < embeddingArray.Length; i++) // Use embeddingArray
        {
            dotProduct += embeddingArray[i] * otherEmbedding[i]; // Use embeddingArray
            magnitudeA += embeddingArray[i] * embeddingArray[i]; // Use embeddingArray
            magnitudeB += otherEmbedding[i] * otherEmbedding[i];
        }

        var magnitude = Math.Sqrt(magnitudeA) * Math.Sqrt(magnitudeB);
        return magnitude == 0 ? 0 : dotProduct / magnitude;
    }

    public string GetSourceIdentifier()
    {
        return $"{TableName}:{RowId}:{ColumnName}";
    }

    public override string ToString()
    {
        return
            $"RagChunk [Id: {Id}, Source: {GetSourceIdentifier()}, Chunk: {ChunkIndex}, ContentLength: {Content.Length}]";
    }

    public string GetSearchableContent()
    {
        return $"{TableName} {RowId} {ColumnName} {Content} {Metadata ?? string.Empty}";
    }
}

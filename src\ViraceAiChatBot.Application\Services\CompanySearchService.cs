using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using ViraceAiChatBot.Application.Configuration;
using ViraceAiChatBot.Application.Dtos.CompanySearch;

namespace ViraceAiChatBot.Application.Services;

public class CompanySearchService(
    HttpClient httpClient,
    ILogger<CompanySearchService> logger,
    Interfaces.OpenAiService openAiService,
    IOptions<CompanySearchOptions> options)
    : Interfaces.CompanySearchService
{
    private readonly CompanySearchOptions? _options = options?.Value;


    public async Task<CompanySearchResponse<SearchCompanyResponseModel>> SimpleSearchAsync(
        SimpleSearchRequest request,
        string? authToken = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var url = $"{_options.BaseUrl}/api/micro-service-company/company-search/search-company";
            var queryParams = new List<string>();

            if (!string.IsNullOrEmpty(request.SearchText))
            {
                queryParams.Add($"searchText={Uri.EscapeDataString(request.SearchText)}");
            }

            queryParams.Add($"pageIndex={request.PageIndex}");
            queryParams.Add($"pageSize={request.PageSize}");

            if (queryParams.Any())
            {
                url += "?" + string.Join("&", queryParams);
            }

            var httpRequest = new HttpRequestMessage(HttpMethod.Get, url);

            if (!string.IsNullOrEmpty(authToken))
            {
                httpRequest.Headers.Authorization =
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", authToken);
            }

            var response = await httpClient.SendAsync(httpRequest, cancellationToken);
            var content = await response.Content.ReadAsStringAsync(cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                logger.LogError("Simple search failed. Status: {StatusCode}, Response: {Response}",
                    response.StatusCode, content);
            }


            var result = JsonConvert.DeserializeObject<CompanySearchResponse<SearchCompanyResponseModel>>(content);

            return result ?? new CompanySearchResponse<SearchCompanyResponseModel>
            {
                Success = false, Message = "Failed to parse response"
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in SimpleSearchAsync");
            return new CompanySearchResponse<SearchCompanyResponseModel>
            {
                Success = false, StatusCode = 500, Message = $"Internal error: {ex.Message}"
            };
        }
    }

    public async Task<CompanySearchResponse<SearchCompanyResponseModel>> AdvancedSearchAsync(
        AdvancedSearchRequest request,
        string? authToken = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var url = $"{_options.BaseUrl}/api/micro-service-company/company-search/advanced-search";

            var httpRequest = new HttpRequestMessage(HttpMethod.Post, url);

            if (!string.IsNullOrEmpty(authToken))
                httpRequest.Headers.Authorization =
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", authToken);

            var json = JsonConvert.SerializeObject(request);

            httpRequest.Content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await httpClient.SendAsync(httpRequest, cancellationToken);
            var content = await response.Content.ReadAsStringAsync(cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                logger.LogError("Advanced search failed. Status: {StatusCode}, Response: {Response}",
                    response.StatusCode, content);
            }

            var result = JsonConvert.DeserializeObject<CompanySearchResponse<SearchCompanyResponseModel>>(content);

            return result ?? new CompanySearchResponse<SearchCompanyResponseModel>
            {
                Success = false, Message = "Failed to parse response"
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in AdvancedSearchAsync");
            return new CompanySearchResponse<SearchCompanyResponseModel>
            {
                Success = false, StatusCode = 500, Message = $"Internal error: {ex.Message}"
            };
        }
    }

    public async Task<(bool IsAdvanced, object SearchRequest)> ParseSearchQueryAsync(
        string query,
        CancellationToken cancellationToken = default)
    {
        // Use AI to analyze the query and determine if it needs advanced search
        var analysisPrompt =
            $@"Analyze this company search query and determine if it needs advanced search capabilities:
Query: '{query}'

Advanced search is needed if the query mentions:
- Specific location (province, district)
- Industry/business sector (VSIC codes)
- Financial criteria (revenue, capital)
- Company type
- Registration date range
- Import/export data
- Legal representatives or owners

Return JSON in this format:
{{
  ""isAdvanced"": true/false,
  ""simpleSearch"": {{
    ""searchText"": ""extracted search text""
  }},
  ""advancedSearch"": {{
    ""areas"": [...],
    ""vsics"": [...],
    ""financials"": [...],
    ""registrationDates"": [...],
    // other filters as needed
  }}
}}";

        try
        {
            var response = await openAiService.ChatCompletionAsync(
                analysisPrompt,
                maxTokens: 1000,
                temperature: 0.3,
                cancellationToken: cancellationToken);

            var jsonStart = response.Content.IndexOf('{');
            var jsonEnd = response.Content.LastIndexOf('}') + 1;

            if (jsonStart >= 0 && jsonEnd > jsonStart)
            {
                var jsonContent = response.Content.Substring(jsonStart, jsonEnd - jsonStart);
                using var doc = JsonDocument.Parse(jsonContent);
                var root = doc.RootElement;

                var isAdvanced = root.GetProperty("isAdvanced").GetBoolean();

                if (isAdvanced)
                {
                    var advancedJson = root.GetProperty("advancedSearch").GetRawText();
                    var advancedRequest =JsonConvert.DeserializeObject<AdvancedSearchRequest>(advancedJson);
                    return (true, advancedRequest ?? new AdvancedSearchRequest());
                }
                else
                {
                    var simpleJson = root.GetProperty("simpleSearch").GetRawText();
                    var simpleRequest = JsonConvert.DeserializeObject<SimpleSearchRequest>(simpleJson);
                    return (false, simpleRequest ?? new SimpleSearchRequest { SearchText = query });
                }
            }
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "Failed to parse query with AI, falling back to simple search");
        }

        // Default to simple search
        return (false, new SimpleSearchRequest { SearchText = query });
    }
}

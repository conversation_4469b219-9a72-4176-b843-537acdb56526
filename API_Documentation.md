# ViraceAI ChatBot Company Search - API Documentation

## Overview
This document provides comprehensive documentation for all APIs available in the ViraceAI ChatBot Company Search system. The system provides endpoints for chat completion, company search, data chunking, and suggestion services.

## Base URL
```
https://api.yourserver.com/api
```

## Authentication
All endpoints require JWT Bearer token authentication unless specified otherwise.

```
Authorization: Bearer {token}
```

---

## 📊 Company Search APIs

### 1. Simple Company Search
**Endpoint:** `POST /api/v1/companies/search/simple`
**Description:** Search companies using simple text query with basic filters
**Authentication:** Required

#### Request Body
```json
{
  "searchText": "string",     // Text to search for
  "pageIndex": 1,             // Page number (default: 1)
  "pageSize": 30              // Items per page (default: 30)
}
```

#### Response
```json
{
  "companies": [
    {
      "id": "string",
      "name": "string",
      "address": "string",
      "phoneNumber": "string",
      "email": "string",
      "website": "string",
      "registrationDate": "2023-01-01T00:00:00Z",
      "businessLicense": "string",
      "companyType": "string",
      "status": "string"
    }
  ],
  "pageIndex": 1,
  "pageSize": 30,
  "totalItem": 100,
  "pageCount": 4
}
```

#### Status Codes
- `200 OK` - Success
- `400 Bad Request` - Invalid request parameters
- `401 Unauthorized` - Authentication required
- `500 Internal Server Error` - Server error

---

### 2. Advanced Company Search
**Endpoint:** `POST /api/v1/companies/search/advanced`
**Description:** Search companies using advanced filters and criteria
**Authentication:** Required

#### Request Body
```json
{
  "pageIndex": 1,
  "pageSize": 30,
  "areas": [
    {
      "year": "2023",
      "provinceId": "01",
      "districtCode": "001",
      "communeId": "00001"
    }
  ],
  "vsics": [
    {
      "year": "2023",
      "vsicCode": "0111"
    }
  ],
  "financials": [
    {
      "year": "2023",
      "financialItemId": 1,
      "fromValue": 1000000,
      "toValue": 10000000
    }
  ],
  "companyTypes": [
    {
      "year": "2023",
      "companyTypeId": 1
    }
  ],
  "legals": [
    {
      "name": "Nguyen Van A",
      "identityNumber": "*********"
    }
  ],
  "owners": [
    {
      "name": "Tran Thi B",
      "ownershipPercentage": "51%"
    }
  ],
  "importExportTurnover": {
    "importExportHsValue": [
      {
        "hsCode": "0101",
        "year": "2023"
      }
    ],
    "importExportYearValue": [
      {
        "year": "2023",
        "fromValue": 1000000,
        "toValue": 50000000
      }
    ]
  },
  "registrationDates": [
    {
      "fromDate": "2020-01-01T00:00:00Z",
      "toDate": "2023-12-31T23:59:59Z"
    }
  ],
  "roleName": "Director"
}
```

#### Response
Similar to Simple Search response with matching companies

#### Status Codes
- `200 OK` - Success
- `400 Bad Request` - Invalid request parameters
- `401 Unauthorized` - Authentication required
- `500 Internal Server Error` - Server error

---

## 💬 Chat APIs

### 3. Chat Completions
**Endpoint:** `POST /api/chat/completions`
**Description:** Create a chat completion with AI assistant
**Authentication:** Required

#### Request Body
```json
{
  "userId": "user123",
  "sessionId": "550e8400-e29b-41d4-a716-************",
  "messages": [
    {
      "role": "user",
      "content": "Tell me about VinGroup company"
    }
  ],
  "model": "gpt-4",
  "maxTokens": 4000,
  "temperature": 0.7,
  "topP": 1.0,
  "useRag": true,
  "stream": false
}
```

#### Response
```json
{
  "id": "chatcmpl-550e8400-e29b-41d4-a716-************",
  "object": "chat.completion",
  "created": 1640995200,
  "model": "gpt-4",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "VinGroup is one of Vietnam's largest private conglomerates..."
      },
      "finishReason": "stop"
    }
  ],
  "usage": {
    "promptTokens": 100,
    "completionTokens": 200,
    "totalTokens": 300
  },
  "systemFingerprint": "fp_20240101",
  "sessionId": "550e8400-e29b-41d4-a716-************",
  "contextChunksUsed": 5
}
```

#### Status Codes
- `200 OK` - Success
- `400 Bad Request` - Invalid request or use streaming endpoint for stream requests

---

### 4. Streaming Chat Completions
**Endpoint:** `POST /api/chat/completions/stream`
**Description:** Create a streaming chat completion
**Authentication:** Required
**Content-Type:** `text/event-stream`

#### Request Body
Same as Chat Completions but with `stream: true`

#### Response
Server-Sent Events stream:
```
event: connection
data: {"id":"chatcmpl-123","status":"started","model":"gpt-4"}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1640995200,"model":"gpt-4","choices":[{"index":0,"message":{"role":"assistant","content":"Vin"},"finishReason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1640995200,"model":"gpt-4","choices":[{"index":0,"message":{"role":"assistant","content":"Group"},"finishReason":null}]}

data: [DONE]
```

---

### 5. Chat History
**Endpoint:** `GET /api/chat/history`
**Description:** Get chat history for a user
**Authentication:** Required

#### Query Parameters
- `userId` (required): User identifier
- `includeDeleted` (required): Include deleted sessions
- `pageSize` (optional): Items per page (default: 20)
- `pageNumber` (optional): Page number (default: 1)

#### Response
```json
{
  "sessions": [
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "title": "VinGroup Analysis",
      "createdAt": "2023-01-01T00:00:00Z",
      "lastMessageAt": "2023-01-01T12:00:00Z",
      "messageCount": 10,
      "isActive": true
    }
  ],
  "totalSessions": 50,
  "currentPage": 1,
  "pageSize": 20,
  "totalPages": 3
}
```

---

### 6. Session Messages
**Endpoint:** `GET /api/chat/sessions/{sessionId}/messages`
**Description:** Get messages from a specific chat session
**Authentication:** Required

#### Path Parameters
- `sessionId` (required): Session UUID

#### Query Parameters
- `pageSize` (optional): Items per page (default: 50)
- `pageNumber` (optional): Page number (default: 1)

#### Response
```json
{
  "sessionId": "550e8400-e29b-41d4-a716-************",
  "sessionTitle": "VinGroup Analysis",
  "messages": [
    {
      "id": "msg-123",
      "role": "user",
      "content": "Tell me about VinGroup",
      "timestamp": "2023-01-01T10:00:00Z"
    },
    {
      "id": "msg-124",
      "role": "assistant",
      "content": "VinGroup is Vietnam's largest private conglomerate...",
      "timestamp": "2023-01-01T10:00:05Z"
    }
  ],
  "currentPage": 1,
  "pageSize": 50,
  "totalMessages": 20,
  "totalPages": 1
}
```

#### Status Codes
- `200 OK` - Success
- `404 Not Found` - Session not found
- `400 Bad Request` - Invalid parameters

---

### 7. Delete Session
**Endpoint:** `DELETE /api/chat/sessions/{sessionId}`
**Description:** Delete (archive) a specific chat session
**Authentication:** Required

#### Path Parameters
- `sessionId` (required): Session UUID

#### Query Parameters
- `userId` (required): User identifier for authorization

#### Response
```json
{
  "success": true,
  "message": "Session deleted successfully",
  "sessionId": "550e8400-e29b-41d4-a716-************"
}
```

#### Status Codes
- `200 OK` - Success
- `404 Not Found` - Session not found
- `403 Forbidden` - No permission to delete
- `400 Bad Request` - Invalid request

---

## 🧠 Suggestion APIs

### 8. Prompt Suggestions
**Endpoint:** `POST /api/chat/suggestions/prompts`
**Description:** Get prompt suggestions when user input needs clarification
**Authentication:** Required

#### Request Body
```json
{
  "userId": "user123",
  "sessionId": "550e8400-e29b-41d4-a716-************",
  "currentInput": "Tell me about companies",
  "domain": "company_search",
  "maxSuggestions": 8
}
```

#### Response
```json
{
  "suggestions": [
    {
      "text": "Search for technology companies in Ho Chi Minh City",
      "description": "Find tech companies by location",
      "example": "Show me all software companies in District 1, HCMC"
    },
    {
      "text": "Find companies by revenue range",
      "description": "Search based on financial criteria",
      "example": "Companies with revenue between 10-100 billion VND"
    }
  ],
  "totalSuggestions": 8
}
```

---

### 9. Next Word Suggestions
**Endpoint:** `POST /api/chat/suggestions/next-word`
**Description:** Get next word suggestions based on current text and cursor position
**Authentication:** Required

#### Request Body
```json
{
  "userId": "user123",
  "sessionId": "550e8400-e29b-41d4-a716-************",
  "currentText": "Find companies in Ho Chi",
  "cursorPosition": 21,
  "context": "company_search",
  "maxSuggestions": 5
}
```

#### Response
```json
{
  "suggestions": [
    {
      "word": "Minh",
      "confidence": 0.95,
      "context": "location"
    },
    {
      "word": "An",
      "confidence": 0.85,
      "context": "location"
    }
  ],
  "totalSuggestions": 5
}
```

---

### 10. Sentence Suggestions
**Endpoint:** `POST /api/chat/suggestions/sentences`
**Description:** Get sentence completion or new sentence suggestions
**Authentication:** Required

#### Request Body
```json
{
  "userId": "user123",
  "sessionId": "550e8400-e29b-41d4-a716-************",
  "currentText": "I want to find companies",
  "cursorPosition": 23,
  "context": "company_search",
  "includeNewSentences": true,
  "maxSuggestions": 5
}
```

#### Response
```json
{
  "completions": [
    {
      "text": " in the technology sector",
      "confidence": 0.9,
      "type": "completion"
    }
  ],
  "newSentences": [
    {
      "text": "Please specify the location or industry.",
      "confidence": 0.8,
      "type": "clarification"
    }
  ],
  "totalSuggestions": 5
}
```

---

### 11. Enhance Prompt
**Endpoint:** `POST /api/chat/suggestions/enhance-prompt`
**Description:** Enhance user prompt for better search results
**Authentication:** Required

#### Request Body
```json
{
  "userId": "user123",
  "sessionId": "550e8400-e29b-41d4-a716-************",
  "originalPrompt": "companies in Vietnam",
  "intendedContext": "financial_analysis",
  "includeExamples": true
}
```

#### Response
```json
{
  "originalPrompt": "companies in Vietnam",
  "enhancedPrompt": "Find Vietnamese companies with detailed financial information including revenue, profit, and growth metrics for market analysis",
  "improvements": [
    "Added financial criteria specificity",
    "Clarified analysis purpose",
    "Enhanced search scope"
  ],
  "examples": [
    "Show me top 100 Vietnamese companies by revenue in 2023",
    "Find manufacturing companies in Northern Vietnam with revenue > 1 billion VND"
  ]
}
```

---

## 🔧 Data Chunking APIs

### 12. Get Available Tables
**Endpoint:** `GET /api/chunking/tables`
**Description:** Get list of tables available for chunking
**Authentication:** Required

#### Response
```json
{
  "tables": [
    "companies",
    "financial_statements",
    "company_types",
    "location_index",
    "vsic_index"
  ]
}
```

---

### 13. Chunking Health Check
**Endpoint:** `GET /api/chunking/health`
**Description:** Check the health of the chunking service
**Authentication:** Required

#### Response
```json
{
  "status": "Healthy",
  "availableTables": 15,
  "timestamp": "2023-01-01T10:00:00Z"
}
```

#### Status Codes
- `200 OK` - Service healthy
- `503 Service Unavailable` - Service unhealthy

---

### 14. Get Chunking Status
**Endpoint:** `GET /api/chunking/status/{tableName}`
**Description:** Get chunking status for a specific table
**Authentication:** Required

#### Path Parameters
- `tableName` (required): Name of the table

#### Response
```json
{
  "tableName": "companies",
  "status": "completed",
  "totalRecords": 100000,
  "chunkedRecords": 100000,
  "lastChunkedAt": "2023-01-01T08:00:00Z",
  "estimatedTimeRemaining": null,
  "errorCount": 0
}
```

#### Status Codes
- `200 OK` - Success
- `400 Bad Request` - Invalid table name

---

### 15. Chunk Specific Table
**Endpoint:** `POST /api/chunking/chunk/{tableName}`
**Description:** Chunk data from a specific table into RAG chunks
**Authentication:** Required

#### Path Parameters
- `tableName` (required): Name of the table to chunk

#### Response
```json
{
  "success": true,
  "message": "Table chunking completed successfully",
  "tableName": "companies",
  "recordsProcessed": 50000,
  "chunksCreated": 12500,
  "duration": "00:05:30",
  "errors": []
}
```

#### Status Codes
- `200 OK` - Success
- `400 Bad Request` - Invalid table name or chunking failed

---

### 16. Chunk All Tables
**Endpoint:** `POST /api/chunking/chunk-all`
**Description:** Chunk data from all supported tables into RAG chunks
**Authentication:** Required

#### Response
```json
{
  "success": true,
  "message": "All tables chunking completed",
  "results": [
    {
      "tableName": "companies",
      "success": true,
      "recordsProcessed": 50000,
      "chunksCreated": 12500
    },
    {
      "tableName": "financial_statements",
      "success": true,
      "recordsProcessed": 25000,
      "chunksCreated": 8300
    }
  ],
  "totalTablesProcessed": 15,
  "totalRecordsProcessed": 500000,
  "totalChunksCreated": 125000,
  "duration": "00:45:20",
  "errors": []
}
```

---

### 17. Refresh Table Chunks
**Endpoint:** `POST /api/chunking/refresh/{tableName}`
**Description:** Refresh chunks for a specific table (delete existing and rechunk)
**Authentication:** Required

#### Path Parameters
- `tableName` (required): Name of the table to refresh

#### Response
```json
{
  "success": true,
  "message": "Table chunks refreshed successfully",
  "tableName": "companies",
  "oldChunksDeleted": 12000,
  "newChunksCreated": 12500,
  "duration": "00:07:45"
}
```

#### Status Codes
- `200 OK` - Success
- `400 Bad Request` - Invalid table name or refresh failed

---

## 🎯 Common Response Formats

### Success Response
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": { /* response data */ }
}
```

### Error Response
```json
{
  "success": false,
  "error": "Error message describing what went wrong",
  "details": "Additional error details if available"
}
```

### Pagination Response
```json
{
  "data": [ /* array of items */ ],
  "pagination": {
    "currentPage": 1,
    "pageSize": 30,
    "totalItems": 1500,
    "totalPages": 50,
    "hasNext": true,
    "hasPrevious": false,
    "nextEndpoint": "/api/v1/companies/search/simple?page=2&size=30",
    "previousEndpoint": null
  }
}
```

---

## 📋 Status Codes Summary

| Code | Description |
|------|-------------|
| 200 | OK - Request successful |
| 400 | Bad Request - Invalid request parameters |
| 401 | Unauthorized - Authentication required |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource not found |
| 500 | Internal Server Error - Server error |
| 503 | Service Unavailable - Service temporarily unavailable |

---

## 🔐 Authentication Details

All API endpoints require a valid JWT bearer token in the Authorization header:

```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

The token should be obtained from your company's IdentityServer 4 instance and must include appropriate scopes for the requested operations.

---

## 📊 Rate Limiting

- **Chat APIs**: 100 requests per minute per user
- **Search APIs**: 200 requests per minute per user
- **Chunking APIs**: 10 requests per minute per user (admin only)
- **Suggestion APIs**: 50 requests per minute per user

---

## 📝 Notes

1. All datetime values are in ISO 8601 format (UTC)
2. Monetary values are in Vietnamese Dong (VND) unless specified otherwise
3. Text content supports Vietnamese and English languages
4. Streaming endpoints use Server-Sent Events (SSE) protocol
5. All numeric IDs are strings for consistency
6. Company search supports fuzzy matching and Vietnamese diacritics
7. RAG (Retrieval-Augmented Generation) is enabled by default for chat completions

---

*Last updated: December 2024*

using Serilog;
using Serilog.Events;
using Serilog.Formatting.Json;

namespace ViraceAiChatBot.Presentation.Extensions;

public static class SerilogExtensions
{
    public static void ConfigureSerilog(this IHostBuilder host)
    {
        host.UseSerilog((context, services, configuration) =>
        {
            var environment = context.HostingEnvironment;

            configuration
                .ReadFrom.Configuration(context.Configuration)
                .ReadFrom.Services(services)
                .Enrich.FromLogContext()
                .Enrich.WithProperty("Application", "ViraceAI-ChatBot")
                .Enrich.WithProperty("Environment", environment.EnvironmentName);

            configuration.WriteTo.Console(
                outputTemplate:
                "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] [{SourceContext}] {Message:lj}{NewLine}{Exception}"
            );

            var logPath = environment.IsDevelopment()
                ? "logs/virace-ai-chatbot-dev-.log"
                : "logs/virace-ai-chatbot-.log";

            configuration.WriteTo.File(
                path: logPath,
                rollingInterval: RollingInterval.Day,
                rollOnFileSizeLimit: true,
                fileSizeLimitBytes: environment.IsDevelopment()
                    ? 5 * 1024 * 1024
                    : 10 * 1024 * 1024, // 5MB dev, 10MB prod
                retainedFileCountLimit: environment.IsDevelopment() ? 3 : 7,
                outputTemplate:
                "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] [{SourceContext}] {Message:lj} {Properties:j}{NewLine}{Exception}"
            );

            // Entity Framework specific logging
            if (environment.IsDevelopment())
            {
                configuration.WriteTo.Logger(efLogger => efLogger
                    .Filter.ByIncludingOnly(evt => evt.Properties.ContainsKey("SourceContext") &&
                                                  evt.Properties["SourceContext"].ToString().Contains("Microsoft.EntityFrameworkCore"))
                    .WriteTo.File(
                        path: "logs/entityframework-commands-.log",
                        rollingInterval: RollingInterval.Day,
                        rollOnFileSizeLimit: true,
                        fileSizeLimitBytes: 10 * 1024 * 1024, // 10MB
                        retainedFileCountLimit: 3,
                        outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff} [{Level:u3}] [EF] {Message:lj}{NewLine}{Exception}"
                    ));
            }

            // Structured JSON logging for production
            if (environment.IsProduction())
            {
                configuration.WriteTo.File(
                    new JsonFormatter(),
                    path: "logs/virace-ai-chatbot-structured-.json",
                    rollingInterval: RollingInterval.Day,
                    rollOnFileSizeLimit: true,
                    fileSizeLimitBytes: 20 * 1024 * 1024, // 20MB
                    retainedFileCountLimit: 14
                );
            }

        });
    }

    public static void ConfigureRequestLogging(this WebApplication app)
    {
        app.UseSerilogRequestLogging(options =>
        {
            options.MessageTemplate =
                "HTTP {RequestMethod} {RequestPath} responded {StatusCode} in {Elapsed:0.0000} ms";

            options.GetLevel = (httpContext, elapsed, ex) => ex != null
                ? LogEventLevel.Error
                : httpContext.Response.StatusCode > 499
                    ? LogEventLevel.Error
                    : httpContext.Response.StatusCode > 399
                        ? LogEventLevel.Information : LogEventLevel.Debug;

            options.EnrichDiagnosticContext = (diagnosticContext, httpContext) =>
            {
                diagnosticContext.Set("RequestHost", httpContext.Request.Host.Value ?? "Unknown");
                diagnosticContext.Set("RequestScheme", httpContext.Request.Scheme);
                diagnosticContext.Set("UserAgent", httpContext.Request.Headers.UserAgent.FirstOrDefault() ?? "Unknown");
                diagnosticContext.Set("RemoteIP", httpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown");
                diagnosticContext.Set("RequestSize", httpContext.Request.ContentLength ?? 0);
                diagnosticContext.Set("ResponseSize", httpContext.Response.ContentLength ?? 0);

                // Add custom correlation ID if available
                if (httpContext.Request.Headers.ContainsKey("X-Correlation-ID"))
                {
                    diagnosticContext.Set("CorrelationId",
                        httpContext.Request.Headers["X-Correlation-ID"].FirstOrDefault() ?? "Unknown");
                }
            };
        });
    }

    public static void EnsureLogDirectoryExists()
    {
        var logDirectory = Path.Combine(Directory.GetCurrentDirectory(), "logs");
        if (!Directory.Exists(logDirectory))
        {
            Directory.CreateDirectory(logDirectory);
            Log.Information("Created logs directory at {LogDirectory}", logDirectory);
        }
    }

    /// <summary>
    /// Enables detailed Entity Framework command logging at runtime
    /// Use this for debugging specific database operations
    /// </summary>
    public static void EnableDetailedEfLogging()
    {
        Log.Logger = new LoggerConfiguration()
            .ReadFrom.Configuration(GetCurrentConfiguration())
            .MinimumLevel.Override("Microsoft.EntityFrameworkCore.Database.Command", LogEventLevel.Debug)
            .MinimumLevel.Override("Microsoft.EntityFrameworkCore.Query", LogEventLevel.Debug)
            .MinimumLevel.Override("Microsoft.EntityFrameworkCore.Update", LogEventLevel.Debug)
            .CreateLogger();

        Log.Information("Enabled detailed Entity Framework logging");
    }

    /// <summary>
    /// Disables Entity Framework command logging at runtime
    /// Use this to reduce log noise in production scenarios
    /// </summary>
    public static void DisableEfLogging()
    {
        Log.Logger = new LoggerConfiguration()
            .ReadFrom.Configuration(GetCurrentConfiguration())
            .MinimumLevel.Override("Microsoft.EntityFrameworkCore", LogEventLevel.Fatal)
            .MinimumLevel.Override("Microsoft.EntityFrameworkCore.Database.Command", LogEventLevel.Fatal)
            .MinimumLevel.Override("Microsoft.EntityFrameworkCore.Query", LogEventLevel.Fatal)
            .CreateLogger();

        Log.Information("Disabled Entity Framework logging");
    }

    private static IConfiguration GetCurrentConfiguration()
    {
        return new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
            .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production"}.json", optional: true)
            .AddEnvironmentVariables()
            .Build();
    }
}

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ViraceAiChatBot.Domain.Entities.ReferenceData;

namespace ViraceAiChatBot.Infrastructure.Persistence.Configurations;

public class RelatedCompanyTypeIndexConfiguration : IEntityTypeConfiguration<RelatedCompanyTypeIndex>
{
    public void Configure(EntityTypeBuilder<RelatedCompanyTypeIndex> builder)
    {
        builder.ToTable("RelatedCompanyTypeIndex");

        builder.<PERSON><PERSON><PERSON>(x => x.Id);

        builder.Property(x => x.Id)
            .HasColumnName("Id")
            .ValueGeneratedOnAdd();

        builder.Property(x => x.Name)
            .HasColumnName("Name")
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(x => x.EnName)
            .HasColumnName("EnName")
            .IsRequired()
            .HasMaxLength(50);


        // Indexes for performance
        builder.HasIndex(x => x.Name)
            .HasDatabaseName("ix_related_company_type_index_name");
    }
}

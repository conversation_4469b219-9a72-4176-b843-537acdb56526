namespace ViraceAiChatBot.Application.Dtos.CompanySearch;

/// <summary>
/// Represents a request for performing an advanced company search.
/// </summary>
public class AdvancedSearchRequest
{
    /// <summary>
    /// Gets or sets the index of the page to retrieve.
    /// </summary>
    public int PageIndex { get; set; } = 1;

    /// <summary>
    /// Gets or sets the size of the page to retrieve.
    /// </summary>
    public int PageSize { get; set; } = 0;

    /// <summary>
    /// Gets or sets the list of search areas to filter by.
    /// </summary>
    public List<SearchArea>? Areas { get; set; }

    /// <summary>
    /// Gets or sets the list of VSIC codes to filter by.
    /// </summary>
    public List<SearchVsic>? Vsics { get; set; }

    /// <summary>
    /// Gets or sets the list of financial criteria to filter by.
    /// </summary>
    public List<SearchFinancial>? Financials { get; set; }

    /// <summary>
    /// Gets or sets the list of company types to filter by.
    /// </summary>
    public List<SearchCompanyType>? CompanyTypes { get; set; }

    /// <summary>
    /// Gets or sets the list of legal representatives to filter by.
    /// </summary>
    public List<LegalRepresentativeRequest>? Legals { get; set; }

    /// <summary>
    /// Gets or sets the list of company owners to filter by.
    /// </summary>
    public List<CompanyOwner>? Owners { get; set; }

    /// <summary>
    /// Gets or sets the import/export turnover criteria.
    /// </summary>
    public ImportExportTurnover? ImportExportTurnover { get; set; }

    /// <summary>
    /// Gets or sets the list of registration dates to filter by.
    /// </summary>
    public List<RegistrationDate>? RegistrationDates { get; set; }

    /// <summary>
    /// Gets or sets the role name to filter by.
    /// </summary>
    public string? RoleName { get; set; }
}

/// <summary>
/// Represents a search area with specific filtering criteria.
/// </summary>
public class SearchArea
{
    /// <summary>
    /// Gets or sets the year associated with the search area.
    /// </summary>
    public string? Year { get; set; }

    /// <summary>
    /// Gets or sets the province ID associated with the search area.
    /// </summary>
    public string? ProvinceId { get; set; }

    /// <summary>
    /// Gets or sets the district code associated with the search area.
    /// </summary>
    public string? DistrictCode { get; set; }

    /// <summary>
    /// Gets or sets the commune ID associated with the search area.
    /// </summary>
    public string? CommuneId { get; set; }
}

/// <summary>
/// Represents a VSIC code with specific filtering criteria.
/// </summary>
public class SearchVsic
{
    /// <summary>
    /// Gets or sets the year associated with the VSIC code.
    /// </summary>
    public string? Year { get; set; }

    /// <summary>
    /// Gets or sets the VSIC code.
    /// </summary>
    public string? VsicCode { get; set; }
}

/// <summary>
/// Represents financial filtering criteria.
/// </summary>
public class SearchFinancial
{
    /// <summary>
    /// Gets or sets the year associated with the financial criteria.
    /// </summary>
    public string? Year { get; set; }

    /// <summary>
    /// Gets or sets the financial item ID.
    /// </summary>
    public int? FinancialItemId { get; set; }

    /// <summary>
    /// Gets or sets the minimum value for the financial criteria.
    /// </summary>
    public decimal? FromValue { get; set; }

    /// <summary>
    /// Gets or sets the maximum value for the financial criteria.
    /// </summary>
    public decimal? ToValue { get; set; }
}

/// <summary>
/// Represents a company type with specific filtering criteria.
/// </summary>
public class SearchCompanyType
{
    /// <summary>
    /// Gets or sets the year associated with the company type.
    /// </summary>
    public string? Year { get; set; }

    /// <summary>
    /// Gets or sets the company type ID.
    /// </summary>
    public int? CompanyTypeId { get; set; }
}

/// <summary>
/// Represents a legal representative with specific filtering criteria.
/// </summary>
public class LegalRepresentativeRequest
{
    /// <summary>
    /// Gets or sets the name of the legal representative.
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// Gets or sets the identity number of the legal representative.
    /// </summary>
    public string? IdentityNumber { get; set; }
}

/// <summary>
/// Represents a company owner with specific filtering criteria.
/// </summary>
public class CompanyOwner
{
    /// <summary>
    /// Gets or sets the name of the company owner.
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// Gets or sets the ownership percentage of the company owner.
    /// </summary>
    public string? OwnershipPercentage { get; set; }
}

/// <summary>
/// Represents import/export turnover filtering criteria.
/// </summary>
public class ImportExportTurnover
{
    /// <summary>
    /// Gets or sets the list of HS codes and their values.
    /// </summary>
    public List<ImportExportHsCode>? ImportExportHsValue { get; set; }

    /// <summary>
    /// Gets or sets the list of year-based turnover values.
    /// </summary>
    public List<ImportExportYearValue>? ImportExportYearValue { get; set; }
}

/// <summary>
/// Represents an HS code with specific filtering criteria.
/// </summary>
public class ImportExportHsCode
{
    /// <summary>
    /// Gets or sets the HS code.
    /// </summary>
    public string? HsCode { get; set; }

    /// <summary>
    /// Gets or sets the year associated with the HS code.
    /// </summary>
    public string? Year { get; set; }
}

/// <summary>
/// Represents year-based turnover values.
/// </summary>
public class ImportExportYearValue
{
    /// <summary>
    /// Gets or sets the year associated with the turnover values.
    /// </summary>
    public string? Year { get; set; }

    /// <summary>
    /// Gets or sets the minimum turnover value.
    /// </summary>
    public decimal? FromValue { get; set; }

    /// <summary>
    /// Gets or sets the maximum turnover value.
    /// </summary>
    public decimal? ToValue { get; set; }
}

/// <summary>
/// Represents registration date filtering criteria.
/// </summary>
public class RegistrationDate
{
    /// <summary>
    /// Gets or sets the start date for the registration date range.
    /// </summary>
    public DateTime? FromDate { get; set; }

    /// <summary>
    /// Gets or sets the end date for the registration date range.
    /// </summary>
    public DateTime ToDate { get; set; }
}

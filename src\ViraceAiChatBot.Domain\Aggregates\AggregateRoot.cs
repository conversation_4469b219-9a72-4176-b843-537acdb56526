using ViraceAiChatBot.Domain.Entities;

namespace ViraceAiChatBot.Domain.Aggregates;

public abstract class AggregateRoot : Entity<Guid>
{
    private readonly List<DomainEvent> domainEvents = [];

    public IReadOnlyCollection<DomainEvent> DomainEvents => domainEvents.AsReadOnly();

    protected void AddDomainEvent(DomainEvent domainEvent)
    {
        domainEvents.Add(domainEvent);
    }

    public void ClearDomainEvents()
    {
        domainEvents.Clear();
    }

    protected AggregateRoot() { }

    protected AggregateRoot(Guid id) : base(id) { }
}

public interface DomainEvent
{
    DateTime OccurredOn { get; }
    Guid EventId { get; }
}

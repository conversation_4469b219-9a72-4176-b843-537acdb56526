using ViraceAiChatBot.Domain.Entities;
using ViraceAiChatBot.Domain.Events;

namespace ViraceAiChatBot.Domain.Aggregates;

/// <summary>
/// Base class for aggregate roots in the domain.
/// Provides domain event management functionality.
/// </summary>
public abstract class AggregateRoot : Entity<Guid>
{
    private readonly List<IDomainEvent> domainEvents = [];

    public IReadOnlyCollection<IDomainEvent> DomainEvents => domainEvents.AsReadOnly();

    protected void AddDomainEvent(IDomainEvent domainEvent)
    {
        ArgumentNullException.ThrowIfNull(domainEvent);
        domainEvents.Add(domainEvent);
    }

    public void ClearDomainEvents()
    {
        domainEvents.Clear();
    }

    protected AggregateRoot() { }

    protected AggregateRoot(Guid id) : base(id) { }
}

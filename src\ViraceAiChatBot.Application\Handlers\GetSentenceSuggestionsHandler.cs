using MediatR;
using Microsoft.Extensions.Logging;
using ViraceAiChatBot.Application.Dtos;
using ViraceAiChatBot.Application.Interfaces;
using ViraceAiChatBot.Application.Queries;

namespace ViraceAiChatBot.Application.Handlers;

public class GetSentenceSuggestionsHandler(
    SuggestionService suggestionService,
    ILogger<GetSentenceSuggestionsHandler> logger) : IRequestHandler<GetSentenceSuggestionsQuery, SentenceSuggestionResponse>
{
    public async Task<SentenceSuggestionResponse> Handle(GetSentenceSuggestionsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Processing sentence suggestions for user {UserId}, text: {Text}",
                request.UserId, request.CurrentText);

            var response = await suggestionService.GetSentenceSuggestionsAsync(
                request.CurrentText,
                request.CursorPosition,
                request.Context,
                request.IncludeNewSentences,
                request.MaxSuggestions,
                cancellationToken);

            logger.LogInformation("Generated {Count} sentence suggestions for user {UserId}",
                response.Suggestions.Count, request.UserId);

            return response;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing sentence suggestions for user {UserId}", request.UserId);

            return new SentenceSuggestionResponse(
                [],
                request.CursorPosition,
                "Error occurred while generating suggestions",
                TimeSpan.Zero
            );
        }
    }
}

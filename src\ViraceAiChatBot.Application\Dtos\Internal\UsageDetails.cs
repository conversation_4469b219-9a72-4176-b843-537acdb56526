namespace ViraceAiChatBot.Application.DTOs.Internal;

/// <summary>
/// Represents the usage details of a specific operation.
/// </summary>
/// <param name="PromptTokens">The number of tokens used in the prompt.</param>
/// <param name="CompletionTokens">The number of tokens used in the completion.</param>
/// <param name="TotalTokens">The total number of tokens used in the operation.</param>
public record UsageDetails(
    int PromptTokens,
    int CompletionTokens,
    int TotalTokens
);

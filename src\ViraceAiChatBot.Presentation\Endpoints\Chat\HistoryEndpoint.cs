using Microsoft.AspNetCore.Mvc;
using MediatR;
using ViraceAiChatBot.Application.Queries;

namespace ViraceAiChatBot.Presentation.Endpoints.Chat;

/// <summary>
/// Endpoint xử lý lịch sử chat
/// </summary>
public static class HistoryEndpoint
{
    /// <summary>
    /// Map Chat History endpoint
    /// </summary>
    /// <param name="group">Route group</param>
    public static void MapHistoryEndpoint(this RouteGroupBuilder group)
    {
        group.MapGet("/history", async ([FromQuery] string userId, [FromQuery] bool includeDeleted,
                IMediator mediator,
                CancellationToken cancellationToken,
                [FromQuery] int pageSize = 20,
                [FromQuery] int pageNumber = 1) =>
            {
                try
                {
                    var query = new GetChatHistoryQuery(userId, includeDeleted, pageSize, pageNumber);
                    var result = await mediator.Send(query, cancellationToken);
                    return Results.Ok(result);
                }
                catch (Exception ex)
                {
                    return Results.BadRequest(new { error = ex.Message });
                }
            })
            .WithName("GetChatHistory")
            .WithSummary("Get chat history for a user")
            .Produces<ChatHistoryResponse>(200)
            .Produces<object>(400);
    }
}

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ViraceAiChatBot.Domain.Entities.ReferenceData;

namespace ViraceAiChatBot.Infrastructure.Persistence.Configurations;

public class HSCodeConfiguration : IEntityTypeConfiguration<HSCode>
{
    public void Configure(EntityTypeBuilder<HSCode> builder)
    {
        builder.ToTable("HSCode");

        builder.<PERSON><PERSON><PERSON>(x => x.Code2);

        builder.Property(x => x.Code2)
            .HasColumnName("code2")
            .IsRequired()
            .HasMaxLength(2);

        builder.Property(x => x.Code4)
            .HasColumnName("code4")
            .IsRequired()
            .HasMaxLength(16);

        builder.Property(x => x.DescVi)
            .HasColumnName("descvi")
            .HasMaxLength(1000);

        builder.Property(x => x.DescEn)
            .HasColumnName("descen")
            .HasMaxLength(1000);

        builder.HasIndex(x => x.Code2)
            .HasDatabaseName("ix_hs_code_code2");

        builder.HasIndex(x => x.Code4)
            .HasDatabaseName("ix_hs_code_code4");
    }
}

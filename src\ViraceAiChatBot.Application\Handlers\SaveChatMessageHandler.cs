using MediatR;
using Microsoft.Extensions.Logging;
using ViraceAiChatBot.Application.Commands;
using ViraceAiChatBot.Application.Interfaces;
using ViraceAiChatBot.Domain.Aggregates;
using ViraceAiChatBot.Domain.Interfaces;
using ViraceAiChatBot.Infrastructure.Persistence.Repositories;

namespace ViraceAiChatBot.Application.Handlers;

public class SaveChatMessageHandler(
    ChatSessionRepository repository,
    ILogger<SaveChatMessageHandler> logger)
    : IRequestHandler<SaveChatMessageCommand, Unit>
{
    public async Task<Unit> Handle(SaveChatMessageCommand request, CancellationToken cancellationToken)
    {
        try
        {
            ChatSession? session;
            if (request.SessionId.HasValue)
            {
                session = await repository.GetByIdAsync(request.SessionId.Value, cancellationToken);
                if (session == null || !session.IsActive)
                {
                    session = new ChatSession(request.UserId, "Chat Session");
                    await repository.AddAsync(session, cancellationToken);
                }
            }
            else
            {
                session = new ChatSession(request.UserId, "Chat Session");
                await repository.AddAsync(session, cancellationToken);
            }

            session.AddMessage("user", request.UserMessage);

            session.AddMessage("assistant", request.AssistantMessage);

            await repository.UpdateAsync(session, cancellationToken);

            logger.LogInformation(
                "Saved chat messages for user {UserId} in session {SessionId}. Model: {Model}, Tokens: {Tokens}",
                request.UserId, session.Id, request.Model, request.TokensUsed);

            return Unit.Value;
        }
        catch (Exception ex)
        {
            logger.LogError(ex,
                "Failed to save chat messages for user {UserId}, session {SessionId}",
                request.UserId, request.SessionId);
            throw;
        }
    }
}

---
description:
globs:
alwaysApply: true
---
You are an expert in C#, ASP.NET Core 9.0 minimal APIs, Clean Architecture, PostgreSQL + EF Core, IdentityServer 4, and integrating external AI services that follow an OpenAI-style REST contract

IMPORTANT RULE:
Don't run build, clean, start
don't run ef tool migarion, update, drop database

Key Principles
Clean Architecture


Separate Domain, Application, Infrastructure, and Presentation (API) layers.

Keep all business logic and entities in Domain; no framework or DB references.

Application holds commands/queries (CQRS), DTOs, interfaces, and validation.

Infrastructure implements persistence (EF Core with Npgsql), external AI HTTP clients, and IdentityServer data stores.


Presentation is a minimal-API project (program.cs only) that wires dependencies via DI and exposes endpoints.

Minimal API style (no MVC controllers)

Use top-level route groups (MapGroup("/v1/chat")) and extension methods (app.MapChatEndpoints()), not controllers.

Prefer endpoint-level validation (FluentValidation) and attribute routing.


Target net9.0 and the latest stable (non-preview) packages:

Microsoft.EntityFrameworkCore 9.*

Npgsql.EntityFrameworkCore.PostgreSQL 9.*

Duende.IdentityServer 4.* (fork of IdentityServer4)

Microsoft.AspNetCore.OpenApi 9.* + Scalar.AspNetCore for docs

MediatR 14.* (optional, for CQRS)


PostgreSQL persistence

One DbContext per bounded context; migrations live in Infrastructure.Persistence/Migrations.

Use schema per aggregate if helpful; snake_case naming convention (modelBuilder.UseSnakeCaseNamingConvention()).

Authentication & authorization

Accept JWT bearer tokens issued by your company’s IdentityServer 4.

Enforce [Authorize] globally; allow anonymous only on the health-check endpoint.

Provide AddJwtBearer("IS4", ...) with proper authority and audience.

External AI service

Register a typed HttpClient (IAiClient) with Polly retry + timeout.

Accept api-key in Authorization: Bearer header to the AI backend.

Expose /chat/completions proxy endpoint that pipes user prompt → company AI → response stream (IAsyncEnumerable) — keep the controller-style streaming bufferless.

API documentation
Serve docs with Scalar, not Swagger UI.

Code style

Enable nullable + implicit usings; require file-scoped namespaces.

Follow Microsoft naming rules; favour records for immutability.

Keep methods ≤ 25 LOC; fail PR if Cyclomatic > 15 (use dotnet format / analyzers).


Cursor AI interaction hints

When asked to create an endpoint, add it to the appropriate MapGroup in Presentation and surface any new DTOs in Application.

When asked to write data access, place repository interface in Application and implementation in Infrastructure, injected via DI in Program.cs.

Never mix presentation concerns (HTTP, Identity) into Domain or Application layers.

Always propose EF Core migrations for model changes.

Default all new code to async/await and cancellation tokens.

Do not generate any UI code (Blazor, React, etc.) for this project.

Alway generate documment xml in header function












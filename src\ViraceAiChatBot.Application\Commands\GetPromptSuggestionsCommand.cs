using MediatR;
using ViraceAiChatBot.Application.Dtos;

namespace ViraceAiChatBot.Application.Commands;

/// <summary>
/// Represents a command to retrieve prompt suggestions based on the user's current input.
/// </summary>
/// <remarks>
/// This command is used to generate a list of prompt suggestions tailored to the user's input,
/// session details, and optional domain context.
/// </remarks>
/// <param name="UserId">The identifier of the user requesting the prompt suggestions.</param>
/// <param name="SessionId">The optional unique identifier of the chat session.</param>
/// <param name="CurrentInput">The current input provided by the user to generate suggestions.</param>
/// <param name="Domain">An optional domain context to refine the suggestions.</param>
/// <param name="MaxSuggestions">The maximum number of suggestions to generate.</param>
/// <returns>
/// A response of type <see cref="PromptSuggestionResponse"/> containing the generated prompt suggestions.
/// </returns>
public record GetPromptSuggestionsCommand(
    string UserId,
    Guid? SessionId,
    string CurrentInput,
    string? Domain = null,
    int MaxSuggestions = 8
) : IRequest<PromptSuggestionResponse>;

namespace ViraceAiChatBot.Application.DTOs.Internal;

/// <summary>
/// Represents a request for generating embeddings.
/// </summary>
/// <param name="Provider">The name of the provider used for generating embeddings.</param>
/// <param name="Model">The model used for generating embeddings.</param>
/// <param name="InputString">The optional input string to generate embeddings for.</param>
/// <param name="InputArrayString">The optional array of input strings to generate embeddings for.</param>
/// <param name="EncodingFormat">The optional encoding format for the embeddings (default: "float").</param>
/// <param name="Dimensions">The optional number of dimensions for the embeddings.</param>
/// <param name="User">The optional user identifier associated with the request.</param>
public record EmbeddingRequest(
    string Provider,
    string Model,
    string? InputString,
    string[]? InputArrayString,
    string? EncodingFormat = "float",
    int? Dimensions = null,
    string? User = null
);

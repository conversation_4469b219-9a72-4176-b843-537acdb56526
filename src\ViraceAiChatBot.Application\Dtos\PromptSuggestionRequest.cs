namespace ViraceAiChatBot.Application.Dtos;

/// <summary>
/// Represents a request for prompt suggestions.
/// </summary>
public record PromptSuggestionRequest(
    string UserId,
    Guid? SessionId,
    string CurrentInput,
    string? Domain,
    int MaxSuggestions = 8
);

/// <summary>
/// Represents a request for next word suggestions.
/// </summary>
public record NextWordSuggestionRequest(
    string UserId,
    Guid? SessionId,
    string CurrentText,
    int CursorPosition,
    string? Context,
    int MaxSuggestions = 5
);

/// <summary>
/// Represents a request for sentence suggestions.
/// </summary>
public record SentenceSuggestionRequest(
    string UserId,
    Guid? SessionId,
    string CurrentText,
    int CursorPosition,
    string? Context,
    bool IncludeNewSentences = true,
    int MaxSuggestions = 3
);

/// <summary>
/// Represents a request for an enhanced prompt.
/// </summary>
public record EnhancedPromptRequest(
    string UserId,
    Guid? SessionId,
    string OriginalPrompt,
    string? IntendedContext,
    bool IncludeExamples = true
);

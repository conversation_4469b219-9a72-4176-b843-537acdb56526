using Microsoft.AspNetCore.Mvc;
using MediatR;
using ViraceAiChatBot.Application.Queries;
using ViraceAiChatBot.Application.Dtos;

namespace ViraceAiChatBot.Presentation.Endpoints.Suggestions;

/// <summary>
/// Endpoint xử lý gợi ý từ tiếp theo
/// </summary>
public static class NextWordSuggestionsEndpoint
{
    /// <summary>
    /// Map Next Word Suggestions endpoint
    /// </summary>
    /// <param name="group">Route group</param>
    public static void MapNextWordSuggestionsEndpoint(this RouteGroupBuilder group)
    {
        group.MapPost("/suggestions/next-word", async (
                [FromBody] NextWordSuggestionRequest request,
                IMediator mediator,
                CancellationToken cancellationToken) =>
            {
                try
                {
                    var query = new GetNextWordSuggestionsQuery(
                        request.UserId,
                        request.SessionId,
                        request.CurrentText,
                        request.CursorPosition,
                        request.Context,
                        request.MaxSuggestions);

                    var result = await mediator.Send(query, cancellationToken);
                    return Results.Ok(result);
                }
                catch (Exception ex)
                {
                    return Results.BadRequest(new { error = ex.Message });
                }
            })
            .WithName("GetNextWordSuggestions")
            .WithSummary("Get next word suggestions based on current text and cursor position")
            .Produces<NextWordSuggestionResponse>(200)
            .Produces<object>(400);
    }
}

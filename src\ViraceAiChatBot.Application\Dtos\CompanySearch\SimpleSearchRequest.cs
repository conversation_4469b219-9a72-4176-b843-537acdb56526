namespace ViraceAiChatBot.Application.Dtos.CompanySearch;

/// <summary>
/// Represents a request for performing a simple company search.
/// </summary>
public class SimpleSearchRequest
{
    /// <summary>
    /// Gets or sets the text to search for.
    /// </summary>
    public string? SearchText { get; set; }

    /// <summary>
    /// Gets or sets the index of the page to retrieve.
    /// </summary>
    public int PageIndex { get; set; } = 1;

    /// <summary>
    /// Gets or sets the size of the page to retrieve.
    /// </summary>
    public int PageSize { get; set; } = 30;
}

using ViraceAiChatBot.Application.Interfaces;
using ViraceAiChatBot.Application.Records;

namespace ViraceAiChatBot.Presentation.Endpoints.Chunking;

/// <summary>
/// Endpoint xử lý chunk tất cả tables
/// </summary>
public static class ChunkAllEndpoint
{
    /// <summary>
    /// Map Chunk All endpoint
    /// </summary>
    /// <param name="group">Route group</param>
    public static void MapChunkAllEndpoint(this RouteGroupBuilder group)
    {
        group.MapPost("/chunk-all", async (
            DataChunkingService chunkingService,
            CancellationToken cancellationToken) =>
        {
            var result = await chunkingService.ChunkAllTablesAsync(cancellationToken);

            if (result.Success)
            {
                return Results.Ok(result);
            }
            else
            {
                return Results.Ok(result); // Return 200 even if some tables failed, as it's a partial success
            }
        })
        .WithName("ChunkAllTables")
        .WithSummary("Chunk data from all supported tables into RAG chunks")
        .Produces<DataChunkingResult>(200);
    }
}

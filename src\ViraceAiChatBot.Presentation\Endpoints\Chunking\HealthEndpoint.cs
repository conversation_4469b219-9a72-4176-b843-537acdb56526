using ViraceAiChatBot.Application.Interfaces;

namespace ViraceAiChatBot.Presentation.Endpoints.Chunking;

/// <summary>
/// Endpoint xử lý health check cho chunking service
/// </summary>
public static class HealthEndpoint
{
    /// <summary>
    /// Map Health Check endpoint
    /// </summary>
    /// <param name="group">Route group</param>
    public static void MapHealthEndpoint(this RouteGroupBuilder group)
    {
        group.MapGet("/health", async (
            DataChunkingService chunkingService,
            CancellationToken cancellationToken) =>
        {
            try
            {
                var tables = await chunkingService.GetAvailableTablesAsync(cancellationToken);
                return Results.Ok(new
                {
                    Status = "Healthy",
                    AvailableTables = tables.Count,
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                return Results.Problem(
                    title: "Chunking Service Unhealthy",
                    detail: ex.Message,
                    statusCode: 503
                );
            }
        })
        .WithName("ChunkingHealthCheck")
        .WithSummary("Check the health of the chunking service")
        .Produces<object>(200)
        .Produces<object>(503);
    }
}

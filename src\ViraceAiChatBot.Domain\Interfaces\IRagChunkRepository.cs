using ViraceAiChatBot.Domain.Entities;

namespace ViraceAiChatBot.Domain.Interfaces;

public interface IRagChunkRepository
{
    Task<RagChunk?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    Task<List<RagChunk>> GetBySourceAsync(string tableName, string rowId, CancellationToken cancellationToken = default);

    Task<List<RagChunk>> SearchSimilarAsync(
        float[] queryEmbedding,
        int limit = 10,
        string[]? tableFilter = null,
        CancellationToken cancellationToken = default);

    Task<List<RagChunk>> SearchSimilarWithMetadataAsync(
        float[] queryEmbedding,
        int limit = 10,
        string[]? tableFilter = null,
        Dictionary<string, object>? metadataFilter = null,
        CancellationToken cancellationToken = default);

    Task<RagChunk> AddAsync(RagChunk ragChunk, CancellationToken cancellationToken = default);

    Task AddRangeAsync(IEnumerable<RagChunk> ragChunks, CancellationToken cancellationToken = default);

    Task UpdateAsync(RagChunk ragChunk, CancellationToken cancellationToken = default);

    Task DeleteAsync(RagChunk ragChunk, CancellationToken cancellationToken = default);

    Task DeleteBySourceAsync(string tableName, string rowId, CancellationToken cancellationToken = default);

    Task<bool> ExistsAsync(string tableName, string rowId, string columnName, int chunkIndex, CancellationToken cancellationToken = default);

    Task<int> GetCountAsync( CancellationToken cancellationToken = default);

    Task<List<string>> GetAvailableTablesAsync( CancellationToken cancellationToken = default);

    Task<DateTime?> GetLastProcessedTimeAsync(string tableName, CancellationToken cancellationToken = default);

    Task SetLastProcessedTimeAsync(string tableName, DateTime processedTime, CancellationToken cancellationToken = default);
}

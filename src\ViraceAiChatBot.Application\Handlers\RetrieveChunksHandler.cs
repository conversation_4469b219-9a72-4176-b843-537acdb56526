using MediatR;
using Microsoft.Extensions.Logging;
using System.Diagnostics;
using ViraceAiChatBot.Application.Interfaces;
using ViraceAiChatBot.Application.Queries;
using ViraceAiChatBot.Application.Records;
using ViraceAiChatBot.Domain.Interfaces;

namespace ViraceAiChatBot.Application.Handlers;

public class RetrieveChunksHandler(
    IRagChunkRepository ragChunkRepository,
    EmbeddingService embeddingService,
    ILogger<RetrieveChunksHandler> logger) : IRequestHandler<RetrieveChunksQuery, RetrieveChunksResponse>
{
    public async Task<RetrieveChunksResponse> Handle(RetrieveChunksQuery request, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            logger.LogInformation("Retrieving chunks for query: {Query}, K: {K}, Tenant: {TenantId}",
                request.Query, request.K, request.TenantId);

            var queryEmbedding = await embeddingService.GenerateEmbeddingAsync(request.Query, cancellationToken);

            // 3. Search for similar chunks
            var chunks = await ragChunkRepository.SearchSimilarWithMetadataAsync(
                queryEmbedding,
                limit: request.K,
                tableFilter: request.Tables,
                metadataFilter: request.MetadataFilter,
                cancellationToken);

            // 4. Filter by minimum similarity score
            var filteredChunks = chunks
                .Select(chunk => new RetrievedChunk
                {
                    Content = chunk.Content,
                    TableName = chunk.TableName,
                    RowId = chunk.RowId,
                    ColumnName = chunk.ColumnName,
                    SimilarityScore = chunk.CalculateCosineSimilarity(queryEmbedding),
                    Metadata = chunk.Metadata
                })
                .ToList();

            stopwatch.Stop();

            var response = new RetrieveChunksResponse
            {
                Chunks = filteredChunks,
                QueryEmbedding = queryEmbedding,
                TotalMatches = chunks.Count,
                ProcessingTime = stopwatch.Elapsed,
                CacheKey = ""//not úsed in this example
            };

            // 5. Cache the response
            // await cacheService.SetAsync(cacheKey, response, TimeSpan.FromMinutes(30), cancellationToken);

            logger.LogInformation("Retrieved {ChunkCount} chunks in {ElapsedMs}ms for query: {Query}",
                filteredChunks.Count, stopwatch.ElapsedMilliseconds, request.Query);

            return response;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error retrieving chunks for query: {Query}", request.Query);
            throw;
        }
    }

}

# ViraceAI ChatBot Company Search - API Documentation

## Overview
This API provides endpoints for AI-powered company search, chat completion, data chunking, and intelligent suggestions for business data analysis.

## Authentication
All endpoints require JWT Bearer token authentication:
```
Authorization: Bearer {your_jwt_token}
```

## API Endpoints

### Company Search APIs

#### 1. Simple Company Search
- **Method:** POST
- **URL:** `/api/v1/companies/search/simple`
- **Description:** Search companies using simple text query
- **Request Body:**
```json
{
  "searchText": "string",      // Required: Search keyword (company name, tax code, etc.)
  "pageIndex": 1,              // Required: Page number (default: 1)
  "pageSize": 20               // Required: Number of results per page (default: 20, max: 100)
}
```
- **Response:** Returns paginated list of matching companies
```json
{
  "companies": [
    {
      "id": "string",
      "name": "string",
      "taxCode": "string",
      "address": "string",
      "phoneNumber": "string",
      "email": "string",
      "website": "string",
      "registrationDate": "2023-01-01T00:00:00Z",
      "businessLicense": "string",
      "companyType": "string",
      "status": "active",
      "legalRepresentative": "string",
      "registeredCapital": **********,
      "industry": "string",
      "vsicCode": "string"
    }
  ],
  "pageIndex": 1,
  "pageSize": 20,
  "totalItem": 150,
  "pageCount": 8
}
```
- **Status Codes:** 200 (Success), 400 (Bad Request), 401 (Unauthorized), 500 (Server Error)

#### 2. Advanced Company Search
- **Method:** POST
- **URL:** `/api/v1/companies/search/advanced`
- **Description:** Search companies using advanced filters with AND/OR logic
- **Request Body:**
```json
{
  "pageIndex": 1,              // Required: Page number for pagination
  "pageSize": 10,              // Required: Number of results per page
  "areas": [                   // Required (can be null): Area filters (OR logic within array)
    {
      "year": "2023",          // Required: Year for the area filter
      "provinceId": "01",      // Required: Province ID
      "districtCode": "001",   // Optional: District code
      "communeId": "00001"     // Optional: Commune ID
    }
  ],
  "vsics": [                   // Required (can be null): VSIC code filters (OR logic within array)
    {
      "year": "2023",          // Required: Year for the VSIC filter
      "vsicCode": "0111"       // Required: VSIC code
    }
  ],
  "financials": [              // Required (can be null): Financial filters (OR logic within array)
    {
      "year": "2023",          // Required: Year for the financial filter
      "financialItemId": 1,    // Required: Financial item ID
      "fromValue": 1000000,    // Optional: Minimum value for the financial item
      "toValue": 10000000      // Optional: Maximum value for the financial item
    }
  ],
  "companyTypes": [            // Required (can be null): Company type filters (OR logic within array)
    {
      "year": "2023",          // Required: Year for the company type filter
      "companyTypeId": 1       // Required: Company type ID
    }
  ],
  "legals": [                  // Required (can be null): Legal representative filters (OR logic within array)
    {
      "userId": "Nguyen Van A" // Required: Legal representative user ID or name
    }
  ],
  "owners": [                  // Required (can be null): Owner filters (OR logic within array)
    {
      "id": "Tran Thi B",      // Required: Owner ID or name
      "ownerShipType": 1       // Optional: Ownership type
    }
  ],
  "importExportTurnover": {    // Required (can be null): Import/export turnover filters
    "importExportYearValue": [ // Optional: Import/export year value filters
      {
        "type": "import",      // Required: Type: 'import' or 'export'
        "year": "2023",        // Required: Year for the import/export value
        "from": 1000000,       // Required: Minimum value for the import/export
        "to": 50000000         // Required: Maximum value for the import/export
      }
    ],
    "importExportHsValue": [   // Optional: Import/export HS code value filters
      {
        "type": "export",      // Required: Type: 'import' or 'export'
        "value": "0101"        // Required: HS code value
      }
    ]
  },
  "registrationDates": [       // Required (can be null): Registration date range filters (OR logic, but typically one range)
    {
      "fromDate": "2020-01-01T00:00:00Z",  // Required: Start date for the registration date range
      "toDate": "2023-12-31T23:59:59Z"     // Required: End date for the registration date range
    }
  ]
}
```
- **Response:** Returns paginated list of matching companies (same structure as Simple Search)
- **Status Codes:** 200 (Success), 400 (Bad Request), 401 (Unauthorized), 500 (Server Error)

### Chat APIs

#### 3. Chat Completions
- **Method:** POST
- **URL:** `/api/chat/completions`
- **Description:** Create AI chat completion for company analysis with multiple response types
- **Request Body:**
```json
{
  "userId": "string",
  "sessionId": "uuid",
  "messages": [{"role": "user", "content": "string"}],
  "model": "gpt-4",
  "maxTokens": 4000,
  "temperature": 0.7,
  "useRag": true,
  "stream": false
}
```

#### Response Types and Examples:

##### A. Standard Text Response
```json
{
  "id": "chatcmpl-550e8400-e29b-41d4-a716-************",
  "object": "chat.completion",
  "created": **********,
  "model": "gpt-4",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "Based on your search criteria, I found several technology companies in Ho Chi Minh City. Here are the key findings:\n\n**Top Technology Companies:**\n1. **FPT Corporation** - Leading IT services provider\n2. **VNG Corporation** - Digital technology and online entertainment\n3. **Tiki Corporation** - E-commerce platform\n\nWould you like more detailed information about any specific company?"
      },
      "finishReason": "stop"
    }
  ],
  "usage": {
    "promptTokens": 150,
    "completionTokens": 95,
    "totalTokens": 245
  },
  "systemFingerprint": "fp_20240101",
  "sessionId": "550e8400-e29b-41d4-a716-************",
  "contextChunksUsed": 12
}
```

##### B. JSON Data Response (Function Call Results)
When the AI makes a function call to search companies, the response returns structured JSON data that includes both the original request parameters and the search results:

**Simple Search Function Call Result:**
```json
{
  "request": {
    "functionName": "search_company_simple",
    "parameters": {
      "searchText": "FPT",
      "pageIndex": 1,
      "pageSize": 20
    }
  },
  "response": {
    "companies": [
      {
        "id": "**********",
        "name": "CÔNG TY CỔ PHẦN CÔNG NGHỆ FPT",
        "taxCode": "**********",
        "address": "Số 17 Duy Tân, Phường Dịch Vọng Hậu, Quận Cầu Giấy, Thành phố Hà Nội",
        "phoneNumber": "024 7300 8866",
        "email": "<EMAIL>",
        "website": "https://fpt.com.vn",
        "registrationDate": "1999-01-15T00:00:00Z",
        "businessLicense": "**********",
        "companyType": "Công ty cổ phần",
        "status": "Đang hoạt động",
        "legalRepresentative": "Trương Gia Bình",
        "registeredCapital": 1**********00,
        "industry": "Công nghệ thông tin",
        "vsicCode": "6201"
      },
      {
        "id": "**********",
        "name": "CÔNG TY CỔ PHẦN VNG",
        "taxCode": "**********",
        "address": "Số 182 Lê Đại Hành, Phường 15, Quận 11, Thành phố Hồ Chí Minh",
        "phoneNumber": "028 7300 7888",
        "email": "<EMAIL>",
        "website": "https://vng.com.vn",
        "registrationDate": "2005-04-20T00:00:00Z",
        "businessLicense": "**********",
        "companyType": "Công ty cổ phần",
        "status": "Đang hoạt động",
        "legalRepresentative": "Lê Hồng Minh",
        "registeredCapital": 500000000000,
        "industry": "Công nghệ số",
        "vsicCode": "6311"
      }
    ],
    "pageIndex": 1,
    "pageSize": 20,
    "totalItem": 145,
    "pageCount": 8
  },
  "metadata": {
    "executedAt": "2024-01-15T10:30:45.123Z",
    "hasData": true,
    "totalResults": 2
  }
}
```

**Advanced Search Function Call Result:**
```json
{
  "request": {
    "functionName": "search_company_advanced",
    "parameters": {
      "pageIndex": 1,
      "pageSize": 10,
      "areas": [
        {
          "year": "2023",
          "provinceId": "01",
          "districtCode": "009",
          "communeId": "00304"
        }
      ],
      "vsics": [
        {
          "year": "2023",
          "vsicCode": "6201"
        }
      ],
      "financials": [
        {
          "year": "2023",
          "financialItemId": 1,
          "fromValue": **********000,
          "toValue": 50000000000000
        }
      ],
      "companyTypes": [
        {
          "year": "2023",
          "companyTypeId": 1
        }
      ],
      "legals": null,
      "owners": null,
      "importExportTurnover": null,
      "registrationDates": null
    }
  },
  "response": {
    "companies": [
      {
        "id": "**********",
        "name": "CÔNG TY CỔ PHẦN CÔNG NGHỆ FPT",
        "taxCode": "**********",
        "address": "Số 17 Duy Tân, Phường Dịch Vọng Hậu, Quận Cầu Giấy, Thành phố Hà Nội",
        "phoneNumber": "024 7300 8866",
        "email": "<EMAIL>",
        "website": "https://fpt.com.vn",
        "registrationDate": "1999-01-15T00:00:00Z",
        "businessLicense": "**********",
        "companyType": "Công ty cổ phần",
        "status": "Đang hoạt động",
        "legalRepresentative": "Trương Gia Bình",
        "registeredCapital": 1**********00,
        "industry": "Công nghệ thông tin",
        "vsicCode": "6201",
        "financialData": {
          "year": "2023",
          "revenue": 45000000000000,
          "profit": 8500000000000,
          "assets": 32000000000000,
          "equity": 18000000000000
        },
        "location": {
          "provinceId": "01",
          "provinceName": "Thành phố Hà Nội",
          "districtCode": "009",
          "districtName": "Quận Cầu Giấy",
          "communeId": "00304",
          "communeName": "Phường Dịch Vọng Hậu"
        },
        "importExportData": {
          "2023": {
            "totalImport": 2500000000000,
            "totalExport": 1800000000000,
            "mainHsCodes": ["8471", "8517", "9011"]
          }
        }
      }
    ],
    "pageIndex": 1,
    "pageSize": 10,
    "totalItem": 25,
    "pageCount": 3
  },
  "metadata": {
    "executedAt": "2024-01-15T10:30:45.123Z",
    "hasData": true,
    "totalResults": 1,
    "filterCriteria": {
      "hasAreaFilters": true,
      "hasVsicFilters": true,
      "hasFinancialFilters": true,
      "hasCompanyTypeFilters": true,
      "hasLegalFilters": false,
      "hasOwnerFilters": false,
      "hasImportExportFilters": false,
      "hasRegistrationDateFilters": false
    }
  }
}
```

##### C. Markdown Response (Formatted Analysis)
```json
{
  "id": "chatcmpl-550e8400-e29b-41d4-a716-************",
  "object": "chat.completion",
  "created": **********,
  "model": "gpt-4",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "# Phân tích Công ty FPT Corporation\n\n## Thông tin cơ bản\n- **Tên công ty:** FPT Corporation\n- **Mã số thuế:** **********\n- **Loại hình:** Công ty cổ phần\n- **Vốn điều lệ:** 1.100 tỷ VND\n\n## Hiệu quả kinh doanh 2023\n- **Doanh thu:** 45.000 tỷ VND\n- **Lợi nhuận:** 8.500 tỷ VND\n- **Tổng tài sản:** 32.000 tỷ VND\n\n## Hoạt động xuất nhập khẩu\n- **Tổng kim ngạch nhập khẩu:** 2.500 tỷ VND\n- **Tổng kim ngạch xuất khẩu:** 1.800 tỷ VND\n- **Mặt hàng chính:** Thiết bị công nghệ thông tin\n\n### Đánh giá\nFPT là một trong những công ty công nghệ hàng đầu Việt Nam với tăng trưởng ổn định và hoạt động đa dạng."
      },
      "finishReason": "stop"
    }
  ],
  "usage": {
    "promptTokens": 200,
    "completionTokens": 180,
    "totalTokens": 380
  },
  "systemFingerprint": "fp_20240101",
  "sessionId": "550e8400-e29b-41d4-a716-************",
  "contextChunksUsed": 8
}
```

##### D. Error Response
```json
{
  "id": "chatcmpl-error-550e8400",
  "object": "chat.completion",
  "created": **********,
  "model": "gpt-4",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "Xin lỗi, tôi không thể xử lý yêu cầu này do thiếu thông tin cụ thể. Vui lòng cung cấp thêm chi tiết về công ty hoặc tiêu chí tìm kiếm mà bạn quan tâm."
      },
      "finishReason": "stop"
    }
  ],
  "usage": {
    "promptTokens": 50,
    "completionTokens": 45,
    "totalTokens": 95
  },
  "systemFingerprint": "fp_20240101",
  "sessionId": "550e8400-e29b-41d4-a716-************",
  "contextChunksUsed": 0,
  "metadata": {
    "type": "error",
    "validation_status": "Invalid",
    "validation_reason": "Insufficient information provided"
  }
}
```

##### E. Suggestion Response
```json
{
  "id": "chatcmpl-suggestion-550e8400",
  "object": "chat.completion",
  "created": **********,
  "model": "gpt-4",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "Tôi nhận thấy câu hỏi của bạn cần làm rõ thêm. Dưới đây là một số gợi ý để giúp bạn tìm kiếm thông tin công ty hiệu quả hơn:\n\n**Các gợi ý tìm kiếm:**\n\n1. **Tìm kiếm theo ngành nghề**\n   - Ví dụ: \"Các công ty công nghệ thông tin tại TP.HCM\"\n\n2. **Tìm kiếm theo quy mô doanh thu**\n   - Ví dụ: \"Công ty có doanh thu từ 100-500 tỷ VND năm 2023\"\n\n3. **Tìm kiếm theo khu vực địa lý**\n   - Ví dụ: \"Doanh nghiệp sản xuất tại Bình Dương\"\n\nVui lòng chọn một trong các gợi ý trên hoặc cung cấp thêm thông tin cụ thể."
      },
      "finishReason": "stop"
    }
  ],
  "usage": {
    "promptTokens": 75,
    "completionTokens": 120,
    "totalTokens": 195
  },
  "systemFingerprint": "fp_20240101",
  "sessionId": "550e8400-e29b-41d4-a716-************",
  "contextChunksUsed": 0,
  "metadata": {
    "type": "suggestions",
    "validation_reason": "Input needs clarification",
    "suggestions_count": 3
  }
}
```

- **Status Codes:** 200 (Success), 400 (Bad Request)

#### 4. Streaming Chat Completions
- **Method:** POST
- **URL:** `/api/chat/completions/stream`
- **Description:** Create streaming AI chat completion
- **Content-Type:** text/event-stream
- **Request Body:** Same as chat completions
- **Response:** Server-Sent Events stream
- **Status Codes:** 200 (Success)

#### 5. Chat History
- **Method:** GET
- **URL:** `/api/chat/history`
- **Description:** Get user's chat session history
- **Query Parameters:** userId, includeDeleted, pageSize, pageNumber
- **Response:** Paginated list of chat sessions
- **Status Codes:** 200 (Success), 400 (Bad Request)

#### 6. Session Messages
- **Method:** GET
- **URL:** `/api/chat/sessions/{sessionId}/messages`
- **Description:** Get messages from specific chat session
- **Path Parameters:** sessionId
- **Query Parameters:** pageSize, pageNumber
- **Response:** Paginated list of messages
- **Status Codes:** 200 (Success), 404 (Not Found), 400 (Bad Request)

#### 7. Delete Session
- **Method:** DELETE
- **URL:** `/api/chat/sessions/{sessionId}`
- **Description:** Delete (archive) a chat session
- **Path Parameters:** sessionId
- **Query Parameters:** userId
- **Response:** Success/failure status
- **Status Codes:** 200 (Success), 404 (Not Found), 403 (Forbidden), 400 (Bad Request)

### Suggestion APIs

#### 8. Prompt Suggestions
- **Method:** POST
- **URL:** `/api/chat/suggestions/prompts`
- **Description:** Get intelligent prompt suggestions for unclear user input
- **Request Body:**
```json
{
  "userId": "string",
  "sessionId": "uuid",
  "currentInput": "string",
  "domain": "company_search",
  "maxSuggestions": 8
}
```
- **Response:** List of suggested prompts with descriptions and examples
- **Status Codes:** 200 (Success), 400 (Bad Request)

#### 9. Next Word Suggestions
- **Method:** POST
- **URL:** `/api/chat/suggestions/next-word`
- **Description:** Get next word suggestions based on current text
- **Request Body:**
```json
{
  "userId": "string",
  "sessionId": "uuid",
  "currentText": "string",
  "cursorPosition": 0,
  "context": "string",
  "maxSuggestions": 5
}
```
- **Response:** List of word suggestions with confidence scores
- **Status Codes:** 200 (Success), 400 (Bad Request)

#### 10. Sentence Suggestions
- **Method:** POST
- **URL:** `/api/chat/suggestions/sentences`
- **Description:** Get sentence completion or new sentence suggestions
- **Request Body:**
```json
{
  "userId": "string",
  "sessionId": "uuid",
  "currentText": "string",
  "cursorPosition": 0,
  "context": "string",
  "includeNewSentences": true,
  "maxSuggestions": 5
}
```
- **Response:** List of sentence completions and new sentence suggestions
- **Status Codes:** 200 (Success), 400 (Bad Request)

#### 11. Enhance Prompt
- **Method:** POST
- **URL:** `/api/chat/suggestions/enhance-prompt`
- **Description:** Enhance user prompt for better search results
- **Request Body:**
```json
{
  "userId": "string",
  "sessionId": "uuid",
  "originalPrompt": "string",
  "intendedContext": "string",
  "includeExamples": true
}
```
- **Response:** Enhanced prompt with improvements and examples
- **Status Codes:** 200 (Success), 400 (Bad Request)

### Data Chunking APIs

#### 12. Get Available Tables
- **Method:** GET
- **URL:** `/api/chunking/tables`
- **Description:** Get list of tables available for data chunking
- **Response:** List of table names
- **Status Codes:** 200 (Success)

#### 13. Chunking Health Check
- **Method:** GET
- **URL:** `/api/chunking/health`
- **Description:** Check health status of chunking service
- **Response:** Health status and available tables count
- **Status Codes:** 200 (Healthy), 503 (Unhealthy)

#### 14. Get Chunking Status
- **Method:** GET
- **URL:** `/api/chunking/status/{tableName}`
- **Description:** Get chunking status for specific table
- **Path Parameters:** tableName
- **Response:** Detailed chunking status including progress and errors
- **Status Codes:** 200 (Success), 400 (Invalid table name)

#### 15. Chunk Specific Table
- **Method:** POST
- **URL:** `/api/chunking/chunk/{tableName}`
- **Description:** Process and chunk data from specific table for RAG
- **Path Parameters:** tableName
- **Response:** Chunking operation result with statistics
- **Status Codes:** 200 (Success), 400 (Failed)

#### 16. Chunk All Tables
- **Method:** POST
- **URL:** `/api/chunking/chunk-all`
- **Description:** Process and chunk data from all supported tables
- **Response:** Comprehensive chunking results for all tables
- **Status Codes:** 200 (Success)

#### 17. Refresh Table Chunks
- **Method:** POST
- **URL:** `/api/chunking/refresh/{tableName}`
- **Description:** Delete existing chunks and rechunk table data
- **Path Parameters:** tableName
- **Response:** Refresh operation result
- **Status Codes:** 200 (Success), 400 (Failed)

## Features

### RAG (Retrieval-Augmented Generation)
- Automatically retrieves relevant company data context for AI responses
- Supports similarity search across multiple business data tables
- Configurable relevance thresholds and result limits

### Vietnamese Language Support
- Full support for Vietnamese text processing and search
- Handles diacritics and Vietnamese-specific business terminology
- Bilingual responses (Vietnamese/English)

### Advanced Search Capabilities
- Location-based filtering (province, district, commune)
- Industry classification (VSIC codes)
- Financial criteria filtering
- Legal representative and ownership searches
- Import/export data analysis
- Date range filtering for registration and other events

### Intelligent Suggestions
- Context-aware prompt suggestions
- Real-time word and sentence completion
- Prompt enhancement for better search results
- Domain-specific suggestion logic

### Session Management
- Persistent chat sessions across requests
- Message history with pagination
- Session archival and deletion
- User-based session isolation

### Data Processing
- Background data chunking for RAG optimization
- Table-specific processing status monitoring
- Bulk and individual table refresh capabilities
- Health monitoring for data services

## Rate Limits
- Chat APIs: 100 requests/minute per user
- Search APIs: 200 requests/minute per user
- Chunking APIs: 10 requests/minute per user (admin only)
- Suggestion APIs: 50 requests/minute per user

## Error Handling
All APIs return standardized error responses:
```json
{
  "success": false,
  "error": "Error description",
  "details": "Additional error details"
}
```

## Notes
- All timestamps use ISO 8601 format (UTC)
- Monetary values in Vietnamese Dong (VND)
- Company data includes Vietnamese business entities
- Streaming endpoints use Server-Sent Events protocol
- JWT tokens must include appropriate scopes for operations

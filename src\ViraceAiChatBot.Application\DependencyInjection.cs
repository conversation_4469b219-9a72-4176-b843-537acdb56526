using Microsoft.Extensions.DependencyInjection;
using System.Reflection;
using Microsoft.Extensions.Configuration;
using ViraceAiChatBot.Application.Configuration;
using ViraceAiChatBot.Application.Interfaces;
using ViraceAiChatBot.Application.Services;
using CompanySearchService = ViraceAiChatBot.Application.Interfaces.CompanySearchService;
using DataChunkingService = ViraceAiChatBot.Application.Interfaces.DataChunkingService;
using EmbeddingService = ViraceAiChatBot.Application.Interfaces.EmbeddingService;
using InputValidationService = ViraceAiChatBot.Application.Interfaces.InputValidationService;
using OpenAiService = ViraceAiChatBot.Application.Interfaces.OpenAiService;
using ResponseBeautificationService = ViraceAiChatBot.Application.Interfaces.ResponseBeautificationService;
using SuggestionService = ViraceAiChatBot.Application.Interfaces.SuggestionService;

namespace ViraceAiChatBot.Application;

public static class DependencyInjection
{
    public static void AddApplication(this IServiceCollection services, IConfiguration configuration)
    {
        var executingAssembly = Assembly.GetExecutingAssembly();

        try
        {
            services.AddScoped<DataChunkingService, Services.DataChunkingService>();

            // Application Services
            services.AddMemoryCache();
            services.AddScoped<EmbeddingService, Services.EmbeddingService>();

            // External Services Configuration
            services.Configure<OpenAiOptions>(configuration.GetSection("OpenAI"));
            services.Configure<CompanySearchOptions>(configuration.GetSection("CompanySearch"));

            // Register Services
            services.AddScoped<OpenAiService, Services.OpenAiService>();
            services.AddScoped<InputValidationService, Services.InputValidationService>();
            services.AddScoped<ResponseBeautificationService, Services.ResponseBeautificationService>();
            services.AddScoped<SuggestionService, Services.SuggestionService>();

            services.AddHttpClient<CompanySearchService, Services.CompanySearchService>(client =>
            {
                var companySearchOptions = configuration.GetSection("CompanySearch").Get<CompanySearchOptions>();
                if (companySearchOptions == null)
                {
                    return;
                }

                client.BaseAddress = new Uri(companySearchOptions.BaseUrl);
                client.Timeout = TimeSpan.FromSeconds(companySearchOptions.RequestTimeoutSeconds);
            });

            services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(executingAssembly));
        }
        catch
        {
            throw new InvalidOperationException(
                "Failed to register MediatR services. Ensure the assembly is correctly referenced.");
        }
    }
}

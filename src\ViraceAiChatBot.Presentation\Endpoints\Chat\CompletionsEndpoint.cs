using Microsoft.AspNetCore.Mvc;
using MediatR;
using ViraceAiChatBot.Application.Commands;
using ViraceAiChatBot.Application.Response;
using ViraceAiChatBot.Presentation.DTOs;
using Serilog;

namespace ViraceAiChatBot.Presentation.Endpoints.Chat;

/// <summary>
/// Endpoint xử lý chat completions
/// </summary>
public static class CompletionsEndpoint
{
    /// <summary>
    /// Map Chat Completions endpoint
    /// </summary>
    /// <param name="group">Route group</param>
    public static void MapCompletionsEndpoint(this RouteGroupBuilder group)
    {
        group.MapPost("/completions", async Task<IResult> (
                [FromBody] ChatCompletionRequest request,
                IMediator mediator,
                CancellationToken cancellationToken) =>
            {
                try
                {
                    Log.Information(
                        "ChatCompletions called - UserId: {UserId}, SessionId: {SessionId}, HasSessionId: {HasSessionId}",
                        request.UserId, request.SessionId, request.SessionId.HasValue);

                    if (request.Stream == true)
                    {
                        return Results.BadRequest(new { });
                    }

                    var command = new ChatCompletionCommand(
                        UserId: request.UserId ?? "anonymous",
                        Message: request.Messages.LastOrDefault()?.Content ??
                                 throw new ArgumentException("No message content provided"),
                        SessionId: request.SessionId,
                        UseRag: request.UseRag ?? true,
                        MaxTokens: request.MaxTokens ?? 4000,
                        Temperature: request.Temperature ?? 0.7f,
                        TopP: request.TopP ?? 1.0f,
                        Model: request.Model ?? "gpt-4"
                    );

                    Log.Information("ChatCompletionCommand created - UserId: {UserId}, SessionId: {SessionId}",
                        command.UserId, command.SessionId);

                    var result = await mediator.Send(command, cancellationToken);

                    if (result is { ResponseType: ChatResponseType.Json, JsonData: not null })
                    {
                        return Results.Ok(result.JsonData);
                    }

                    Log.Information(
                        "ChatCompletions response - InputSessionId: {InputSessionId}, OutputSessionId: {OutputSessionId}",
                        request.SessionId, result.SessionId);

                    return Results.Ok(result);
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "Error in ChatCompletions endpoint");
                    return Results.BadRequest(new { error = ex.Message });
                }
            })
            .WithName("ChatCompletions")
            .WithSummary("Create a chat completion")
            .WithDescription("Generates a chat completion based on the provided messages and parameters.")
            .Produces<ChatCompletionResponse>(200)
            .Produces<object>(400);
    }
}

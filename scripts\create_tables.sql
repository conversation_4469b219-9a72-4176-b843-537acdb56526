-- V<PERSON>ce<PERSON><PERSON> ChatBot Company Search Database Schema
-- This script creates all required tables for the application

-- Enable required PostgreSQL extensions
CREATE EXTENSION IF NOT EXISTS "vector";
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =============================================
-- CORE TABLES
-- =============================================

-- Table: chat_sessions
CREATE TABLE IF NOT EXISTS chat_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id VARCHAR(100) NOT NULL,
    title VARCHAR(200) NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    last_message_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Table: chat_messages (owned by chat_sessions)
CREATE TABLE IF NOT EXISTS chat_messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID NOT NULL REFERENCES chat_sessions(id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL,
    content TEXT NOT NULL,
    token_count INTEGER NOT NULL DEFAULT 0,
    function_name VARCHAR(100),
    function_arguments JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Table: rag_chunks (for RAG functionality with vector embeddings)
CREATE TABLE IF NOT EXISTS rag_chunks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    table_name VARCHAR(200) NOT NULL,
    row_id VARCHAR(500) NOT NULL,
    column_name VARCHAR(200) NOT NULL,
    content VARCHAR(4000) NOT NULL,
    embedding VECTOR(3072) NOT NULL, -- text-embedding-3-large dimension
    chunk_index INTEGER NOT NULL,
    metadata JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- REFERENCE DATA TABLES
-- =============================================

-- Table: country_index
CREATE TABLE IF NOT EXISTS country_index (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    iso VARCHAR(2) NOT NULL UNIQUE,
    iso3 VARCHAR(3) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Table: company_types
CREATE TABLE IF NOT EXISTS company_types (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code VARCHAR(10) NOT NULL UNIQUE,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Table: currency_exchange_rate
CREATE TABLE IF NOT EXISTS currency_exchange_rate (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    from_currency VARCHAR(3) NOT NULL,
    to_currency VARCHAR(3) NOT NULL,
    rate DECIMAL(18, 8) NOT NULL,
    rate_date DATE NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Table: financial_index
CREATE TABLE IF NOT EXISTS financial_index (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    symbol VARCHAR(20) NOT NULL,
    name VARCHAR(200) NOT NULL,
    index_type VARCHAR(50),
    country_code VARCHAR(3),
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Table: hs_code (Harmonized System Codes)
CREATE TABLE IF NOT EXISTS hs_code (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code VARCHAR(10) NOT NULL UNIQUE,
    description TEXT NOT NULL,
    level INTEGER NOT NULL,
    parent_code VARCHAR(10),
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Table: location_index
CREATE TABLE IF NOT EXISTS location_index (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    country_code VARCHAR(3) NOT NULL,
    region VARCHAR(100),
    city VARCHAR(100),
    postal_code VARCHAR(20),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Table: vsic_index (Vietnam Standard Industrial Classification)
CREATE TABLE IF NOT EXISTS vsic_index (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code VARCHAR(10) NOT NULL UNIQUE,
    name VARCHAR(500) NOT NULL,
    description TEXT,
    level INTEGER NOT NULL,
    parent_code VARCHAR(10),
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

-- Chat Sessions Indexes
CREATE INDEX IF NOT EXISTS ix_chat_sessions_user_id ON chat_sessions(user_id);
CREATE INDEX IF NOT EXISTS ix_chat_sessions_created_at ON chat_sessions(created_at);
CREATE INDEX IF NOT EXISTS ix_chat_sessions_last_message_at ON chat_sessions(last_message_at);

-- Chat Messages Indexes
CREATE INDEX IF NOT EXISTS ix_chat_messages_session_created ON chat_messages(session_id, created_at);
CREATE INDEX IF NOT EXISTS ix_chat_messages_role ON chat_messages(role);

-- RAG Chunks Indexes
CREATE INDEX IF NOT EXISTS ix_rag_chunks_table_row ON rag_chunks(table_name, row_id);
CREATE INDEX IF NOT EXISTS ix_rag_chunks_table_name ON rag_chunks(table_name);
CREATE INDEX IF NOT EXISTS ix_rag_chunks_embedding_hnsw ON rag_chunks USING hnsw (embedding vector_cosine_ops);

-- Country Index Indexes
CREATE INDEX IF NOT EXISTS ix_country_index_name ON country_index(name);

-- Company Types Indexes
CREATE INDEX IF NOT EXISTS ix_company_types_name ON company_types(name);
CREATE INDEX IF NOT EXISTS ix_company_types_is_active ON company_types(is_active);

-- Currency Exchange Rate Indexes
CREATE INDEX IF NOT EXISTS ix_currency_exchange_rate_currencies ON currency_exchange_rate(from_currency, to_currency);
CREATE INDEX IF NOT EXISTS ix_currency_exchange_rate_date ON currency_exchange_rate(rate_date);

-- Financial Index Indexes
CREATE INDEX IF NOT EXISTS ix_financial_index_symbol ON financial_index(symbol);
CREATE INDEX IF NOT EXISTS ix_financial_index_country ON financial_index(country_code);

-- HS Code Indexes
CREATE INDEX IF NOT EXISTS ix_hs_code_parent ON hs_code(parent_code);
CREATE INDEX IF NOT EXISTS ix_hs_code_level ON hs_code(level);

-- Location Index Indexes
CREATE INDEX IF NOT EXISTS ix_location_index_country ON location_index(country_code);
CREATE INDEX IF NOT EXISTS ix_location_index_coordinates ON location_index(latitude, longitude);

-- VSIC Index Indexes
CREATE INDEX IF NOT EXISTS ix_vsic_index_name ON vsic_index(name);
CREATE INDEX IF NOT EXISTS ix_vsic_index_parent ON vsic_index(parent_code);
CREATE INDEX IF NOT EXISTS ix_vsic_index_level ON vsic_index(level);

-- =============================================
-- TRIGGERS FOR UPDATED_AT TIMESTAMPS
-- =============================================

-- Function to update the updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to all tables
CREATE TRIGGER update_chat_sessions_updated_at BEFORE UPDATE ON chat_sessions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_chat_messages_updated_at BEFORE UPDATE ON chat_messages FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_rag_chunks_updated_at BEFORE UPDATE ON rag_chunks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_country_index_updated_at BEFORE UPDATE ON country_index FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_company_types_updated_at BEFORE UPDATE ON company_types FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_currency_exchange_rate_updated_at BEFORE UPDATE ON currency_exchange_rate FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_financial_index_updated_at BEFORE UPDATE ON financial_index FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_hs_code_updated_at BEFORE UPDATE ON hs_code FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_location_index_updated_at BEFORE UPDATE ON location_index FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_vsic_index_updated_at BEFORE UPDATE ON vsic_index FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================
-- SAMPLE DATA (OPTIONAL)
-- =============================================

-- Insert some sample countries
INSERT INTO country_index (iso, iso3, name) VALUES
    ('US', 'USA', 'United States'),
    ('VN', 'VNM', 'Vietnam'),
    ('SG', 'SGP', 'Singapore'),
    ('TH', 'THA', 'Thailand'),
    ('MY', 'MYS', 'Malaysia')
ON CONFLICT (iso) DO NOTHING;

-- Insert some sample company types
INSERT INTO company_types (code, name, description) VALUES
    ('LLC', 'Limited Liability Company', 'A business structure in the US that combines elements of corporations and partnerships'),
    ('JSC', 'Joint Stock Company', 'A company whose stock is owned jointly by shareholders'),
    ('LTD', 'Private Limited Company', 'A privately held company with limited liability'),
    ('PLC', 'Public Limited Company', 'A publicly traded company with limited liability'),
    ('COOP', 'Cooperative', 'A business owned and operated by its members')
ON CONFLICT (code) DO NOTHING;

-- Success message
SELECT 'Database schema created successfully! All tables, indexes, and triggers are ready.' as status;

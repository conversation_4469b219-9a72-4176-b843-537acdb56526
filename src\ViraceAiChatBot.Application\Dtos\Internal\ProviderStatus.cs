namespace ViraceAiChatBot.Application.DTOs.Internal;

/// <summary>
/// Represents the status of a provider used by the application.
/// </summary>
/// <param name="Name">The name of the provider.</param>
/// <param name="IsHeal<PERSON>">Indicates whether the provider is healthy.</param>
/// <param name="LastChecked">The timestamp when the provider's health was last checked.</param>
/// <param name="ErrorMessage">The optional error message if the provider is not healthy.</param>
public record ProviderStatus(
    string Name,
    bool IsHealthy,
    DateTime LastChecked,
    string? ErrorMessage = null
);

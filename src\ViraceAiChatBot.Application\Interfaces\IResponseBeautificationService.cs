namespace ViraceAiChatBot.Application.Interfaces;

public interface ResponseBeautificationService
{
    Task<string> BeautifyApiResponseAsync(
        string apiResponse,
        string userQuery,
        CancellationToken cancellationToken = default);

    Task<string> FormatErrorResponseAsync(
        string errorMessage,
        string userQuery,
        CancellationToken cancellationToken = default);
}

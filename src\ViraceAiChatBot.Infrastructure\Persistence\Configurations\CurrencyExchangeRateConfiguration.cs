using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ViraceAiChatBot.Domain.Entities.ReferenceData;

namespace ViraceAiChatBot.Infrastructure.Persistence.Configurations;

public class CurrencyExchangeRateConfiguration : IEntityTypeConfiguration<CurrencyExchangeRate>
{
    public void Configure(EntityTypeBuilder<CurrencyExchangeRate> builder)
    {
        builder.ToTable("CurrencyExchangeRate");

        builder.HasKey(x => x.Year);

        builder.Property(x => x.Year)
            .HasColumnName("Year")
            .IsRequired();

        builder.Property(x => x.Value)
            .HasColumnName("Value")
            .IsRequired();

        // Indexes for performance
        builder.HasIndex(x => x.Year)
            .HasDatabaseName("ix_currency_exchange_rate_year");
    }
}

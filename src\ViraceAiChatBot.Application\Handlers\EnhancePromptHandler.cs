using MediatR;
using Microsoft.Extensions.Logging;
using ViraceAiChatBot.Application.Commands;
using ViraceAiChatBot.Application.Dtos;
using ViraceAiChatBot.Application.Interfaces;

namespace ViraceAiChatBot.Application.Handlers;

public class EnhancePromptHandler(
    SuggestionService suggestionService,
    ILogger<EnhancePromptHandler> logger) : IRequestHandler<EnhancePromptCommand, EnhancedPromptResponse>
{
    public async Task<EnhancedPromptResponse> Handle(EnhancePromptCommand request, CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Processing prompt enhancement for user {UserId}, prompt: {Prompt}",
                request.UserId, request.OriginalPrompt);

            var response = await suggestionService.EnhancePromptAsync(
                request.OriginalPrompt,
                request.IntendedContext,
                request.IncludeExamples,
                cancellationToken);

            logger.LogInformation("Enhanced prompt for user {UserId} with {Count} improvements",
                request.UserId, response.Improvements.Count);

            return response;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing prompt enhancement for user {UserId}", request.UserId);

            return new EnhancedPromptResponse(
                request.OriginalPrompt,
                [ "Error occurred during enhancement" ],
                request.OriginalPrompt,
                [],
                TimeSpan.Zero
            );
        }
    }
}

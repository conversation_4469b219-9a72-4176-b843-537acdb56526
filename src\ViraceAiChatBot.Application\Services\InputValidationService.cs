using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;
using ViraceAiChatBot.Application.Interfaces;

namespace ViraceAiChatBot.Application.Services;

public class InputValidationService(Interfaces.OpenAiService openAiService, ILogger<InputValidationService> logger)
    : Interfaces.InputValidationService
{
    private readonly string inputValidationPrompt = @"
You are an AI assistant designed to validate user inputs for a Company Search System. Your role is to be PERMISSIVE and only reject inputs that are clearly off-topic or inappropriate.

CORE PRINCIPLE: Accept almost all inputs that could reasonably relate to company search or business information.

SYSTEM CAPABILITIES: This system supports two main types of company search:
1. Simple Search: Search by company name, tax code, keywords (e.g., ""FPT"", ""Tập đoàn FPT"", ""công ty ABC"")
2. Advanced Search: Search by multiple criteria like location, industry codes (VSIC), financial metrics, company types, ownership, import/export data, registration dates

ACCEPTANCE GUIDELINES (Be Very Permissive):

✅ ACCEPT ALL company search related requests:
- Company names/groups: ""<PERSON><PERSON><PERSON> kiếm những công ty của tập đoàn FPT"", ""FPT Corporation"", ""VinGroup""
- Business searches: ""công ty bất động sản"", ""ngân hàng"", ""công ty dược""
- Tax codes: ""mã số thuế 0123456789"", ""MST của FPT""
- Location-based: ""công ty ở Hà Nội"", ""doanh nghiệp TPHCM""
- Industry queries: ""công ty công nghệ"", ""ngành ngân hàng"", ""dệt may""
- Financial criteria: ""công ty có vốn trên 100 tỷ"", ""doanh thu cao""
- Registration info: ""công ty thành lập năm 2020""
- Import/export: ""công ty xuất khẩu"", ""nhập khẩu thép""
- Ownership: ""công ty nhà nước"", ""tư nhân""
- Incomplete info: ""công ty ABC"", ""địa chỉ 123 Nguyễn Văn Linh""
- Business analysis: ""phân tích FPT"", ""báo cáo VinMilk""
- Abbreviations: ""HPG"", ""VCB"", ""VIC""

✅ ACCEPT vague but business-related requests:
- ""tìm công ty"", ""search company"", ""doanh nghiệp""
- ""thông tin công ty"", ""company information""

❌ ONLY REJECT clearly off-topic requests:
- Personal matters: ""tình yêu"", ""nấu ăn"", ""du lịch""
- Non-business topics: ""thời tiết"", ""thể thao"", ""phim ảnh""
- Inappropriate content: profanity, offensive language
- Completely unrelated: ""cách làm bánh"", ""học tiếng Anh""

VALIDATION RESPONSES:
- Valid: For ANY company/business related request (be very generous)
- NeedsClarification: Only when user provides extremely minimal info like just ""ABC"" without context
- Invalid: Only for clearly off-topic requests
- Other statuses: Use sparingly, prefer Valid or NeedsClarification

LANGUAGE: Always respond in the same language as the user's input.

OUTPUT: Return JSON strictly following InputValidationResult model:

```json
{
  ""reason"": ""Brief explanation (only for non-Valid status)"",
  ""status"": ""Valid|Invalid|NeedsClarification|etc"",
  ""responseMessage"": ""Message to user (optional)""
}
```

EXAMPLES:
- ""Tìm kiếm những công ty của tập đoàn FPT"" → Status: Valid
- ""FPT"" → Status: Valid
- ""công ty ABC"" → Status: Valid
- ""doanh nghiệp bất động sản"" → Status: Valid
- ""nấu cơm"" → Status: Invalid
- ""thời tiết hôm nay"" → Status: Invalid
";

    public async Task<InputValidationResult> ValidateInputAsync(string input,
        CancellationToken cancellationToken = default)
    {
        return await ValidateInputWithContextAsync(input, null, null, cancellationToken);
    }

    private async Task<InputValidationResult> ValidateInputWithContextAsync(string input,
        string? previousMessage = null,
        string? previousAssistantMessage = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("Validating input: {InputLength} characters", input.Length);

            // Quick check for confirmation responses
            if (IsConfirmationResponse(input, previousAssistantMessage))
            {
                logger.LogInformation("Detected confirmation response from user");
                return new InputValidationResult
                {
                    Status = InputValidationStatus.ConfirmationResponse, ResponseMessage = null
                };
            }

            var contextPrompt = "";
            if (!string.IsNullOrEmpty(previousMessage) || !string.IsNullOrEmpty(previousAssistantMessage))
            {
                contextPrompt =
                    $"\n\nPrevious context:\nUser: {previousMessage ?? "N/A"}\nAssistant: {previousAssistantMessage ?? "N/A"}\n";
            }

            var prompt =
                $"Analyze the following question/request and determine if clarification is needed: \"{input}\"{contextPrompt}";

            var response = await openAiService.ChatCompletionAsync(
                $"{inputValidationPrompt}\n\n{prompt}",
                maxTokens: 1000,
                temperature: 0.3,
                cancellationToken: cancellationToken);

            return ParseValidationResponse(response.Content);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error validating input");
            return new InputValidationResult { Status = InputValidationStatus.Invalid };
        }
    }

    public async Task<InputValidationResult> ValidateInputWithFullContextAsync(string input,
        List<ConversationMessage> conversationHistory,
        CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation(
                "Validating input with full context: {InputLength} characters, {HistoryCount} messages",
                input.Length, conversationHistory.Count);

            // Quick check for confirmation responses using latest assistant message
            var lastAssistantMessage = conversationHistory.LastOrDefault(m => m.Role == "assistant");
            if (IsConfirmationResponse(input, lastAssistantMessage?.Content))
            {
                logger.LogInformation("Detected confirmation response from user based on conversation history");
                return new InputValidationResult
                {
                    Status = InputValidationStatus.ConfirmationResponse,
                    ResponseMessage = null // Let the main handler process this
                };
            }

            var contextPrompt = "";
            if (conversationHistory.Any())
            {
                contextPrompt = "\n\nConversation History:\n";
                foreach (var msg in conversationHistory)
                {
                    contextPrompt += $"{msg.Role.ToUpper()}: {msg.Content}\n";
                }

                contextPrompt += "\n";
            }

            var prompt =
                $"Analyze the following question/request and determine if clarification is needed: \"{input}\"{contextPrompt}";

            var response = await openAiService.ChatCompletionAsync(
                $"{inputValidationPrompt}\n\n{prompt}",
                maxTokens: 1500, // Increase token limit for longer context
                temperature: 0.3,
                cancellationToken: cancellationToken);

            return ParseValidationResponse(response.Content);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error validating input with full context");
            return new InputValidationResult { Status = InputValidationStatus.Invalid };
        }
    }

    private static bool IsConfirmationResponse(string input, string? previousAssistantMessage)
    {
        if (string.IsNullOrEmpty(previousAssistantMessage))
        {
            return false;
        }

        var confirmationPatterns = new[]
        {
            "đúng", "đúng vậy", "đúng rồi", "chính xác", "phải", "ừ", "ừm", "ok", "okay", "yes", "yeah", "yep",
            "correct", "right", "exactly", "confirm", "vâng", "dạ", "đồng ý", "tất cả", "tất cả ư", "toàn bộ"
        };

        var normalizedInput = input.ToLower().Trim();

        if (normalizedInput.Length <= 20 && confirmationPatterns.Any(pattern => normalizedInput.Contains(pattern)))
        {
            var confirmationQuestions = new[]
            {
                "có phải bạn muốn", "bạn muốn", "phải không", "đúng không", "confirm", "is this correct",
                "tất cả ư", "toàn bộ"
            };

            return confirmationQuestions.Any(q => previousAssistantMessage.ToLower().Contains(q));
        }

        return false;
    }

    private InputValidationResult ParseValidationResponse(string response)
    {
        try
        {
            string cleanedResponse = response;

            if (cleanedResponse.Contains("```"))
            {
                cleanedResponse = Regex.Replace(
                    cleanedResponse,
                    @"```(?:json)?\s*([\s\S]*?)```",
                    "$1",
                    RegexOptions.Compiled
                ).Trim();
            }

            if (!cleanedResponse.StartsWith('{') || !cleanedResponse.EndsWith('}'))
            {
                logger.LogWarning("Invalid JSON format in response: {Response}", response);
                return new InputValidationResult { Status = InputValidationStatus.Invalid };
            }

            var options = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                Converters = { new JsonStringEnumConverter(JsonNamingPolicy.CamelCase) }
            };

            var result = JsonSerializer.Deserialize<InputValidationResult>(cleanedResponse, options);
            if (result == null)
            {
                logger.LogWarning("Failed to parse validation result");
                return new InputValidationResult { Status = InputValidationStatus.Invalid };
            }

            return result;
        }
        catch (JsonException ex)
        {
            logger.LogError(ex, "Error parsing validation result JSON: {Response}", response);
            return new InputValidationResult { Status = InputValidationStatus.Invalid };
        }
    }
}

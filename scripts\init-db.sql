-- Enable the pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Verify the extension is installed
SELECT extname, extversion FROM pg_extension WHERE extname = 'vector';

-- Create a test vector to ensure everything is working
DO $$
BEGIN
    -- Test vector operations
    PERFORM '[1,2,3]'::vector;
    RAISE NOTICE 'pgvector extension is working correctly';
END $$;

-- Set up database configuration for optimal vector performance
ALTER SYSTEM SET shared_preload_libraries = 'vector';
ALTER SYSTEM SET max_parallel_workers_per_gather = 2;
ALTER SYSTEM SET max_parallel_workers = 8;
ALTER SYSTEM SET work_mem = '256MB';

-- Create schemas for better organization (optional)
CREATE SCHEMA IF NOT EXISTS rag;
CREATE SCHEMA IF NOT EXISTS chat;

COMMENT ON SCHEMA rag IS 'Schema for RAG-related tables and vector operations';
COMMENT ON SCHEMA chat IS 'Schema for chat sessions and messages';

-- Log successful initialization
SELECT 'Database initialized successfully with pgvector extension' as status;

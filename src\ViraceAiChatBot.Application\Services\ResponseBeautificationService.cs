using Microsoft.Extensions.Logging;
using ViraceAiChatBot.Application.Interfaces;

namespace ViraceAiChatBot.Application.Services;

public class ResponseBeautificationService(Interfaces.OpenAiService openAiService, ILogger<ResponseBeautificationService> logger)
    : Interfaces.ResponseBeautificationService
{
    private readonly string beautificationSystemPrompt = @"
You are an AI assistant that helps beautify API responses based on the user's query.
Your task is to:
1. Analyze the JSON data from the API
2. Extract only the information that is relevant to the user's original question
3. Format the response in a human-readable way using markdown formatting
4. Respond in the SAME LANGUAGE as the user's original query
5. Remove any data that is not relevant to the user's question
6. If the data is empty or doesn't contain relevant information, inform the user

DO NOT include the raw JSON in your response. Instead, present the information in a well-formatted, easy-to-read manner.
Use markdown formatting for better readability:
- Use ## for section headings
- Use **bold** for important information
- Use *italic* for emphasis
- Use bullet points or numbered lists for multiple items
- Use `code` for specific values or identifiers
- Use tables for structured data if appropriate
";

    private readonly string errorFormattingSystemPrompt = @"
You are an AI assistant that responds to users in their own language when API searches return no results or errors.
Please inform the user that no data was found based on their search criteria or that an error occurred.
Respond in the SAME LANGUAGE as the user's original query.
Format your response using markdown for better readability.
Use headings, bold, italic, and other markdown formatting as appropriate.
Be helpful and suggest what the user might try differently.
";

    public async Task<string> BeautifyApiResponseAsync(string apiResponse, string userQuery, CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("Beautifying API response with AI based on user query");

            var userPrompt = $@"
Original user query: ""{userQuery}""

API response data (JSON):
{apiResponse}

Please beautify this response by extracting only the relevant information based on the user's query.
Format it in a human-readable way and respond in the same language as the user's query.
";

            var response = await openAiService.ChatCompletionAsync(
                $"{beautificationSystemPrompt}\n\n{userPrompt}",
                maxTokens: 4000,
                temperature: 0.3,
                cancellationToken: cancellationToken);

            logger.LogInformation("Successfully beautified API response with AI");
            return response.Content;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error beautifying API response with AI");
            // If there's an error, return the original API response
            return apiResponse;
        }
    }

    public async Task<string> FormatErrorResponseAsync(string errorMessage, string userQuery, CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("Formatting error response for user query");

            var userPrompt = $@"
Original user query: ""{userQuery}""

API error message: ""{errorMessage}""

Please inform the user that no data was found or an error occurred.
Provide helpful suggestions and respond in the same language as the user's query.
";

            var response = await openAiService.ChatCompletionAsync(
                $"{errorFormattingSystemPrompt}\n\n{userPrompt}",
                maxTokens: 1000,
                temperature: 0.3,
                cancellationToken: cancellationToken);

            logger.LogInformation("Successfully formatted error response");
            return response.Content;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error formatting error response");
            return $"No data was found based on your search criteria. Error: {errorMessage}";
        }
    }
}

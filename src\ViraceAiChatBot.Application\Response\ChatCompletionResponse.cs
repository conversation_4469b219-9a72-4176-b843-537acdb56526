using ViraceAiChatBot.Application.Records;

namespace ViraceAiChatBot.Application.Response;

/// <summary>
/// Response type for frontend mapping
/// </summary>
public enum ChatResponseType
{
    /// <summary>
    /// Plain text response
    /// </summary>
    Text = 0,

    /// <summary>
    /// Markdown formatted response
    /// </summary>
    Markdown = 1,

    /// <summary>
    /// JSON data response from function calls
    /// </summary>
    Json = 2,

}

/// <summary>
/// Pagination information for JSON responses
/// </summary>
public record PaginationInfo(
    int CurrentPage,
    int PageSize,
    int TotalItems,
    int TotalPages,
    bool HasNextPage,
    bool HasPreviousPage,
    string? NextPageEndpoint = null,
    string? PreviousPageEndpoint = null
);

public record class ChatCompletionResponse(
    string Content = "",
    Guid SessionId = default,
    Guid MessageId = default,
    int TokensUsed = 0,
    int ContextChunksUsed = 0,
    string? FunctionCall = "",
    TimeSpan ProcessingTime = default,
    List<RetrievedChunk>? SourceChunks = null,
    Dictionary<string, object>? Metadata = null,
    ChatResponseType ResponseType = ChatResponseType.Text,
    PaginationInfo? Pagination = null,
    object? JsonData = null);

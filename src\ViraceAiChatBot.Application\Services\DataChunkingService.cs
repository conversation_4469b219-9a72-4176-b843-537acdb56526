using System.Diagnostics;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ViraceAiChatBot.Application.Interfaces;
using ViraceAiChatBot.Application.Records;
using ViraceAiChatBot.Domain.Entities;
using ViraceAiChatBot.Domain.Interfaces;
using ViraceAiChatBot.Infrastructure.Persistence;

namespace ViraceAiChatBot.Application.Services;

public class DataChunkingService(
    ChatBotDbContext context,
    IRagChunkRepository ragChunkRepository,
    Interfaces.OpenAiService openAiService,
    ILogger<DataChunkingService> logger)
    : Interfaces.DataChunkingService
{
    public async Task<DataChunkingResult> ChunkTableAsync(
        string tableName,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        logger.LogInformation("Starting chunking process for table: {TableName}", tableName);

        try
        {
            var result = tableName switch
            {
                // "CountryIndex" => await ChunkCountryIndexAsync(cancellationToken),
                "CompanyTypes" => await ChunkCompanyTypesAsync(cancellationToken),
                "HSCode" => await ChunkHsCodeAsync(cancellationToken),
                "VSICIndex" => await ChunkVSICIndexAsync(cancellationToken),
                "CurrencyExchangeRate" => await ChunkCurrencyExchangeRateAsync(cancellationToken),
                "FinancialIndex" => await ChunkFinancialIndexAsync(cancellationToken),
                "FinancialStatementIndex" => await ChunkFinancialStatementIndexAsync(cancellationToken),
                "LocationIndex" => await ChunkLocationIndexAsync(cancellationToken),
                "OwnershipTypeIndex" => await ChunkOwnershipTypeIndexAsync(cancellationToken),
                "RelatedCompanyTypeIndex" => await ChunkRelatedCompanyTypeIndexAsync(cancellationToken),

                _ => new DataChunkingResult
                (
                    TableName: tableName,
                    Success: false,
                    ErrorMessage: $"Table '{tableName}' is not supported for chunking",
                    ProcessingTime: stopwatch.Elapsed
                )
            };

            result = result with { ProcessingTime = stopwatch.Elapsed };
            logger.LogInformation(
                "Completed chunking for table: {TableName}. Processed: {ProcessedRecords}, Generated: {GeneratedChunks}",
                tableName, result.ProcessedRecords, result.GeneratedChunks);

            return result;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error chunking table: {TableName}", tableName);
            return new DataChunkingResult
            {
                TableName = tableName,
                Success = false,
                ErrorMessage = ex.Message,
                ProcessingTime = stopwatch.Elapsed
            };
        }
    }

    public async Task<DataChunkingResult> ChunkAllTablesAsync(
        CancellationToken cancellationToken = default)
    {
        var tables = new[]
        {
            "CountryIndex", "CompanyTypes", "HSCode", "VSICIndex", "CurrencyExchangeRate", "FinancialIndex",
            "FinancialStatementIndex", "LocationIndex", "OwnershipTypeIndex", "RelatedCompanyTypeIndex"
        };
        var totalProcessed = 0;
        var totalChunks = 0;
        var warnings = new List<string>();

        foreach (var tableName in tables)
        {
            var result = await ChunkTableAsync(tableName, cancellationToken);
            totalProcessed += result.ProcessedRecords;
            totalChunks += result.GeneratedChunks;
            if (!result.Success)
                warnings.Add($"{tableName}: {result.ErrorMessage}");
        }

        return new DataChunkingResult
        {
            TableName = "All Tables",
            ProcessedRecords = totalProcessed,
            GeneratedChunks = totalChunks,
            Success = warnings.Count == 0,
            Warnings = warnings
        };
    }

    public async Task<DataChunkingResult> RefreshTableChunksAsync(
        string tableName,
        CancellationToken cancellationToken = default)
    {
        var existingChunks = await ragChunkRepository.GetBySourceAsync(tableName, "*", cancellationToken);
        foreach (var chunk in existingChunks)
        {
            await ragChunkRepository.DeleteAsync(chunk, cancellationToken);
        }

        return await ChunkTableAsync(tableName, cancellationToken);
    }

    public Task<List<string>> GetAvailableTablesAsync(CancellationToken cancellationToken = default)
    {
        return Task.FromResult<List<string>>([
            "CountryIndex", "CompanyTypes", "HSCode", "VSICIndex",
            "CurrencyExchangeRate", "FinancialIndex", "FinancialStatementIndex", "LocationIndex",
            "OwnershipTypeIndex", "RelatedCompanyTypeIndex"
        ]);
    }

    public async Task<ChunkingStatus> GetChunkingStatusAsync(
        string tableName,
        CancellationToken cancellationToken = default)
    {
        var chunkCount = await context.RagChunks.CountAsync(x => x.TableName == tableName, cancellationToken);
        var lastProcessed = await ragChunkRepository.GetLastProcessedTimeAsync(tableName, cancellationToken);

        var totalRecords = tableName switch
        {
            "CountryIndex" => await context.CountryIndex.CountAsync(cancellationToken),
            "CompanyTypes" => await context.CompanyTypes.CountAsync(cancellationToken),
            "HSCode" => await context.HSCode.CountAsync(cancellationToken),
            "VSICIndex" => await context.VSICIndex.CountAsync(cancellationToken),
            "CurrencyExchangeRate" => await context.CurrencyExchangeRate.CountAsync(cancellationToken),
            "FinancialIndex" => await context.FinancialIndex.CountAsync(cancellationToken),
            "FinancialStatementIndex" => await context.FinancialStatementIndex.CountAsync(cancellationToken),
            "LocationIndex" => await context.LocationIndex.CountAsync(cancellationToken),
            "OwnershipTypeIndex" => await context.OwnershipTypeIndex.CountAsync(cancellationToken),
            "RelatedCompanyTypeIndex" => await context.RelatedCompanyTypeIndex.CountAsync(cancellationToken),
            _ => 0
        };

        return new ChunkingStatus
        (
            TableName: tableName,
            TotalRecords: totalRecords,
            ChunkedRecords: chunkCount,
            LastProcessed: lastProcessed,
            IsUpToDate: chunkCount > 0 && lastProcessed.HasValue
        );
    }

    // Generic helper method to reduce code duplication
    private async Task<DataChunkingResult> ChunkEntitiesAsync<T>(
        string tableName,
        List<T> entities,
        Func<T, string> contentSelector,
        Func<T, string> rowIdSelector,
        CancellationToken cancellationToken)
    {
        var chunks = new List<RagChunk>();

        foreach (var entity in entities)
        {
            var content = contentSelector(entity);
            var embedding = await openAiService.GetEmbeddingAsync(content, cancellationToken: cancellationToken);

            var chunk = new RagChunk(
                tableName: tableName,
                rowId: rowIdSelector(entity),
                columnName: "SearchableContent",
                chunkIndex: 0,
                content: content,
                embedding: embedding
            );

            chunks.Add(chunk);
        }

        await ragChunkRepository.AddRangeAsync(chunks, cancellationToken);

        return new DataChunkingResult
        {
            TableName = tableName, ProcessedRecords = entities.Count, GeneratedChunks = chunks.Count, Success = true
        };
    }

    // Private chunking methods

    private async Task<DataChunkingResult> ChunkCompanyTypesAsync(CancellationToken cancellationToken)
    {
        return await ChunkEntitiesAsync(
            "CompanyTypes",
            await context.CompanyTypes.AsNoTracking().ToListAsync(cancellationToken),
            type => $"Company Type: {type.Name} (English: {type.EnName}, Code: {type.Code})",
            type => type.Id.ToString(),
            cancellationToken);
    }

    private async Task<DataChunkingResult> ChunkHsCodeAsync(CancellationToken cancellationToken)
    {
        var hsCodes = await context.HSCode.AsNoTracking().ToListAsync(cancellationToken);
        var chunks = new List<RagChunk>();

        foreach (var hsCode in hsCodes)
        {
            var content = $"HS Code {hsCode.Code2}-{hsCode.Code4}: {hsCode.DescEn} (Vietnamese: {hsCode.DescVi})";
            var embedding = await openAiService.GetEmbeddingAsync(content, cancellationToken: cancellationToken);

            var chunk = new RagChunk(
                tableName: "HSCode",
                rowId: hsCode.Code2.ToString(),
                columnName: "SearchableContent",
                chunkIndex: 0,
                content: content,
                embedding: embedding
            );

            chunks.Add(chunk);
        }

        await ragChunkRepository.AddRangeAsync(chunks, cancellationToken);

        return new DataChunkingResult
        {
            TableName = "HSCode", ProcessedRecords = hsCodes.Count, GeneratedChunks = chunks.Count, Success = true
        };
    }

    private async Task<DataChunkingResult> ChunkVSICIndexAsync(CancellationToken cancellationToken)
    {
        var vsicIndices = await context.VSICIndex.AsNoTracking().ToListAsync(cancellationToken);
        var chunks = new List<RagChunk>();

        foreach (var vsicIndex in vsicIndices)
        {
            var content = $"VSIC Industry: {vsicIndex.Name} (English: {vsicIndex.EnName}, Code: {vsicIndex.Code})";
            var embedding = await openAiService.GetEmbeddingAsync(content, cancellationToken: cancellationToken);

            var chunk = new RagChunk(
                tableName: "VSICIndex",
                rowId: vsicIndex.Code.ToString(),
                columnName: "SearchableContent",
                chunkIndex: 0,
                content: content,
                embedding: embedding
            );

            chunks.Add(chunk);
        }

        await ragChunkRepository.AddRangeAsync(chunks, cancellationToken);

        return new DataChunkingResult
        {
            TableName = "VSICIndex",
            ProcessedRecords = vsicIndices.Count,
            GeneratedChunks = chunks.Count,
            Success = true
        };
    }

    private async Task<DataChunkingResult> ChunkCurrencyExchangeRateAsync(CancellationToken cancellationToken)
    {
        var currencyRates = await context.CurrencyExchangeRate.AsNoTracking().ToListAsync(cancellationToken);
        var chunks = new List<RagChunk>();

        foreach (var currencyRate in currencyRates)
        {
            var content = $"Currency Exchange Rate: Year {currencyRate.Year}, Value {currencyRate.Value}";
            var embedding = await openAiService.GetEmbeddingAsync(content, cancellationToken: cancellationToken);

            var chunk = new RagChunk(
                tableName: "CurrencyExchangeRate",
                rowId: $"{currencyRate.Year}_{currencyRate.Value}",
                columnName: "SearchableContent",
                chunkIndex: 0,
                content: content,
                embedding: embedding
            );

            chunks.Add(chunk);
        }

        await ragChunkRepository.AddRangeAsync(chunks, cancellationToken);

        return new DataChunkingResult
        {
            TableName = "CurrencyExchangeRate",
            ProcessedRecords = currencyRates.Count,
            GeneratedChunks = chunks.Count,
            Success = true
        };
    }

    private async Task<DataChunkingResult> ChunkFinancialIndexAsync(CancellationToken cancellationToken)
    {
        var financialIndices = await context.FinancialIndex.AsNoTracking().ToListAsync(cancellationToken);
        var chunks = new List<RagChunk>();

        foreach (var financialIndex in financialIndices)
        {
            var content = $"Financial Index: {financialIndex.Name} (English: {financialIndex.EnName})";
            if (!string.IsNullOrWhiteSpace(financialIndex.Unit))
                content += $", Unit: {financialIndex.Unit}";
            if (!string.IsNullOrWhiteSpace(financialIndex.Type))
                content += $", Type: {financialIndex.Type}";
            if (!string.IsNullOrWhiteSpace(financialIndex.TypeIndex))
                content += $", Type Index: {financialIndex.TypeIndex}";

            var embedding = await openAiService.GetEmbeddingAsync(content, cancellationToken: cancellationToken);

            var chunk = new RagChunk(
                tableName: "FinancialIndex",
                rowId: financialIndex.Id.ToString(),
                columnName: "SearchableContent",
                chunkIndex: 0,
                content: content,
                embedding: embedding
            );

            chunks.Add(chunk);
        }

        await ragChunkRepository.AddRangeAsync(chunks, cancellationToken);

        return new DataChunkingResult
        {
            TableName = "FinancialIndex",
            ProcessedRecords = financialIndices.Count,
            GeneratedChunks = chunks.Count,
            Success = true
        };
    }

    private async Task<DataChunkingResult> ChunkFinancialStatementIndexAsync(CancellationToken cancellationToken)
    {
        var financialStatementIndices =
            await context.FinancialStatementIndex.AsNoTracking().ToListAsync(cancellationToken);
        var chunks = new List<RagChunk>();

        foreach (var financialStatementIndex in financialStatementIndices)
        {
            var content =
                $"Financial Statement Index: {financialStatementIndex.Type} (English: {financialStatementIndex.EnType}), Type Index: {financialStatementIndex.TypeIndex}";
            var embedding = await openAiService.GetEmbeddingAsync(content, cancellationToken: cancellationToken);

            var chunk = new RagChunk(
                tableName: "FinancialStatementIndex",
                rowId: financialStatementIndex.Id.ToString(),
                columnName: "SearchableContent",
                chunkIndex: 0,
                content: content,
                embedding: embedding
            );

            chunks.Add(chunk);
        }

        await ragChunkRepository.AddRangeAsync(chunks, cancellationToken);

        return new DataChunkingResult
        {
            TableName = "FinancialStatementIndex",
            ProcessedRecords = financialStatementIndices.Count,
            GeneratedChunks = chunks.Count,
            Success = true
        };
    }

    private async Task<DataChunkingResult> ChunkLocationIndexAsync(CancellationToken cancellationToken)
    {
        var locationIndices = await context.LocationIndex.AsNoTracking().ToListAsync(cancellationToken);
        var chunks = new List<RagChunk>();

        foreach (var locationIndex in locationIndices)
        {
            var content =
                $"Location: {locationIndex.Name} (English: {locationIndex.EngName}), Level: {locationIndex.Level}";
            if (locationIndex.ParentId.HasValue)
                content += $", Parent ID: {locationIndex.ParentId}";

            var embedding = await openAiService.GetEmbeddingAsync(content, cancellationToken: cancellationToken);

            var chunk = new RagChunk(
                tableName: "LocationIndex",
                rowId: locationIndex.Id.ToString(),
                columnName: "SearchableContent",
                chunkIndex: 0,
                content: content,
                embedding: embedding
            );

            chunks.Add(chunk);
        }

        await ragChunkRepository.AddRangeAsync(chunks, cancellationToken);

        return new DataChunkingResult
        {
            TableName = "LocationIndex",
            ProcessedRecords = locationIndices.Count,
            GeneratedChunks = chunks.Count,
            Success = true
        };
    }

    private async Task<DataChunkingResult> ChunkOwnershipTypeIndexAsync(CancellationToken cancellationToken)
    {
        var ownershipTypeIndices = await context.OwnershipTypeIndex.AsNoTracking().ToListAsync(cancellationToken);
        var chunks = new List<RagChunk>();

        foreach (var ownershipTypeIndex in ownershipTypeIndices)
        {
            var content = $"Ownership Type: {ownershipTypeIndex.Name} (English: {ownershipTypeIndex.EnName})";
            var embedding = await openAiService.GetEmbeddingAsync(content, cancellationToken: cancellationToken);

            var chunk = new RagChunk(
                tableName: "OwnershipTypeIndex",
                rowId: ownershipTypeIndex.Id.ToString(),
                columnName: "SearchableContent",
                chunkIndex: 0,
                content: content,
                embedding: embedding
            );

            chunks.Add(chunk);
        }

        await ragChunkRepository.AddRangeAsync(chunks, cancellationToken);

        return new DataChunkingResult
        {
            TableName = "OwnershipTypeIndex",
            ProcessedRecords = ownershipTypeIndices.Count,
            GeneratedChunks = chunks.Count,
            Success = true
        };
    }

    private async Task<DataChunkingResult> ChunkRelatedCompanyTypeIndexAsync(CancellationToken cancellationToken)
    {
        var relatedCompanyTypeIndices =
            await context.RelatedCompanyTypeIndex.AsNoTracking().ToListAsync(cancellationToken);
        var chunks = new List<RagChunk>();

        foreach (var relatedCompanyTypeIndex in relatedCompanyTypeIndices)
        {
            var content =
                $"Related Company Type: {relatedCompanyTypeIndex.Name} (English: {relatedCompanyTypeIndex.EnName})";
            var embedding = await openAiService.GetEmbeddingAsync(content, cancellationToken: cancellationToken);

            var chunk = new RagChunk(
                tableName: "RelatedCompanyTypeIndex",
                rowId: relatedCompanyTypeIndex.Id.ToString(),
                columnName: "SearchableContent",
                chunkIndex: 0,
                content: content,
                embedding: embedding
            );

            chunks.Add(chunk);
        }

        await ragChunkRepository.AddRangeAsync(chunks, cancellationToken);

        return new DataChunkingResult
        {
            TableName = "RelatedCompanyTypeIndex",
            ProcessedRecords = relatedCompanyTypeIndices.Count,
            GeneratedChunks = chunks.Count,
            Success = true
        };
    }
}

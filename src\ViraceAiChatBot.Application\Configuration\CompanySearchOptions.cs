namespace ViraceAiChatBot.Application.Configuration;

/// <summary>
/// Represents configuration options for company search functionality.
/// </summary>
public class CompanySearchOptions
{
    /// <summary>
    /// Gets or sets the base URL for the company search API.
    /// </summary>
    public string BaseUrl { get; set; } = "https://api.example.com";

    /// <summary>
    /// Gets or sets the default number of items per page for search results.
    /// </summary>
    public int DefaultPageSize { get; set; } = 20;

    /// <summary>
    /// Gets or sets the maximum number of items per page for search results.
    /// </summary>
    public int MaxPageSize { get; set; } = 100;

    /// <summary>
    /// Gets or sets a value indicating whether caching is enabled for search results.
    /// </summary>
    public bool EnableCaching { get; set; } = true;

    /// <summary>
    /// Gets or sets the duration, in minutes, for which cached search results are stored.
    /// </summary>
    public int CacheDurationMinutes { get; set; } = 30;

    /// <summary>
    /// Gets or sets the timeout duration, in seconds, for API requests.
    /// </summary>
    public int RequestTimeoutSeconds { get; set; } = 30;
}

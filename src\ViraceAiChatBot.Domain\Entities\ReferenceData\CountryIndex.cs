namespace ViraceAiChatBot.Domain.Entities.ReferenceData;

public class CountryIndex
{
    public Guid Id { get; init; }
    public string? Iso { get; init; }
    public string? Iso3 { get; init; }
    public string? Name { get; init; }

    // Parameterless constructor for EF Core
    private CountryIndex() { }

    public CountryIndex(string iso, string iso3, string name)
    {
        Iso = iso ?? throw new ArgumentNullException(nameof(iso));
        Iso3 = iso3 ?? throw new ArgumentNullException(nameof(iso3));
        Name = name ?? throw new ArgumentNullException(nameof(name));

        ValidateInvariants();
    }

    private void ValidateInvariants()
    {
        if (string.IsNullOrWhiteSpace(Iso) || Iso.Length != 2)
            throw new ArgumentException("ISO code must be exactly 2 characters", nameof(Iso));

        if (string.IsNullOrWhiteSpace(Iso3) || Iso3.Length != 3)
            throw new ArgumentException("ISO3 code must be exactly 3 characters", nameof(Iso3));

        if (string.IsNullOrWhiteSpace(Name))
            throw new ArgumentException("Name cannot be empty", nameof(Name));

        if (Name.Length > 100)
            throw new ArgumentException("Name cannot exceed 100 characters", nameof(Name));
    }

    public string GetSearchableContent()
    {
        return $"{Name} {Iso} {Iso3}";
    }

    public override string ToString()
    {
        return $"Country [ISO: {Iso}, ISO3: {Iso3}, Name: {Name}]";
    }
}

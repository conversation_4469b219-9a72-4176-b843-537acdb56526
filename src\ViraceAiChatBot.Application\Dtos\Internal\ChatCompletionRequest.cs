namespace ViraceAiChatBot.Application.DTOs.Internal;

/// <summary>
/// Represents a request for generating a chat completion.
/// </summary>
/// <param name="Provider">The name of the provider used for the chat completion.</param>
/// <param name="Model">The model used for generating the chat completion.</param>
/// <param name="Messages">The list of chat messages involved in the conversation.</param>
/// <param name="MaxTokens">The optional maximum number of tokens allowed in the response.</param>
/// <param name="Tools">The optional list of tools available for the chat completion.</param>
/// <param name="Temperature">
/// The optional temperature value for response generation, controlling randomness.
/// </param>
public record ChatCompletionRequest(
    string Provider,
    string Model,
    List<ChatMessage> Messages,
    int? MaxTokens = null,
    List<Tool>? Tools = null,
    double? Temperature = null
);

using Microsoft.AspNetCore.Mvc;
using MediatR;
using ViraceAiChatBot.Application.Commands;
using ViraceAiChatBot.Application.Queries;
using ViraceAiChatBot.Application.Dtos;

namespace ViraceAiChatBot.Presentation.Endpoints.Chat;

/// <summary>
/// Endpoint xử lý chat sessions
/// </summary>
public static class SessionsEndpoint
{
    /// <summary>
    /// Map Chat Sessions endpoints
    /// </summary>
    /// <param name="group">Route group</param>
    public static void MapSessionsEndpoint(this RouteGroupBuilder group)
    {

        group.MapGet("/sessions/{sessionId}/messages", async (
                [FromRoute] Guid sessionId,
                [FromQuery] int pageSize,
                [FromQuery] int pageNumber,
                IMediator mediator,
                CancellationToken cancellationToken) =>
            {
                try
                {
                    var query = new GetChatMessagesQuery(sessionId, pageSize == 0 ? 50 : pageSize,
                        pageNumber == 0 ? 1 : pageNumber);
                    var result = await mediator.Send(query, cancellationToken);

                    if (result.SessionTitle == "Session not found" || result.SessionTitle == "Access denied")
                    {
                        return Results.NotFound(new { error = result.SessionTitle });
                    }

                    return Results.Ok(result);
                }
                catch (Exception ex)
                {
                    return Results.BadRequest(new { error = ex.Message });
                }
            })
            .WithName("GetChatMessages")
            .WithSummary("Get messages from a specific chat session with pagination")
            .Produces<GetChatMessagesResponse>(200)
            .Produces<object>(400)
            .Produces<object>(404);

        // Delete session
        group.MapDelete("/sessions/{sessionId}", async (
                [FromRoute] Guid sessionId,
                [FromQuery] string userId,
                IMediator mediator,
                CancellationToken cancellationToken) =>
            {
                try
                {
                    var command = new DeleteChatSessionCommand(sessionId, userId);
                    var result = await mediator.Send(command, cancellationToken);

                    if (!result.Success)
                    {
                        if (result.Message.Contains("not found"))
                        {
                            return Results.NotFound(new { error = result.Message });
                        }

                        if (result.Message.Contains("permission"))
                        {
                            return Results.Forbid();
                        }

                        return Results.BadRequest(new { error = result.Message });
                    }

                    return Results.Ok(result);
                }
                catch (Exception ex)
                {
                    return Results.BadRequest(new { error = ex.Message });
                }
            })
            .WithName("DeleteChatSession")
            .WithSummary("Delete (archive) a specific chat session")
            .Produces<ViraceAiChatBot.Application.Response.DeleteChatSessionResponse>(200)
            .Produces<object>(400)
            .Produces<object>(403)
            .Produces<object>(404);
    }
}

namespace ViraceAiChatBot.Domain.Entities.ReferenceData;

public class VSICIndex
{
    public string? Code { get; init; }
    public string? Name { get; init; }
    public string? EnName { get; init; }

    // Parameterless constructor for EF Core
    private VSICIndex() { }

    public VSICIndex(string code, string name, string enName)
    {
        Code = code ?? throw new ArgumentNullException(nameof(code));
        Name = name ?? throw new ArgumentNullException(nameof(name));
        EnName = enName ?? throw new ArgumentNullException(nameof(enName));

        ValidateInvariants();
    }

    private void ValidateInvariants()
    {
        if (string.IsNullOrWhiteSpace(Code))
            throw new ArgumentException("Code cannot be empty", nameof(Code));

        if (Code.Length > 8)
            throw new ArgumentException("Code cannot exceed 8 characters", nameof(Code));

        if (string.IsNullOrWhiteSpace(Name))
            throw new ArgumentException("Name cannot be empty", nameof(Name));

        if (Name.Length > 500)
            throw new ArgumentException("Name cannot exceed 500 characters", nameof(Name));

        if (string.IsNullOrWhiteSpace(EnName))
            throw new ArgumentException("EnName cannot be empty", nameof(EnName));

        if (EnName.Length > 500)
            throw new ArgumentException("EnName cannot exceed 500 characters", nameof(EnName));
    }

    public string GetSearchableContent()
    {
        return $"{Code} {Name} {EnName}";
    }

    public override string ToString()
    {
        return $"VSICIndex [Code: {Code}, Name: {Name}, EnName: {EnName}]";
    }
}

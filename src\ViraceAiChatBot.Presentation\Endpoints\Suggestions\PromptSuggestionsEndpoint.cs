using Microsoft.AspNetCore.Mvc;
using MediatR;
using ViraceAiChatBot.Application.Commands;
using ViraceAiChatBot.Application.Dtos;

namespace ViraceAiChatBot.Presentation.Endpoints.Suggestions;

/// <summary>
/// Endpoint xử lý gợi ý prompt
/// </summary>
public static class PromptSuggestionsEndpoint
{
    /// <summary>
    /// Map Prompt Suggestions endpoint
    /// </summary>
    /// <param name="group">Route group</param>
    public static void MapPromptSuggestionsEndpoint(this RouteGroupBuilder group)
    {
        group.MapPost("/suggestions/prompts", async (
                [FromBody] PromptSuggestionRequest request,
                IMediator mediator,
                CancellationToken cancellationToken) =>
            {
                try
                {
                    var command = new GetPromptSuggestionsCommand(
                        request.UserId,
                        request.SessionId,
                        request.CurrentInput,
                        request.Domain,
                        request.MaxSuggestions);

                    var result = await mediator.Send(command, cancellationToken);
                    return Results.Ok(result);
                }
                catch (Exception ex)
                {
                    return Results.BadRequest(new { error = ex.Message });
                }
            })
            .WithName("GetPromptSuggestions")
            .WithSummary("Get prompt suggestions when user input needs clarification")
            .Produces<PromptSuggestionResponse>(200)
            .Produces<object>(400);
    }
}

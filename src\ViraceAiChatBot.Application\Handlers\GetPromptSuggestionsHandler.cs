using MediatR;
using Microsoft.Extensions.Logging;
using ViraceAiChatBot.Application.Commands;
using ViraceAiChatBot.Application.Dtos;
using ViraceAiChatBot.Application.Interfaces;

namespace ViraceAiChatBot.Application.Handlers;

public class GetPromptSuggestionsHandler(
    SuggestionService suggestionService,
    ILogger<GetPromptSuggestionsHandler> logger) : IRequestHandler<GetPromptSuggestionsCommand, PromptSuggestionResponse>
{
    public async Task<PromptSuggestionResponse> Handle(GetPromptSuggestionsCommand request, CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Processing prompt suggestions for user {UserId}, input: {Input}",
                request.UserId, request.CurrentInput);

            var response = await suggestionService.GetPromptSuggestionsAsync(
                request.CurrentInput,
                request.MaxSuggestions,
                cancellationToken);

            logger.LogInformation("Generated {Count} prompt suggestions for user {UserId}",
                response.Suggestions.Count, request.UserId);

            return response;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing prompt suggestions for user {UserId}", request.UserId);

            return new PromptSuggestionResponse(
                [],
                "Error occurred while generating suggestions",
                false,
                TimeSpan.Zero
            );
        }
    }
}
